2025-07-29 20:05:24,089 - INFO - ========== 字幕 #6 处理开始 ==========
2025-07-29 20:05:24,089 - INFO - 字幕内容: 就在此刻，城中最高档的酒店里，女人正和富二代新欢举办订婚宴，男人突然闯入，打破了这片虚假的和谐。
2025-07-29 20:05:24,089 - INFO - 字幕序号: [152, 154]
2025-07-29 20:05:24,089 - INFO - 音频文件详情:
2025-07-29 20:05:24,089 - INFO -   - 路径: output\6.wav
2025-07-29 20:05:24,089 - INFO -   - 时长: 6.79秒
2025-07-29 20:05:24,089 - INFO -   - 验证音频时长: 6.79秒
2025-07-29 20:05:24,089 - INFO - 字幕时间戳信息:
2025-07-29 20:05:24,089 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:24,090 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:24,090 - INFO -   - 根据生成的音频时长(6.79秒)已调整字幕时间戳
2025-07-29 20:05:24,090 - INFO - ========== 新模式：为字幕 #6 生成4套场景方案 ==========
2025-07-29 20:05:24,090 - INFO - 字幕序号列表: [152, 154]
2025-07-29 20:05:24,090 - INFO - 
--- 生成方案 #1：基于字幕序号 #152 ---
2025-07-29 20:05:24,090 - INFO - 开始为单个字幕序号 #152 匹配场景，目标时长: 6.79秒
2025-07-29 20:05:24,090 - INFO - 开始查找字幕序号 [152] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:24,090 - INFO - 找到related_overlap场景: scene_id=173, 字幕#152
2025-07-29 20:05:24,090 - INFO - 找到related_between场景: scene_id=174, 字幕#152
2025-07-29 20:05:24,090 - INFO - 找到related_between场景: scene_id=175, 字幕#152
2025-07-29 20:05:24,091 - INFO - 字幕 #152 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:24,091 - INFO - 字幕序号 #152 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:05:24,091 - INFO - 选择第一个overlap场景作为起点: scene_id=173
2025-07-29 20:05:24,091 - INFO - 添加起点场景: scene_id=173, 时长=2.64秒, 累计时长=2.64秒
2025-07-29 20:05:24,091 - INFO - 起点场景时长不足，需要延伸填充 4.15秒
2025-07-29 20:05:24,091 - INFO - 起点场景在原始列表中的索引: 172
2025-07-29 20:05:24,091 - INFO - 延伸添加场景: scene_id=174 (完整时长 1.28秒)
2025-07-29 20:05:24,091 - INFO - 累计时长: 3.92秒
2025-07-29 20:05:24,091 - INFO - 延伸添加场景: scene_id=175 (完整时长 1.56秒)
2025-07-29 20:05:24,091 - INFO - 累计时长: 5.48秒
2025-07-29 20:05:24,091 - INFO - 延伸添加场景: scene_id=176 (完整时长 0.80秒)
2025-07-29 20:05:24,091 - INFO - 累计时长: 6.28秒
2025-07-29 20:05:24,091 - INFO - 延伸添加场景: scene_id=177 (裁剪至 0.51秒)
2025-07-29 20:05:24,091 - INFO - 累计时长: 6.79秒
2025-07-29 20:05:24,091 - INFO - 字幕序号 #152 场景匹配完成，共选择 5 个场景，总时长: 6.79秒
2025-07-29 20:05:24,091 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:24,091 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:24,091 - INFO - 
--- 生成方案 #2：基于字幕序号 #154 ---
2025-07-29 20:05:24,091 - INFO - 开始为单个字幕序号 #154 匹配场景，目标时长: 6.79秒
2025-07-29 20:05:24,091 - INFO - 开始查找字幕序号 [154] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:24,091 - INFO - 找到related_overlap场景: scene_id=176, 字幕#154
2025-07-29 20:05:24,091 - INFO - 找到related_overlap场景: scene_id=177, 字幕#154
2025-07-29 20:05:24,092 - INFO - 字幕 #154 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:24,093 - INFO - 字幕序号 #154 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:24,093 - ERROR - 字幕序号 #154 没有找到任何可用的匹配场景
2025-07-29 20:05:24,093 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:05:24,093 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:05:24,093 - INFO - ========== 当前模式：为字幕 #6 生成 1 套场景方案 ==========
2025-07-29 20:05:24,093 - INFO - 开始查找字幕序号 [152, 154] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:24,093 - INFO - 找到related_overlap场景: scene_id=173, 字幕#152
2025-07-29 20:05:24,093 - INFO - 找到related_overlap场景: scene_id=176, 字幕#154
2025-07-29 20:05:24,093 - INFO - 找到related_overlap场景: scene_id=177, 字幕#154
2025-07-29 20:05:24,093 - INFO - 找到related_between场景: scene_id=174, 字幕#152
2025-07-29 20:05:24,093 - INFO - 找到related_between场景: scene_id=175, 字幕#152
2025-07-29 20:05:24,094 - INFO - 字幕 #152 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:24,094 - INFO - 字幕 #154 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:24,094 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 20:05:24,094 - INFO - 开始生成方案 #1
2025-07-29 20:05:24,094 - INFO - 方案 #1: 为字幕#152选择初始化overlap场景id=173
2025-07-29 20:05:24,094 - INFO - 方案 #1: 为字幕#154选择初始化overlap场景id=177
2025-07-29 20:05:24,094 - INFO - 方案 #1: 初始选择后，当前总时长=4.08秒
2025-07-29 20:05:24,094 - INFO - 方案 #1: 额外添加overlap场景id=176, 当前总时长=4.88秒
2025-07-29 20:05:24,094 - INFO - 方案 #1: 额外between选择后，当前总时长=4.88秒
2025-07-29 20:05:24,094 - INFO - 方案 #1: 额外添加between场景id=175, 当前总时长=6.44秒
2025-07-29 20:05:24,094 - INFO - 方案 #1: 额外添加between场景id=174, 当前总时长=7.72秒
2025-07-29 20:05:24,094 - INFO - 方案 #1: 场景总时长(7.72秒)大于音频时长(6.79秒)，需要裁剪
2025-07-29 20:05:24,094 - INFO - 调整前总时长: 7.72秒, 目标时长: 6.79秒
2025-07-29 20:05:24,094 - INFO - 需要裁剪 0.93秒
2025-07-29 20:05:24,094 - INFO - 裁剪最长场景ID=173：从2.64秒裁剪至1.71秒
2025-07-29 20:05:24,094 - INFO - 调整后总时长: 6.79秒，与目标时长差异: 0.00秒
2025-07-29 20:05:24,094 - INFO - 方案 #1 调整/填充后最终总时长: 6.79秒
2025-07-29 20:05:24,094 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:24,094 - INFO - ========== 当前模式：字幕 #6 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:24,094 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:05:24,094 - INFO - ========== 新模式：字幕 #6 共生成 2 套有效场景方案 ==========
2025-07-29 20:05:24,094 - INFO - 
----- 处理字幕 #6 的方案 #1 -----
2025-07-29 20:05:24,094 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 20:05:24,095 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbnto6bqs
2025-07-29 20:05:24,095 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\173.mp4 (确认存在: True)
2025-07-29 20:05:24,095 - INFO - 添加场景ID=173，时长=2.64秒，累计时长=2.64秒
2025-07-29 20:05:24,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\174.mp4 (确认存在: True)
2025-07-29 20:05:24,096 - INFO - 添加场景ID=174，时长=1.28秒，累计时长=3.92秒
2025-07-29 20:05:24,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\175.mp4 (确认存在: True)
2025-07-29 20:05:24,096 - INFO - 添加场景ID=175，时长=1.56秒，累计时长=5.48秒
2025-07-29 20:05:24,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\176.mp4 (确认存在: True)
2025-07-29 20:05:24,096 - INFO - 添加场景ID=176，时长=0.80秒，累计时长=6.28秒
2025-07-29 20:05:24,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\177.mp4 (确认存在: True)
2025-07-29 20:05:24,096 - INFO - 添加场景ID=177，时长=1.44秒，累计时长=7.72秒
2025-07-29 20:05:24,096 - INFO - 准备合并 5 个场景文件，总时长约 7.72秒
2025-07-29 20:05:24,096 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/173.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/174.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/175.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/176.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/177.mp4'

2025-07-29 20:05:24,096 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbnto6bqs\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbnto6bqs\temp_combined.mp4
2025-07-29 20:05:24,241 - INFO - 合并后的视频时长: 7.84秒，目标音频时长: 6.79秒
2025-07-29 20:05:24,241 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbnto6bqs\temp_combined.mp4 -ss 0 -to 6.792 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 20:05:24,610 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:24,610 - INFO - 目标音频时长: 6.79秒
2025-07-29 20:05:24,611 - INFO - 实际视频时长: 6.82秒
2025-07-29 20:05:24,611 - INFO - 时长差异: 0.03秒 (0.46%)
2025-07-29 20:05:24,611 - INFO - ==========================================
2025-07-29 20:05:24,611 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:24,611 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 20:05:24,611 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbnto6bqs
2025-07-29 20:05:24,655 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:24,655 - INFO -   - 音频时长: 6.79秒
2025-07-29 20:05:24,655 - INFO -   - 视频时长: 6.82秒
2025-07-29 20:05:24,655 - INFO -   - 时长差异: 0.03秒 (0.46%)
2025-07-29 20:05:24,655 - INFO - 
----- 处理字幕 #6 的方案 #2 -----
2025-07-29 20:05:24,655 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 20:05:24,655 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp14mubosm
2025-07-29 20:05:24,656 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\173.mp4 (确认存在: True)
2025-07-29 20:05:24,656 - INFO - 添加场景ID=173，时长=2.64秒，累计时长=2.64秒
2025-07-29 20:05:24,656 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\177.mp4 (确认存在: True)
2025-07-29 20:05:24,656 - INFO - 添加场景ID=177，时长=1.44秒，累计时长=4.08秒
2025-07-29 20:05:24,656 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\176.mp4 (确认存在: True)
2025-07-29 20:05:24,656 - INFO - 添加场景ID=176，时长=0.80秒，累计时长=4.88秒
2025-07-29 20:05:24,656 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\175.mp4 (确认存在: True)
2025-07-29 20:05:24,656 - INFO - 添加场景ID=175，时长=1.56秒，累计时长=6.44秒
2025-07-29 20:05:24,656 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\174.mp4 (确认存在: True)
2025-07-29 20:05:24,656 - INFO - 添加场景ID=174，时长=1.28秒，累计时长=7.72秒
2025-07-29 20:05:24,656 - INFO - 准备合并 5 个场景文件，总时长约 7.72秒
2025-07-29 20:05:24,656 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/173.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/177.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/176.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/175.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/174.mp4'

2025-07-29 20:05:24,656 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp14mubosm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp14mubosm\temp_combined.mp4
2025-07-29 20:05:24,787 - INFO - 合并后的视频时长: 7.84秒，目标音频时长: 6.79秒
2025-07-29 20:05:24,787 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp14mubosm\temp_combined.mp4 -ss 0 -to 6.792 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 20:05:25,149 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:25,149 - INFO - 目标音频时长: 6.79秒
2025-07-29 20:05:25,149 - INFO - 实际视频时长: 6.82秒
2025-07-29 20:05:25,149 - INFO - 时长差异: 0.03秒 (0.46%)
2025-07-29 20:05:25,149 - INFO - ==========================================
2025-07-29 20:05:25,149 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:25,149 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 20:05:25,149 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp14mubosm
2025-07-29 20:05:25,197 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:25,197 - INFO -   - 音频时长: 6.79秒
2025-07-29 20:05:25,197 - INFO -   - 视频时长: 6.82秒
2025-07-29 20:05:25,197 - INFO -   - 时长差异: 0.03秒 (0.46%)
2025-07-29 20:05:25,197 - INFO - 
字幕 #6 处理完成，成功生成 2/2 套方案
2025-07-29 20:05:25,197 - INFO - 生成的视频文件:
2025-07-29 20:05:25,197 - INFO -   1. F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 20:05:25,197 - INFO -   2. F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 20:05:25,197 - INFO - ========== 字幕 #6 处理结束 ==========

