2025-07-29 20:05:48,254 - INFO - ========== 字幕 #23 处理开始 ==========
2025-07-29 20:05:48,254 - INFO - 字幕内容: 女人瞬间炸毛，认为男人小题大做，竟然因为她和别人逢场作戏就要分手，简直是小气至极！
2025-07-29 20:05:48,254 - INFO - 字幕序号: [365, 368]
2025-07-29 20:05:48,254 - INFO - 音频文件详情:
2025-07-29 20:05:48,254 - INFO -   - 路径: output\23.wav
2025-07-29 20:05:48,254 - INFO -   - 时长: 6.51秒
2025-07-29 20:05:48,254 - INFO -   - 验证音频时长: 6.51秒
2025-07-29 20:05:48,254 - INFO - 字幕时间戳信息:
2025-07-29 20:05:48,254 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:48,254 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:48,254 - INFO -   - 根据生成的音频时长(6.51秒)已调整字幕时间戳
2025-07-29 20:05:48,254 - INFO - ========== 新模式：为字幕 #23 生成4套场景方案 ==========
2025-07-29 20:05:48,254 - INFO - 字幕序号列表: [365, 368]
2025-07-29 20:05:48,254 - INFO - 
--- 生成方案 #1：基于字幕序号 #365 ---
2025-07-29 20:05:48,254 - INFO - 开始为单个字幕序号 #365 匹配场景，目标时长: 6.51秒
2025-07-29 20:05:48,255 - INFO - 开始查找字幕序号 [365] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:48,255 - INFO - 找到related_overlap场景: scene_id=430, 字幕#365
2025-07-29 20:05:48,256 - INFO - 字幕 #365 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:48,256 - INFO - 字幕序号 #365 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:48,256 - INFO - 选择第一个overlap场景作为起点: scene_id=430
2025-07-29 20:05:48,256 - INFO - 添加起点场景: scene_id=430, 时长=3.20秒, 累计时长=3.20秒
2025-07-29 20:05:48,256 - INFO - 起点场景时长不足，需要延伸填充 3.31秒
2025-07-29 20:05:48,257 - INFO - 起点场景在原始列表中的索引: 429
2025-07-29 20:05:48,257 - INFO - 延伸添加场景: scene_id=431 (完整时长 1.52秒)
2025-07-29 20:05:48,257 - INFO - 累计时长: 4.72秒
2025-07-29 20:05:48,257 - INFO - 延伸添加场景: scene_id=432 (完整时长 1.72秒)
2025-07-29 20:05:48,257 - INFO - 累计时长: 6.44秒
2025-07-29 20:05:48,257 - INFO - 延伸添加场景: scene_id=433 (裁剪至 0.07秒)
2025-07-29 20:05:48,257 - INFO - 累计时长: 6.51秒
2025-07-29 20:05:48,257 - INFO - 字幕序号 #365 场景匹配完成，共选择 4 个场景，总时长: 6.51秒
2025-07-29 20:05:48,257 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:05:48,257 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:05:48,257 - INFO - 
--- 生成方案 #2：基于字幕序号 #368 ---
2025-07-29 20:05:48,257 - INFO - 开始为单个字幕序号 #368 匹配场景，目标时长: 6.51秒
2025-07-29 20:05:48,257 - INFO - 开始查找字幕序号 [368] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:48,257 - INFO - 找到related_overlap场景: scene_id=432, 字幕#368
2025-07-29 20:05:48,258 - INFO - 字幕 #368 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:48,258 - INFO - 字幕序号 #368 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:48,258 - ERROR - 字幕序号 #368 没有找到任何可用的匹配场景
2025-07-29 20:05:48,258 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:05:48,258 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:05:48,258 - INFO - ========== 当前模式：为字幕 #23 生成 1 套场景方案 ==========
2025-07-29 20:05:48,258 - INFO - 开始查找字幕序号 [365, 368] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:48,258 - INFO - 找到related_overlap场景: scene_id=430, 字幕#365
2025-07-29 20:05:48,258 - INFO - 找到related_overlap场景: scene_id=432, 字幕#368
2025-07-29 20:05:48,259 - INFO - 字幕 #365 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:48,259 - INFO - 字幕 #368 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:48,259 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:48,259 - INFO - 开始生成方案 #1
2025-07-29 20:05:48,259 - INFO - 方案 #1: 为字幕#365选择初始化overlap场景id=430
2025-07-29 20:05:48,259 - INFO - 方案 #1: 为字幕#368选择初始化overlap场景id=432
2025-07-29 20:05:48,259 - INFO - 方案 #1: 初始选择后，当前总时长=4.92秒
2025-07-29 20:05:48,259 - INFO - 方案 #1: 额外between选择后，当前总时长=4.92秒
2025-07-29 20:05:48,259 - INFO - 方案 #1: 场景总时长(4.92秒)小于音频时长(6.51秒)，需要延伸填充
2025-07-29 20:05:48,259 - INFO - 方案 #1: 最后一个场景ID: 432
2025-07-29 20:05:48,260 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 431
2025-07-29 20:05:48,260 - INFO - 方案 #1: 需要填充时长: 1.59秒
2025-07-29 20:05:48,260 - INFO - 方案 #1: 追加场景 scene_id=433 (裁剪至 1.59秒)
2025-07-29 20:05:48,260 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:48,260 - INFO - 方案 #1 调整/填充后最终总时长: 6.51秒
2025-07-29 20:05:48,260 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:48,260 - INFO - ========== 当前模式：字幕 #23 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:48,260 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:05:48,260 - INFO - ========== 新模式：字幕 #23 共生成 2 套有效场景方案 ==========
2025-07-29 20:05:48,260 - INFO - 
----- 处理字幕 #23 的方案 #1 -----
2025-07-29 20:05:48,260 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 20:05:48,260 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp02m73jmm
2025-07-29 20:05:48,261 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\430.mp4 (确认存在: True)
2025-07-29 20:05:48,261 - INFO - 添加场景ID=430，时长=3.20秒，累计时长=3.20秒
2025-07-29 20:05:48,261 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\431.mp4 (确认存在: True)
2025-07-29 20:05:48,261 - INFO - 添加场景ID=431，时长=1.52秒，累计时长=4.72秒
2025-07-29 20:05:48,261 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\432.mp4 (确认存在: True)
2025-07-29 20:05:48,261 - INFO - 添加场景ID=432，时长=1.72秒，累计时长=6.44秒
2025-07-29 20:05:48,261 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\433.mp4 (确认存在: True)
2025-07-29 20:05:48,261 - INFO - 添加场景ID=433，时长=1.88秒，累计时长=8.32秒
2025-07-29 20:05:48,261 - INFO - 准备合并 4 个场景文件，总时长约 8.32秒
2025-07-29 20:05:48,261 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/430.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/431.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/432.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/433.mp4'

2025-07-29 20:05:48,261 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp02m73jmm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp02m73jmm\temp_combined.mp4
2025-07-29 20:05:48,394 - INFO - 合并后的视频时长: 8.41秒，目标音频时长: 6.51秒
2025-07-29 20:05:48,394 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp02m73jmm\temp_combined.mp4 -ss 0 -to 6.506 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 20:05:48,746 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:48,746 - INFO - 目标音频时长: 6.51秒
2025-07-29 20:05:48,746 - INFO - 实际视频时长: 6.54秒
2025-07-29 20:05:48,746 - INFO - 时长差异: 0.04秒 (0.57%)
2025-07-29 20:05:48,746 - INFO - ==========================================
2025-07-29 20:05:48,746 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:48,746 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 20:05:48,747 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp02m73jmm
2025-07-29 20:05:48,790 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:48,790 - INFO -   - 音频时长: 6.51秒
2025-07-29 20:05:48,790 - INFO -   - 视频时长: 6.54秒
2025-07-29 20:05:48,790 - INFO -   - 时长差异: 0.04秒 (0.57%)
2025-07-29 20:05:48,791 - INFO - 
----- 处理字幕 #23 的方案 #2 -----
2025-07-29 20:05:48,791 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 20:05:48,791 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp127jppoh
2025-07-29 20:05:48,791 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\430.mp4 (确认存在: True)
2025-07-29 20:05:48,791 - INFO - 添加场景ID=430，时长=3.20秒，累计时长=3.20秒
2025-07-29 20:05:48,791 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\432.mp4 (确认存在: True)
2025-07-29 20:05:48,791 - INFO - 添加场景ID=432，时长=1.72秒，累计时长=4.92秒
2025-07-29 20:05:48,792 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\433.mp4 (确认存在: True)
2025-07-29 20:05:48,792 - INFO - 添加场景ID=433，时长=1.88秒，累计时长=6.80秒
2025-07-29 20:05:48,792 - INFO - 准备合并 3 个场景文件，总时长约 6.80秒
2025-07-29 20:05:48,792 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/430.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/432.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/433.mp4'

2025-07-29 20:05:48,792 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp127jppoh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp127jppoh\temp_combined.mp4
2025-07-29 20:05:48,917 - INFO - 合并后的视频时长: 6.87秒，目标音频时长: 6.51秒
2025-07-29 20:05:48,917 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp127jppoh\temp_combined.mp4 -ss 0 -to 6.506 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 20:05:49,268 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:49,268 - INFO - 目标音频时长: 6.51秒
2025-07-29 20:05:49,268 - INFO - 实际视频时长: 6.54秒
2025-07-29 20:05:49,268 - INFO - 时长差异: 0.04秒 (0.57%)
2025-07-29 20:05:49,268 - INFO - ==========================================
2025-07-29 20:05:49,268 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:49,268 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 20:05:49,268 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp127jppoh
2025-07-29 20:05:49,312 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:49,312 - INFO -   - 音频时长: 6.51秒
2025-07-29 20:05:49,312 - INFO -   - 视频时长: 6.54秒
2025-07-29 20:05:49,312 - INFO -   - 时长差异: 0.04秒 (0.57%)
2025-07-29 20:05:49,312 - INFO - 
字幕 #23 处理完成，成功生成 2/2 套方案
2025-07-29 20:05:49,312 - INFO - 生成的视频文件:
2025-07-29 20:05:49,312 - INFO -   1. F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 20:05:49,313 - INFO -   2. F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 20:05:49,313 - INFO - ========== 字幕 #23 处理结束 ==========

