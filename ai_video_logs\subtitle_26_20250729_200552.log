2025-07-29 20:05:52,332 - INFO - ========== 字幕 #26 处理开始 ==========
2025-07-29 20:05:52,332 - INFO - 字幕内容: 心死的男人彻底摊牌，既然女人觉得他吃软饭，那他便将这七年的账一笔一笔算清楚！
2025-07-29 20:05:52,332 - INFO - 字幕序号: [485, 489]
2025-07-29 20:05:52,333 - INFO - 音频文件详情:
2025-07-29 20:05:52,333 - INFO -   - 路径: output\26.wav
2025-07-29 20:05:52,333 - INFO -   - 时长: 4.89秒
2025-07-29 20:05:52,333 - INFO -   - 验证音频时长: 4.89秒
2025-07-29 20:05:52,333 - INFO - 字幕时间戳信息:
2025-07-29 20:05:52,342 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:52,342 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:52,342 - INFO -   - 根据生成的音频时长(4.89秒)已调整字幕时间戳
2025-07-29 20:05:52,342 - INFO - ========== 新模式：为字幕 #26 生成4套场景方案 ==========
2025-07-29 20:05:52,342 - INFO - 字幕序号列表: [485, 489]
2025-07-29 20:05:52,342 - INFO - 
--- 生成方案 #1：基于字幕序号 #485 ---
2025-07-29 20:05:52,342 - INFO - 开始为单个字幕序号 #485 匹配场景，目标时长: 4.89秒
2025-07-29 20:05:52,342 - INFO - 开始查找字幕序号 [485] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:52,342 - INFO - 找到related_overlap场景: scene_id=519, 字幕#485
2025-07-29 20:05:52,343 - INFO - 找到related_between场景: scene_id=518, 字幕#485
2025-07-29 20:05:52,343 - INFO - 字幕 #485 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:52,343 - INFO - 字幕序号 #485 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:52,343 - INFO - 选择第一个overlap场景作为起点: scene_id=519
2025-07-29 20:05:52,344 - INFO - 添加起点场景: scene_id=519, 时长=1.04秒, 累计时长=1.04秒
2025-07-29 20:05:52,344 - INFO - 起点场景时长不足，需要延伸填充 3.85秒
2025-07-29 20:05:52,344 - INFO - 起点场景在原始列表中的索引: 518
2025-07-29 20:05:52,344 - INFO - 延伸添加场景: scene_id=520 (完整时长 2.16秒)
2025-07-29 20:05:52,344 - INFO - 累计时长: 3.20秒
2025-07-29 20:05:52,344 - INFO - 延伸添加场景: scene_id=521 (裁剪至 1.69秒)
2025-07-29 20:05:52,344 - INFO - 累计时长: 4.89秒
2025-07-29 20:05:52,344 - INFO - 字幕序号 #485 场景匹配完成，共选择 3 个场景，总时长: 4.89秒
2025-07-29 20:05:52,344 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:52,344 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:52,344 - INFO - 
--- 生成方案 #2：基于字幕序号 #489 ---
2025-07-29 20:05:52,344 - INFO - 开始为单个字幕序号 #489 匹配场景，目标时长: 4.89秒
2025-07-29 20:05:52,344 - INFO - 开始查找字幕序号 [489] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:52,344 - INFO - 找到related_overlap场景: scene_id=521, 字幕#489
2025-07-29 20:05:52,344 - INFO - 找到related_between场景: scene_id=522, 字幕#489
2025-07-29 20:05:52,344 - INFO - 找到related_between场景: scene_id=523, 字幕#489
2025-07-29 20:05:52,344 - INFO - 找到related_between场景: scene_id=524, 字幕#489
2025-07-29 20:05:52,345 - INFO - 字幕 #489 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:05:52,345 - INFO - 字幕序号 #489 找到 0 个可用overlap场景, 3 个可用between场景
2025-07-29 20:05:52,345 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=522
2025-07-29 20:05:52,345 - INFO - 添加起点场景: scene_id=522, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 20:05:52,345 - INFO - 起点场景时长不足，需要延伸填充 3.01秒
2025-07-29 20:05:52,345 - INFO - 起点场景在原始列表中的索引: 521
2025-07-29 20:05:52,345 - INFO - 延伸添加场景: scene_id=523 (完整时长 0.84秒)
2025-07-29 20:05:52,345 - INFO - 累计时长: 2.72秒
2025-07-29 20:05:52,345 - INFO - 延伸添加场景: scene_id=524 (完整时长 1.80秒)
2025-07-29 20:05:52,345 - INFO - 累计时长: 4.52秒
2025-07-29 20:05:52,345 - INFO - 延伸添加场景: scene_id=525 (裁剪至 0.37秒)
2025-07-29 20:05:52,345 - INFO - 累计时长: 4.89秒
2025-07-29 20:05:52,345 - INFO - 字幕序号 #489 场景匹配完成，共选择 4 个场景，总时长: 4.89秒
2025-07-29 20:05:52,345 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:52,345 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:52,345 - INFO - ========== 当前模式：为字幕 #26 生成 1 套场景方案 ==========
2025-07-29 20:05:52,345 - INFO - 开始查找字幕序号 [485, 489] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:52,345 - INFO - 找到related_overlap场景: scene_id=519, 字幕#485
2025-07-29 20:05:52,345 - INFO - 找到related_overlap场景: scene_id=521, 字幕#489
2025-07-29 20:05:52,346 - INFO - 找到related_between场景: scene_id=518, 字幕#485
2025-07-29 20:05:52,346 - INFO - 找到related_between场景: scene_id=522, 字幕#489
2025-07-29 20:05:52,346 - INFO - 找到related_between场景: scene_id=523, 字幕#489
2025-07-29 20:05:52,346 - INFO - 找到related_between场景: scene_id=524, 字幕#489
2025-07-29 20:05:52,346 - INFO - 字幕 #485 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:52,346 - INFO - 字幕 #489 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:05:52,346 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-29 20:05:52,346 - INFO - 开始生成方案 #1
2025-07-29 20:05:52,346 - INFO - 方案 #1: 为字幕#485选择初始化overlap场景id=519
2025-07-29 20:05:52,346 - INFO - 方案 #1: 为字幕#489选择初始化overlap场景id=521
2025-07-29 20:05:52,347 - INFO - 方案 #1: 初始选择后，当前总时长=3.52秒
2025-07-29 20:05:52,347 - INFO - 方案 #1: 额外between选择后，当前总时长=3.52秒
2025-07-29 20:05:52,347 - INFO - 方案 #1: 额外添加between场景id=523, 当前总时长=4.36秒
2025-07-29 20:05:52,347 - INFO - 方案 #1: 额外添加between场景id=518, 当前总时长=5.52秒
2025-07-29 20:05:52,347 - INFO - 方案 #1: 场景总时长(5.52秒)大于音频时长(4.89秒)，需要裁剪
2025-07-29 20:05:52,347 - INFO - 调整前总时长: 5.52秒, 目标时长: 4.89秒
2025-07-29 20:05:52,347 - INFO - 需要裁剪 0.63秒
2025-07-29 20:05:52,347 - INFO - 裁剪最长场景ID=521：从2.48秒裁剪至1.85秒
2025-07-29 20:05:52,347 - INFO - 调整后总时长: 4.89秒，与目标时长差异: 0.00秒
2025-07-29 20:05:52,347 - INFO - 方案 #1 调整/填充后最终总时长: 4.89秒
2025-07-29 20:05:52,347 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:52,347 - INFO - ========== 当前模式：字幕 #26 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:52,347 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:52,347 - INFO - ========== 新模式：字幕 #26 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:52,347 - INFO - 
----- 处理字幕 #26 的方案 #1 -----
2025-07-29 20:05:52,347 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 20:05:52,347 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppgfbi8do
2025-07-29 20:05:52,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\519.mp4 (确认存在: True)
2025-07-29 20:05:52,348 - INFO - 添加场景ID=519，时长=1.04秒，累计时长=1.04秒
2025-07-29 20:05:52,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\520.mp4 (确认存在: True)
2025-07-29 20:05:52,348 - INFO - 添加场景ID=520，时长=2.16秒，累计时长=3.20秒
2025-07-29 20:05:52,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\521.mp4 (确认存在: True)
2025-07-29 20:05:52,348 - INFO - 添加场景ID=521，时长=2.48秒，累计时长=5.68秒
2025-07-29 20:05:52,348 - INFO - 准备合并 3 个场景文件，总时长约 5.68秒
2025-07-29 20:05:52,348 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/519.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/520.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/521.mp4'

2025-07-29 20:05:52,348 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppgfbi8do\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppgfbi8do\temp_combined.mp4
2025-07-29 20:05:52,486 - INFO - 合并后的视频时长: 5.75秒，目标音频时长: 4.89秒
2025-07-29 20:05:52,486 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppgfbi8do\temp_combined.mp4 -ss 0 -to 4.886 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 20:05:52,798 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:52,798 - INFO - 目标音频时长: 4.89秒
2025-07-29 20:05:52,798 - INFO - 实际视频时长: 4.94秒
2025-07-29 20:05:52,798 - INFO - 时长差异: 0.06秒 (1.17%)
2025-07-29 20:05:52,798 - INFO - ==========================================
2025-07-29 20:05:52,798 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:52,798 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 20:05:52,799 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppgfbi8do
2025-07-29 20:05:52,848 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:52,848 - INFO -   - 音频时长: 4.89秒
2025-07-29 20:05:52,848 - INFO -   - 视频时长: 4.94秒
2025-07-29 20:05:52,848 - INFO -   - 时长差异: 0.06秒 (1.17%)
2025-07-29 20:05:52,848 - INFO - 
----- 处理字幕 #26 的方案 #2 -----
2025-07-29 20:05:52,848 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 20:05:52,848 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp__zfuxci
2025-07-29 20:05:52,848 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\522.mp4 (确认存在: True)
2025-07-29 20:05:52,848 - INFO - 添加场景ID=522，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:05:52,848 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\523.mp4 (确认存在: True)
2025-07-29 20:05:52,848 - INFO - 添加场景ID=523，时长=0.84秒，累计时长=2.72秒
2025-07-29 20:05:52,848 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\524.mp4 (确认存在: True)
2025-07-29 20:05:52,848 - INFO - 添加场景ID=524，时长=1.80秒，累计时长=4.52秒
2025-07-29 20:05:52,849 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\525.mp4 (确认存在: True)
2025-07-29 20:05:52,849 - INFO - 添加场景ID=525，时长=1.16秒，累计时长=5.68秒
2025-07-29 20:05:52,849 - INFO - 准备合并 4 个场景文件，总时长约 5.68秒
2025-07-29 20:05:52,849 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/522.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/523.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/524.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/525.mp4'

2025-07-29 20:05:52,849 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp__zfuxci\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp__zfuxci\temp_combined.mp4
2025-07-29 20:05:53,000 - INFO - 合并后的视频时长: 5.77秒，目标音频时长: 4.89秒
2025-07-29 20:05:53,000 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp__zfuxci\temp_combined.mp4 -ss 0 -to 4.886 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 20:05:53,345 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:53,345 - INFO - 目标音频时长: 4.89秒
2025-07-29 20:05:53,345 - INFO - 实际视频时长: 4.94秒
2025-07-29 20:05:53,345 - INFO - 时长差异: 0.06秒 (1.17%)
2025-07-29 20:05:53,345 - INFO - ==========================================
2025-07-29 20:05:53,345 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:53,345 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 20:05:53,347 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp__zfuxci
2025-07-29 20:05:53,390 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:53,390 - INFO -   - 音频时长: 4.89秒
2025-07-29 20:05:53,390 - INFO -   - 视频时长: 4.94秒
2025-07-29 20:05:53,390 - INFO -   - 时长差异: 0.06秒 (1.17%)
2025-07-29 20:05:53,390 - INFO - 
----- 处理字幕 #26 的方案 #3 -----
2025-07-29 20:05:53,390 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 20:05:53,391 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8htucpdg
2025-07-29 20:05:53,391 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\519.mp4 (确认存在: True)
2025-07-29 20:05:53,391 - INFO - 添加场景ID=519，时长=1.04秒，累计时长=1.04秒
2025-07-29 20:05:53,391 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\521.mp4 (确认存在: True)
2025-07-29 20:05:53,391 - INFO - 添加场景ID=521，时长=2.48秒，累计时长=3.52秒
2025-07-29 20:05:53,391 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\523.mp4 (确认存在: True)
2025-07-29 20:05:53,391 - INFO - 添加场景ID=523，时长=0.84秒，累计时长=4.36秒
2025-07-29 20:05:53,391 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\518.mp4 (确认存在: True)
2025-07-29 20:05:53,391 - INFO - 添加场景ID=518，时长=1.16秒，累计时长=5.52秒
2025-07-29 20:05:53,391 - INFO - 准备合并 4 个场景文件，总时长约 5.52秒
2025-07-29 20:05:53,391 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/519.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/521.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/523.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/518.mp4'

2025-07-29 20:05:53,391 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8htucpdg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8htucpdg\temp_combined.mp4
2025-07-29 20:05:53,522 - INFO - 合并后的视频时长: 5.61秒，目标音频时长: 4.89秒
2025-07-29 20:05:53,522 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8htucpdg\temp_combined.mp4 -ss 0 -to 4.886 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 20:05:53,849 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:53,849 - INFO - 目标音频时长: 4.89秒
2025-07-29 20:05:53,849 - INFO - 实际视频时长: 4.94秒
2025-07-29 20:05:53,849 - INFO - 时长差异: 0.06秒 (1.17%)
2025-07-29 20:05:53,849 - INFO - ==========================================
2025-07-29 20:05:53,849 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:53,849 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 20:05:53,851 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8htucpdg
2025-07-29 20:05:53,895 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:53,895 - INFO -   - 音频时长: 4.89秒
2025-07-29 20:05:53,895 - INFO -   - 视频时长: 4.94秒
2025-07-29 20:05:53,895 - INFO -   - 时长差异: 0.06秒 (1.17%)
2025-07-29 20:05:53,896 - INFO - 
字幕 #26 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:53,896 - INFO - 生成的视频文件:
2025-07-29 20:05:53,896 - INFO -   1. F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 20:05:53,896 - INFO -   2. F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 20:05:53,896 - INFO -   3. F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 20:05:53,896 - INFO - ========== 字幕 #26 处理结束 ==========

