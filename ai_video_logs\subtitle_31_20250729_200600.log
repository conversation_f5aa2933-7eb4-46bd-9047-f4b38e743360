2025-07-29 20:06:00,393 - INFO - ========== 字幕 #31 处理开始 ==========
2025-07-29 20:06:00,393 - INFO - 字幕内容: 男人笑了，笑得无比讽刺。新欢回国，女人便送百万豪车，买成千上万的西服，甚至为他包下整栋大楼。
2025-07-29 20:06:00,393 - INFO - 字幕序号: [542, 551]
2025-07-29 20:06:00,393 - INFO - 音频文件详情:
2025-07-29 20:06:00,393 - INFO -   - 路径: output\31.wav
2025-07-29 20:06:00,393 - INFO -   - 时长: 7.01秒
2025-07-29 20:06:00,393 - INFO -   - 验证音频时长: 7.01秒
2025-07-29 20:06:00,394 - INFO - 字幕时间戳信息:
2025-07-29 20:06:00,394 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:00,394 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:00,394 - INFO -   - 根据生成的音频时长(7.01秒)已调整字幕时间戳
2025-07-29 20:06:00,394 - INFO - ========== 新模式：为字幕 #31 生成4套场景方案 ==========
2025-07-29 20:06:00,394 - INFO - 字幕序号列表: [542, 551]
2025-07-29 20:06:00,394 - INFO - 
--- 生成方案 #1：基于字幕序号 #542 ---
2025-07-29 20:06:00,394 - INFO - 开始为单个字幕序号 #542 匹配场景，目标时长: 7.01秒
2025-07-29 20:06:00,394 - INFO - 开始查找字幕序号 [542] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:00,394 - INFO - 找到related_overlap场景: scene_id=573, 字幕#542
2025-07-29 20:06:00,394 - INFO - 找到related_overlap场景: scene_id=574, 字幕#542
2025-07-29 20:06:00,395 - INFO - 字幕 #542 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:00,395 - INFO - 字幕序号 #542 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:00,395 - INFO - 选择第一个overlap场景作为起点: scene_id=573
2025-07-29 20:06:00,395 - INFO - 添加起点场景: scene_id=573, 时长=1.00秒, 累计时长=1.00秒
2025-07-29 20:06:00,395 - INFO - 起点场景时长不足，需要延伸填充 6.01秒
2025-07-29 20:06:00,395 - INFO - 起点场景在原始列表中的索引: 572
2025-07-29 20:06:00,395 - INFO - 延伸添加场景: scene_id=574 (完整时长 2.36秒)
2025-07-29 20:06:00,395 - INFO - 累计时长: 3.36秒
2025-07-29 20:06:00,395 - INFO - 延伸添加场景: scene_id=575 (裁剪至 3.65秒)
2025-07-29 20:06:00,395 - INFO - 累计时长: 7.01秒
2025-07-29 20:06:00,395 - INFO - 字幕序号 #542 场景匹配完成，共选择 3 个场景，总时长: 7.01秒
2025-07-29 20:06:00,395 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:00,395 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:00,395 - INFO - 
--- 生成方案 #2：基于字幕序号 #551 ---
2025-07-29 20:06:00,395 - INFO - 开始为单个字幕序号 #551 匹配场景，目标时长: 7.01秒
2025-07-29 20:06:00,395 - INFO - 开始查找字幕序号 [551] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:00,396 - INFO - 找到related_overlap场景: scene_id=578, 字幕#551
2025-07-29 20:06:00,397 - INFO - 字幕 #551 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:00,397 - INFO - 字幕序号 #551 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:00,397 - INFO - 选择第一个overlap场景作为起点: scene_id=578
2025-07-29 20:06:00,397 - INFO - 添加起点场景: scene_id=578, 时长=2.84秒, 累计时长=2.84秒
2025-07-29 20:06:00,397 - INFO - 起点场景时长不足，需要延伸填充 4.17秒
2025-07-29 20:06:00,397 - INFO - 起点场景在原始列表中的索引: 577
2025-07-29 20:06:00,397 - INFO - 延伸添加场景: scene_id=579 (完整时长 1.48秒)
2025-07-29 20:06:00,397 - INFO - 累计时长: 4.32秒
2025-07-29 20:06:00,397 - INFO - 延伸添加场景: scene_id=580 (完整时长 1.24秒)
2025-07-29 20:06:00,397 - INFO - 累计时长: 5.56秒
2025-07-29 20:06:00,397 - INFO - 延伸添加场景: scene_id=581 (裁剪至 1.45秒)
2025-07-29 20:06:00,397 - INFO - 累计时长: 7.01秒
2025-07-29 20:06:00,397 - INFO - 字幕序号 #551 场景匹配完成，共选择 4 个场景，总时长: 7.01秒
2025-07-29 20:06:00,398 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:00,398 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:00,398 - INFO - ========== 当前模式：为字幕 #31 生成 1 套场景方案 ==========
2025-07-29 20:06:00,398 - INFO - 开始查找字幕序号 [542, 551] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:00,398 - INFO - 找到related_overlap场景: scene_id=573, 字幕#542
2025-07-29 20:06:00,398 - INFO - 找到related_overlap场景: scene_id=574, 字幕#542
2025-07-29 20:06:00,398 - INFO - 找到related_overlap场景: scene_id=578, 字幕#551
2025-07-29 20:06:00,399 - INFO - 字幕 #542 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:00,399 - INFO - 字幕 #551 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:00,399 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:00,399 - INFO - 开始生成方案 #1
2025-07-29 20:06:00,399 - INFO - 方案 #1: 为字幕#542选择初始化overlap场景id=574
2025-07-29 20:06:00,399 - INFO - 方案 #1: 为字幕#551选择初始化overlap场景id=578
2025-07-29 20:06:00,399 - INFO - 方案 #1: 初始选择后，当前总时长=5.20秒
2025-07-29 20:06:00,399 - INFO - 方案 #1: 额外添加overlap场景id=573, 当前总时长=6.20秒
2025-07-29 20:06:00,399 - INFO - 方案 #1: 额外between选择后，当前总时长=6.20秒
2025-07-29 20:06:00,399 - INFO - 方案 #1: 场景总时长(6.20秒)小于音频时长(7.01秒)，需要延伸填充
2025-07-29 20:06:00,399 - INFO - 方案 #1: 最后一个场景ID: 573
2025-07-29 20:06:00,399 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 572
2025-07-29 20:06:00,399 - INFO - 方案 #1: 需要填充时长: 0.81秒
2025-07-29 20:06:00,399 - INFO - 方案 #1: 跳过已使用的场景: scene_id=574
2025-07-29 20:06:00,399 - INFO - 方案 #1: 追加场景 scene_id=575 (裁剪至 0.81秒)
2025-07-29 20:06:00,399 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:00,399 - INFO - 方案 #1 调整/填充后最终总时长: 7.01秒
2025-07-29 20:06:00,399 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:00,399 - INFO - ========== 当前模式：字幕 #31 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:00,399 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:00,399 - INFO - ========== 新模式：字幕 #31 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:00,399 - INFO - 
----- 处理字幕 #31 的方案 #1 -----
2025-07-29 20:06:00,399 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 20:06:00,407 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2k4jmalo
2025-07-29 20:06:00,407 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\573.mp4 (确认存在: True)
2025-07-29 20:06:00,407 - INFO - 添加场景ID=573，时长=1.00秒，累计时长=1.00秒
2025-07-29 20:06:00,407 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\574.mp4 (确认存在: True)
2025-07-29 20:06:00,407 - INFO - 添加场景ID=574，时长=2.36秒，累计时长=3.36秒
2025-07-29 20:06:00,407 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\575.mp4 (确认存在: True)
2025-07-29 20:06:00,407 - INFO - 添加场景ID=575，时长=5.08秒，累计时长=8.44秒
2025-07-29 20:06:00,407 - INFO - 准备合并 3 个场景文件，总时长约 8.44秒
2025-07-29 20:06:00,407 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/573.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/574.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/575.mp4'

2025-07-29 20:06:00,407 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2k4jmalo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2k4jmalo\temp_combined.mp4
2025-07-29 20:06:00,526 - INFO - 合并后的视频时长: 8.51秒，目标音频时长: 7.01秒
2025-07-29 20:06:00,526 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2k4jmalo\temp_combined.mp4 -ss 0 -to 7.013 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 20:06:00,887 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:00,887 - INFO - 目标音频时长: 7.01秒
2025-07-29 20:06:00,887 - INFO - 实际视频时长: 7.06秒
2025-07-29 20:06:00,887 - INFO - 时长差异: 0.05秒 (0.71%)
2025-07-29 20:06:00,887 - INFO - ==========================================
2025-07-29 20:06:00,887 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:00,887 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 20:06:00,888 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2k4jmalo
2025-07-29 20:06:00,933 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:00,933 - INFO -   - 音频时长: 7.01秒
2025-07-29 20:06:00,933 - INFO -   - 视频时长: 7.06秒
2025-07-29 20:06:00,933 - INFO -   - 时长差异: 0.05秒 (0.71%)
2025-07-29 20:06:00,933 - INFO - 
----- 处理字幕 #31 的方案 #2 -----
2025-07-29 20:06:00,933 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 20:06:00,934 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpovg4foyu
2025-07-29 20:06:00,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\578.mp4 (确认存在: True)
2025-07-29 20:06:00,934 - INFO - 添加场景ID=578，时长=2.84秒，累计时长=2.84秒
2025-07-29 20:06:00,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\579.mp4 (确认存在: True)
2025-07-29 20:06:00,934 - INFO - 添加场景ID=579，时长=1.48秒，累计时长=4.32秒
2025-07-29 20:06:00,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\580.mp4 (确认存在: True)
2025-07-29 20:06:00,934 - INFO - 添加场景ID=580，时长=1.24秒，累计时长=5.56秒
2025-07-29 20:06:00,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\581.mp4 (确认存在: True)
2025-07-29 20:06:00,934 - INFO - 添加场景ID=581，时长=2.08秒，累计时长=7.64秒
2025-07-29 20:06:00,935 - INFO - 准备合并 4 个场景文件，总时长约 7.64秒
2025-07-29 20:06:00,935 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/578.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/579.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/580.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/581.mp4'

2025-07-29 20:06:00,935 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpovg4foyu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpovg4foyu\temp_combined.mp4
2025-07-29 20:06:01,073 - INFO - 合并后的视频时长: 7.73秒，目标音频时长: 7.01秒
2025-07-29 20:06:01,073 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpovg4foyu\temp_combined.mp4 -ss 0 -to 7.013 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 20:06:01,442 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:01,442 - INFO - 目标音频时长: 7.01秒
2025-07-29 20:06:01,442 - INFO - 实际视频时长: 7.06秒
2025-07-29 20:06:01,442 - INFO - 时长差异: 0.05秒 (0.71%)
2025-07-29 20:06:01,442 - INFO - ==========================================
2025-07-29 20:06:01,442 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:01,442 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 20:06:01,443 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpovg4foyu
2025-07-29 20:06:01,494 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:01,494 - INFO -   - 音频时长: 7.01秒
2025-07-29 20:06:01,494 - INFO -   - 视频时长: 7.06秒
2025-07-29 20:06:01,494 - INFO -   - 时长差异: 0.05秒 (0.71%)
2025-07-29 20:06:01,494 - INFO - 
----- 处理字幕 #31 的方案 #3 -----
2025-07-29 20:06:01,494 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 20:06:01,494 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjmdi0j4v
2025-07-29 20:06:01,495 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\574.mp4 (确认存在: True)
2025-07-29 20:06:01,495 - INFO - 添加场景ID=574，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:06:01,495 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\578.mp4 (确认存在: True)
2025-07-29 20:06:01,495 - INFO - 添加场景ID=578，时长=2.84秒，累计时长=5.20秒
2025-07-29 20:06:01,495 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\573.mp4 (确认存在: True)
2025-07-29 20:06:01,495 - INFO - 添加场景ID=573，时长=1.00秒，累计时长=6.20秒
2025-07-29 20:06:01,495 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\575.mp4 (确认存在: True)
2025-07-29 20:06:01,495 - INFO - 添加场景ID=575，时长=5.08秒，累计时长=11.28秒
2025-07-29 20:06:01,495 - INFO - 场景总时长(11.28秒)已达到音频时长(7.01秒)的1.5倍，停止添加场景
2025-07-29 20:06:01,495 - INFO - 准备合并 4 个场景文件，总时长约 11.28秒
2025-07-29 20:06:01,495 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/574.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/578.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/573.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/575.mp4'

2025-07-29 20:06:01,495 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjmdi0j4v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjmdi0j4v\temp_combined.mp4
2025-07-29 20:06:01,622 - INFO - 合并后的视频时长: 11.37秒，目标音频时长: 7.01秒
2025-07-29 20:06:01,622 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjmdi0j4v\temp_combined.mp4 -ss 0 -to 7.013 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 20:06:02,005 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:02,005 - INFO - 目标音频时长: 7.01秒
2025-07-29 20:06:02,005 - INFO - 实际视频时长: 7.06秒
2025-07-29 20:06:02,005 - INFO - 时长差异: 0.05秒 (0.71%)
2025-07-29 20:06:02,006 - INFO - ==========================================
2025-07-29 20:06:02,006 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:02,006 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 20:06:02,006 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjmdi0j4v
2025-07-29 20:06:02,050 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:02,050 - INFO -   - 音频时长: 7.01秒
2025-07-29 20:06:02,050 - INFO -   - 视频时长: 7.06秒
2025-07-29 20:06:02,050 - INFO -   - 时长差异: 0.05秒 (0.71%)
2025-07-29 20:06:02,051 - INFO - 
字幕 #31 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:02,051 - INFO - 生成的视频文件:
2025-07-29 20:06:02,051 - INFO -   1. F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 20:06:02,051 - INFO -   2. F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 20:06:02,051 - INFO -   3. F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 20:06:02,051 - INFO - ========== 字幕 #31 处理结束 ==========

