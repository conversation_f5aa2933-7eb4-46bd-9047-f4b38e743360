2025-07-29 20:06:50,312 - INFO - ========== 字幕 #66 处理开始 ==========
2025-07-29 20:06:50,313 - INFO - 字幕内容: 特助高声宣布，有人未经An先生同意擅自入场，请示如何处置，众人这才恍然大悟，眼前的男人，就是An先生本人！
2025-07-29 20:06:50,313 - INFO - 字幕序号: [2600, 2604]
2025-07-29 20:06:50,313 - INFO - 音频文件详情:
2025-07-29 20:06:50,313 - INFO -   - 路径: output\66.wav
2025-07-29 20:06:50,313 - INFO -   - 时长: 7.67秒
2025-07-29 20:06:50,313 - INFO -   - 验证音频时长: 7.67秒
2025-07-29 20:06:50,313 - INFO - 字幕时间戳信息:
2025-07-29 20:06:50,313 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:50,313 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:50,313 - INFO -   - 根据生成的音频时长(7.67秒)已调整字幕时间戳
2025-07-29 20:06:50,313 - INFO - ========== 新模式：为字幕 #66 生成4套场景方案 ==========
2025-07-29 20:06:50,313 - INFO - 字幕序号列表: [2600, 2604]
2025-07-29 20:06:50,313 - INFO - 
--- 生成方案 #1：基于字幕序号 #2600 ---
2025-07-29 20:06:50,313 - INFO - 开始为单个字幕序号 #2600 匹配场景，目标时长: 7.67秒
2025-07-29 20:06:50,313 - INFO - 开始查找字幕序号 [2600] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:50,314 - INFO - 找到related_overlap场景: scene_id=2441, 字幕#2600
2025-07-29 20:06:50,314 - INFO - 找到related_overlap场景: scene_id=2442, 字幕#2600
2025-07-29 20:06:50,315 - INFO - 字幕 #2600 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:50,315 - INFO - 字幕序号 #2600 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:50,315 - INFO - 选择第一个overlap场景作为起点: scene_id=2441
2025-07-29 20:06:50,315 - INFO - 添加起点场景: scene_id=2441, 时长=3.76秒, 累计时长=3.76秒
2025-07-29 20:06:50,315 - INFO - 起点场景时长不足，需要延伸填充 3.91秒
2025-07-29 20:06:50,315 - INFO - 起点场景在原始列表中的索引: 2440
2025-07-29 20:06:50,315 - INFO - 延伸添加场景: scene_id=2442 (完整时长 0.60秒)
2025-07-29 20:06:50,315 - INFO - 累计时长: 4.36秒
2025-07-29 20:06:50,315 - INFO - 延伸添加场景: scene_id=2443 (完整时长 0.68秒)
2025-07-29 20:06:50,315 - INFO - 累计时长: 5.04秒
2025-07-29 20:06:50,315 - INFO - 延伸添加场景: scene_id=2444 (完整时长 1.24秒)
2025-07-29 20:06:50,315 - INFO - 累计时长: 6.28秒
2025-07-29 20:06:50,315 - INFO - 延伸添加场景: scene_id=2445 (完整时长 0.64秒)
2025-07-29 20:06:50,315 - INFO - 累计时长: 6.92秒
2025-07-29 20:06:50,315 - INFO - 延伸添加场景: scene_id=2446 (裁剪至 0.75秒)
2025-07-29 20:06:50,315 - INFO - 累计时长: 7.67秒
2025-07-29 20:06:50,315 - INFO - 字幕序号 #2600 场景匹配完成，共选择 6 个场景，总时长: 7.67秒
2025-07-29 20:06:50,315 - INFO - 方案 #1 生成成功，包含 6 个场景
2025-07-29 20:06:50,315 - INFO - 新模式：第1套方案的 6 个场景已加入全局已使用集合
2025-07-29 20:06:50,315 - INFO - 
--- 生成方案 #2：基于字幕序号 #2604 ---
2025-07-29 20:06:50,315 - INFO - 开始为单个字幕序号 #2604 匹配场景，目标时长: 7.67秒
2025-07-29 20:06:50,315 - INFO - 开始查找字幕序号 [2604] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:50,316 - INFO - 找到related_overlap场景: scene_id=2447, 字幕#2604
2025-07-29 20:06:50,316 - INFO - 字幕 #2604 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:50,316 - INFO - 字幕序号 #2604 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:50,316 - INFO - 选择第一个overlap场景作为起点: scene_id=2447
2025-07-29 20:06:50,316 - INFO - 添加起点场景: scene_id=2447, 时长=1.44秒, 累计时长=1.44秒
2025-07-29 20:06:50,316 - INFO - 起点场景时长不足，需要延伸填充 6.23秒
2025-07-29 20:06:50,316 - INFO - 起点场景在原始列表中的索引: 2446
2025-07-29 20:06:50,316 - INFO - 延伸添加场景: scene_id=2448 (完整时长 1.16秒)
2025-07-29 20:06:50,316 - INFO - 累计时长: 2.60秒
2025-07-29 20:06:50,316 - INFO - 延伸添加场景: scene_id=2449 (完整时长 1.68秒)
2025-07-29 20:06:50,316 - INFO - 累计时长: 4.28秒
2025-07-29 20:06:50,316 - INFO - 延伸添加场景: scene_id=2450 (完整时长 1.04秒)
2025-07-29 20:06:50,316 - INFO - 累计时长: 5.32秒
2025-07-29 20:06:50,316 - INFO - 延伸添加场景: scene_id=2451 (完整时长 1.88秒)
2025-07-29 20:06:50,316 - INFO - 累计时长: 7.20秒
2025-07-29 20:06:50,316 - INFO - 延伸添加场景: scene_id=2452 (裁剪至 0.47秒)
2025-07-29 20:06:50,316 - INFO - 累计时长: 7.67秒
2025-07-29 20:06:50,316 - INFO - 字幕序号 #2604 场景匹配完成，共选择 6 个场景，总时长: 7.67秒
2025-07-29 20:06:50,316 - INFO - 方案 #2 生成成功，包含 6 个场景
2025-07-29 20:06:50,316 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:50,316 - INFO - ========== 当前模式：为字幕 #66 生成 1 套场景方案 ==========
2025-07-29 20:06:50,316 - INFO - 开始查找字幕序号 [2600, 2604] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:50,318 - INFO - 找到related_overlap场景: scene_id=2441, 字幕#2600
2025-07-29 20:06:50,318 - INFO - 找到related_overlap场景: scene_id=2442, 字幕#2600
2025-07-29 20:06:50,318 - INFO - 找到related_overlap场景: scene_id=2447, 字幕#2604
2025-07-29 20:06:50,318 - INFO - 字幕 #2600 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:50,318 - INFO - 字幕 #2604 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:50,318 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:50,318 - INFO - 开始生成方案 #1
2025-07-29 20:06:50,318 - INFO - 方案 #1: 为字幕#2600选择初始化overlap场景id=2441
2025-07-29 20:06:50,318 - INFO - 方案 #1: 为字幕#2604选择初始化overlap场景id=2447
2025-07-29 20:06:50,318 - INFO - 方案 #1: 初始选择后，当前总时长=5.20秒
2025-07-29 20:06:50,318 - INFO - 方案 #1: 额外添加overlap场景id=2442, 当前总时长=5.80秒
2025-07-29 20:06:50,319 - INFO - 方案 #1: 额外between选择后，当前总时长=5.80秒
2025-07-29 20:06:50,319 - INFO - 方案 #1: 场景总时长(5.80秒)小于音频时长(7.67秒)，需要延伸填充
2025-07-29 20:06:50,319 - INFO - 方案 #1: 最后一个场景ID: 2442
2025-07-29 20:06:50,319 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2441
2025-07-29 20:06:50,319 - INFO - 方案 #1: 需要填充时长: 1.87秒
2025-07-29 20:06:50,319 - INFO - 方案 #1: 追加场景 scene_id=2443 (完整时长 0.68秒)
2025-07-29 20:06:50,319 - INFO - 方案 #1: 追加场景 scene_id=2444 (裁剪至 1.19秒)
2025-07-29 20:06:50,319 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:50,319 - INFO - 方案 #1 调整/填充后最终总时长: 7.67秒
2025-07-29 20:06:50,319 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:50,319 - INFO - ========== 当前模式：字幕 #66 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:50,319 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:50,319 - INFO - ========== 新模式：字幕 #66 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:50,319 - INFO - 
----- 处理字幕 #66 的方案 #1 -----
2025-07-29 20:06:50,319 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 20:06:50,319 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_qkoyia2
2025-07-29 20:06:50,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2441.mp4 (确认存在: True)
2025-07-29 20:06:50,320 - INFO - 添加场景ID=2441，时长=3.76秒，累计时长=3.76秒
2025-07-29 20:06:50,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2442.mp4 (确认存在: True)
2025-07-29 20:06:50,320 - INFO - 添加场景ID=2442，时长=0.60秒，累计时长=4.36秒
2025-07-29 20:06:50,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2443.mp4 (确认存在: True)
2025-07-29 20:06:50,320 - INFO - 添加场景ID=2443，时长=0.68秒，累计时长=5.04秒
2025-07-29 20:06:50,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2444.mp4 (确认存在: True)
2025-07-29 20:06:50,320 - INFO - 添加场景ID=2444，时长=1.24秒，累计时长=6.28秒
2025-07-29 20:06:50,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2445.mp4 (确认存在: True)
2025-07-29 20:06:50,320 - INFO - 添加场景ID=2445，时长=0.64秒，累计时长=6.92秒
2025-07-29 20:06:50,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2446.mp4 (确认存在: True)
2025-07-29 20:06:50,320 - INFO - 添加场景ID=2446，时长=0.84秒，累计时长=7.76秒
2025-07-29 20:06:50,320 - INFO - 准备合并 6 个场景文件，总时长约 7.76秒
2025-07-29 20:06:50,320 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2441.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2442.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2443.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2444.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2445.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2446.mp4'

2025-07-29 20:06:50,321 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_qkoyia2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_qkoyia2\temp_combined.mp4
2025-07-29 20:06:50,498 - INFO - 合并后的视频时长: 7.90秒，目标音频时长: 7.67秒
2025-07-29 20:06:50,499 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_qkoyia2\temp_combined.mp4 -ss 0 -to 7.666 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 20:06:50,888 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:50,888 - INFO - 目标音频时长: 7.67秒
2025-07-29 20:06:50,888 - INFO - 实际视频时长: 7.70秒
2025-07-29 20:06:50,888 - INFO - 时长差异: 0.04秒 (0.48%)
2025-07-29 20:06:50,888 - INFO - ==========================================
2025-07-29 20:06:50,888 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:50,888 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 20:06:50,888 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_qkoyia2
2025-07-29 20:06:50,935 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:50,935 - INFO -   - 音频时长: 7.67秒
2025-07-29 20:06:50,935 - INFO -   - 视频时长: 7.70秒
2025-07-29 20:06:50,935 - INFO -   - 时长差异: 0.04秒 (0.48%)
2025-07-29 20:06:50,935 - INFO - 
----- 处理字幕 #66 的方案 #2 -----
2025-07-29 20:06:50,935 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 20:06:50,935 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0gwi2sjy
2025-07-29 20:06:50,935 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2447.mp4 (确认存在: True)
2025-07-29 20:06:50,935 - INFO - 添加场景ID=2447，时长=1.44秒，累计时长=1.44秒
2025-07-29 20:06:50,937 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2448.mp4 (确认存在: True)
2025-07-29 20:06:50,937 - INFO - 添加场景ID=2448，时长=1.16秒，累计时长=2.60秒
2025-07-29 20:06:50,937 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2449.mp4 (确认存在: True)
2025-07-29 20:06:50,937 - INFO - 添加场景ID=2449，时长=1.68秒，累计时长=4.28秒
2025-07-29 20:06:50,937 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2450.mp4 (确认存在: True)
2025-07-29 20:06:50,937 - INFO - 添加场景ID=2450，时长=1.04秒，累计时长=5.32秒
2025-07-29 20:06:50,937 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2451.mp4 (确认存在: True)
2025-07-29 20:06:50,937 - INFO - 添加场景ID=2451，时长=1.88秒，累计时长=7.20秒
2025-07-29 20:06:50,937 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2452.mp4 (确认存在: True)
2025-07-29 20:06:50,937 - INFO - 添加场景ID=2452，时长=2.28秒，累计时长=9.48秒
2025-07-29 20:06:50,937 - INFO - 准备合并 6 个场景文件，总时长约 9.48秒
2025-07-29 20:06:50,937 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2447.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2448.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2449.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2450.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2451.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2452.mp4'

2025-07-29 20:06:50,937 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0gwi2sjy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0gwi2sjy\temp_combined.mp4
2025-07-29 20:06:51,116 - INFO - 合并后的视频时长: 9.62秒，目标音频时长: 7.67秒
2025-07-29 20:06:51,116 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0gwi2sjy\temp_combined.mp4 -ss 0 -to 7.666 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 20:06:51,522 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:51,522 - INFO - 目标音频时长: 7.67秒
2025-07-29 20:06:51,522 - INFO - 实际视频时长: 7.70秒
2025-07-29 20:06:51,522 - INFO - 时长差异: 0.04秒 (0.48%)
2025-07-29 20:06:51,522 - INFO - ==========================================
2025-07-29 20:06:51,522 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:51,522 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 20:06:51,523 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0gwi2sjy
2025-07-29 20:06:51,567 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:51,567 - INFO -   - 音频时长: 7.67秒
2025-07-29 20:06:51,567 - INFO -   - 视频时长: 7.70秒
2025-07-29 20:06:51,567 - INFO -   - 时长差异: 0.04秒 (0.48%)
2025-07-29 20:06:51,567 - INFO - 
----- 处理字幕 #66 的方案 #3 -----
2025-07-29 20:06:51,567 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 20:06:51,568 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9kzzn2r1
2025-07-29 20:06:51,569 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2441.mp4 (确认存在: True)
2025-07-29 20:06:51,569 - INFO - 添加场景ID=2441，时长=3.76秒，累计时长=3.76秒
2025-07-29 20:06:51,569 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2447.mp4 (确认存在: True)
2025-07-29 20:06:51,569 - INFO - 添加场景ID=2447，时长=1.44秒，累计时长=5.20秒
2025-07-29 20:06:51,569 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2442.mp4 (确认存在: True)
2025-07-29 20:06:51,569 - INFO - 添加场景ID=2442，时长=0.60秒，累计时长=5.80秒
2025-07-29 20:06:51,569 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2443.mp4 (确认存在: True)
2025-07-29 20:06:51,569 - INFO - 添加场景ID=2443，时长=0.68秒，累计时长=6.48秒
2025-07-29 20:06:51,569 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2444.mp4 (确认存在: True)
2025-07-29 20:06:51,569 - INFO - 添加场景ID=2444，时长=1.24秒，累计时长=7.72秒
2025-07-29 20:06:51,569 - INFO - 准备合并 5 个场景文件，总时长约 7.72秒
2025-07-29 20:06:51,569 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2441.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2447.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2442.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2443.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2444.mp4'

2025-07-29 20:06:51,569 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9kzzn2r1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9kzzn2r1\temp_combined.mp4
2025-07-29 20:06:51,729 - INFO - 合并后的视频时长: 7.84秒，目标音频时长: 7.67秒
2025-07-29 20:06:51,729 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9kzzn2r1\temp_combined.mp4 -ss 0 -to 7.666 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 20:06:52,124 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:52,124 - INFO - 目标音频时长: 7.67秒
2025-07-29 20:06:52,124 - INFO - 实际视频时长: 7.70秒
2025-07-29 20:06:52,124 - INFO - 时长差异: 0.04秒 (0.48%)
2025-07-29 20:06:52,124 - INFO - ==========================================
2025-07-29 20:06:52,124 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:52,124 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 20:06:52,125 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9kzzn2r1
2025-07-29 20:06:52,168 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:52,168 - INFO -   - 音频时长: 7.67秒
2025-07-29 20:06:52,169 - INFO -   - 视频时长: 7.70秒
2025-07-29 20:06:52,169 - INFO -   - 时长差异: 0.04秒 (0.48%)
2025-07-29 20:06:52,169 - INFO - 
字幕 #66 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:52,169 - INFO - 生成的视频文件:
2025-07-29 20:06:52,169 - INFO -   1. F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 20:06:52,169 - INFO -   2. F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 20:06:52,169 - INFO -   3. F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 20:06:52,169 - INFO - ========== 字幕 #66 处理结束 ==========

