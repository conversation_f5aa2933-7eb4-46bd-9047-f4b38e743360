2025-07-29 20:06:46,089 - INFO - ========== 字幕 #63 处理开始 ==========
2025-07-29 20:06:46,089 - INFO - 字幕内容: 她自以为是地表示，愿意护着男人，让他接管许氏，以为这是一种恩赐。
2025-07-29 20:06:46,090 - INFO - 字幕序号: [2523, 2527]
2025-07-29 20:06:46,090 - INFO - 音频文件详情:
2025-07-29 20:06:46,090 - INFO -   - 路径: output\63.wav
2025-07-29 20:06:46,090 - INFO -   - 时长: 4.92秒
2025-07-29 20:06:46,090 - INFO -   - 验证音频时长: 4.92秒
2025-07-29 20:06:46,090 - INFO - 字幕时间戳信息:
2025-07-29 20:06:46,090 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:46,090 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:46,090 - INFO -   - 根据生成的音频时长(4.92秒)已调整字幕时间戳
2025-07-29 20:06:46,090 - INFO - ========== 新模式：为字幕 #63 生成4套场景方案 ==========
2025-07-29 20:06:46,090 - INFO - 字幕序号列表: [2523, 2527]
2025-07-29 20:06:46,090 - INFO - 
--- 生成方案 #1：基于字幕序号 #2523 ---
2025-07-29 20:06:46,090 - INFO - 开始为单个字幕序号 #2523 匹配场景，目标时长: 4.92秒
2025-07-29 20:06:46,090 - INFO - 开始查找字幕序号 [2523] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:46,091 - INFO - 找到related_overlap场景: scene_id=2383, 字幕#2523
2025-07-29 20:06:46,092 - INFO - 字幕 #2523 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:46,092 - INFO - 字幕序号 #2523 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:46,092 - INFO - 选择第一个overlap场景作为起点: scene_id=2383
2025-07-29 20:06:46,092 - INFO - 添加起点场景: scene_id=2383, 时长=2.32秒, 累计时长=2.32秒
2025-07-29 20:06:46,092 - INFO - 起点场景时长不足，需要延伸填充 2.60秒
2025-07-29 20:06:46,092 - INFO - 起点场景在原始列表中的索引: 2382
2025-07-29 20:06:46,092 - INFO - 延伸添加场景: scene_id=2384 (完整时长 1.68秒)
2025-07-29 20:06:46,092 - INFO - 累计时长: 4.00秒
2025-07-29 20:06:46,092 - INFO - 延伸添加场景: scene_id=2385 (裁剪至 0.92秒)
2025-07-29 20:06:46,092 - INFO - 累计时长: 4.92秒
2025-07-29 20:06:46,092 - INFO - 字幕序号 #2523 场景匹配完成，共选择 3 个场景，总时长: 4.92秒
2025-07-29 20:06:46,092 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:46,092 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:46,092 - INFO - 
--- 生成方案 #2：基于字幕序号 #2527 ---
2025-07-29 20:06:46,092 - INFO - 开始为单个字幕序号 #2527 匹配场景，目标时长: 4.92秒
2025-07-29 20:06:46,092 - INFO - 开始查找字幕序号 [2527] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:46,093 - INFO - 找到related_overlap场景: scene_id=2387, 字幕#2527
2025-07-29 20:06:46,093 - INFO - 字幕 #2527 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:46,093 - INFO - 字幕序号 #2527 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:46,093 - INFO - 选择第一个overlap场景作为起点: scene_id=2387
2025-07-29 20:06:46,093 - INFO - 添加起点场景: scene_id=2387, 时长=6.20秒, 累计时长=6.20秒
2025-07-29 20:06:46,093 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:46,093 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 20:06:46,094 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:46,094 - INFO - ========== 当前模式：为字幕 #63 生成 1 套场景方案 ==========
2025-07-29 20:06:46,094 - INFO - 开始查找字幕序号 [2523, 2527] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:46,094 - INFO - 找到related_overlap场景: scene_id=2383, 字幕#2523
2025-07-29 20:06:46,094 - INFO - 找到related_overlap场景: scene_id=2387, 字幕#2527
2025-07-29 20:06:46,095 - INFO - 字幕 #2523 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:46,095 - INFO - 字幕 #2527 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:46,095 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:46,095 - INFO - 开始生成方案 #1
2025-07-29 20:06:46,095 - INFO - 方案 #1: 为字幕#2523选择初始化overlap场景id=2383
2025-07-29 20:06:46,095 - INFO - 方案 #1: 为字幕#2527选择初始化overlap场景id=2387
2025-07-29 20:06:46,095 - INFO - 方案 #1: 初始选择后，当前总时长=8.52秒
2025-07-29 20:06:46,095 - INFO - 方案 #1: 额外between选择后，当前总时长=8.52秒
2025-07-29 20:06:46,095 - INFO - 方案 #1: 场景总时长(8.52秒)大于音频时长(4.92秒)，需要裁剪
2025-07-29 20:06:46,095 - INFO - 调整前总时长: 8.52秒, 目标时长: 4.92秒
2025-07-29 20:06:46,095 - INFO - 需要裁剪 3.60秒
2025-07-29 20:06:46,095 - INFO - 裁剪最长场景ID=2387：从6.20秒裁剪至2.60秒
2025-07-29 20:06:46,095 - INFO - 调整后总时长: 4.92秒，与目标时长差异: 0.00秒
2025-07-29 20:06:46,095 - INFO - 方案 #1 调整/填充后最终总时长: 4.92秒
2025-07-29 20:06:46,095 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:46,095 - INFO - ========== 当前模式：字幕 #63 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:46,095 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:46,095 - INFO - ========== 新模式：字幕 #63 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:46,095 - INFO - 
----- 处理字幕 #63 的方案 #1 -----
2025-07-29 20:06:46,095 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 20:06:46,095 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsz4bb0d7
2025-07-29 20:06:46,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2383.mp4 (确认存在: True)
2025-07-29 20:06:46,096 - INFO - 添加场景ID=2383，时长=2.32秒，累计时长=2.32秒
2025-07-29 20:06:46,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2384.mp4 (确认存在: True)
2025-07-29 20:06:46,096 - INFO - 添加场景ID=2384，时长=1.68秒，累计时长=4.00秒
2025-07-29 20:06:46,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2385.mp4 (确认存在: True)
2025-07-29 20:06:46,096 - INFO - 添加场景ID=2385，时长=1.60秒，累计时长=5.60秒
2025-07-29 20:06:46,096 - INFO - 准备合并 3 个场景文件，总时长约 5.60秒
2025-07-29 20:06:46,096 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2383.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2384.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2385.mp4'

2025-07-29 20:06:46,096 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsz4bb0d7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsz4bb0d7\temp_combined.mp4
2025-07-29 20:06:46,222 - INFO - 合并后的视频时长: 5.67秒，目标音频时长: 4.92秒
2025-07-29 20:06:46,222 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsz4bb0d7\temp_combined.mp4 -ss 0 -to 4.922 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 20:06:46,504 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:46,504 - INFO - 目标音频时长: 4.92秒
2025-07-29 20:06:46,504 - INFO - 实际视频时长: 4.98秒
2025-07-29 20:06:46,504 - INFO - 时长差异: 0.06秒 (1.24%)
2025-07-29 20:06:46,504 - INFO - ==========================================
2025-07-29 20:06:46,504 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:46,504 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 20:06:46,505 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsz4bb0d7
2025-07-29 20:06:46,548 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:46,548 - INFO -   - 音频时长: 4.92秒
2025-07-29 20:06:46,548 - INFO -   - 视频时长: 4.98秒
2025-07-29 20:06:46,548 - INFO -   - 时长差异: 0.06秒 (1.24%)
2025-07-29 20:06:46,549 - INFO - 
----- 处理字幕 #63 的方案 #2 -----
2025-07-29 20:06:46,549 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 20:06:46,549 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdzn5xdbx
2025-07-29 20:06:46,550 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2387.mp4 (确认存在: True)
2025-07-29 20:06:46,550 - INFO - 添加场景ID=2387，时长=6.20秒，累计时长=6.20秒
2025-07-29 20:06:46,550 - INFO - 准备合并 1 个场景文件，总时长约 6.20秒
2025-07-29 20:06:46,550 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2387.mp4'

2025-07-29 20:06:46,550 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdzn5xdbx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdzn5xdbx\temp_combined.mp4
2025-07-29 20:06:46,673 - INFO - 合并后的视频时长: 6.22秒，目标音频时长: 4.92秒
2025-07-29 20:06:46,673 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdzn5xdbx\temp_combined.mp4 -ss 0 -to 4.922 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 20:06:46,973 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:46,973 - INFO - 目标音频时长: 4.92秒
2025-07-29 20:06:46,973 - INFO - 实际视频时长: 4.98秒
2025-07-29 20:06:46,973 - INFO - 时长差异: 0.06秒 (1.24%)
2025-07-29 20:06:46,973 - INFO - ==========================================
2025-07-29 20:06:46,973 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:46,973 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 20:06:46,974 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdzn5xdbx
2025-07-29 20:06:47,017 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:47,017 - INFO -   - 音频时长: 4.92秒
2025-07-29 20:06:47,017 - INFO -   - 视频时长: 4.98秒
2025-07-29 20:06:47,017 - INFO -   - 时长差异: 0.06秒 (1.24%)
2025-07-29 20:06:47,017 - INFO - 
----- 处理字幕 #63 的方案 #3 -----
2025-07-29 20:06:47,017 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 20:06:47,018 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphu08wpwv
2025-07-29 20:06:47,018 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2383.mp4 (确认存在: True)
2025-07-29 20:06:47,018 - INFO - 添加场景ID=2383，时长=2.32秒，累计时长=2.32秒
2025-07-29 20:06:47,018 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2387.mp4 (确认存在: True)
2025-07-29 20:06:47,018 - INFO - 添加场景ID=2387，时长=6.20秒，累计时长=8.52秒
2025-07-29 20:06:47,018 - INFO - 场景总时长(8.52秒)已达到音频时长(4.92秒)的1.5倍，停止添加场景
2025-07-29 20:06:47,018 - INFO - 准备合并 2 个场景文件，总时长约 8.52秒
2025-07-29 20:06:47,018 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2383.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2387.mp4'

2025-07-29 20:06:47,018 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphu08wpwv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphu08wpwv\temp_combined.mp4
2025-07-29 20:06:47,139 - INFO - 合并后的视频时长: 8.57秒，目标音频时长: 4.92秒
2025-07-29 20:06:47,139 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphu08wpwv\temp_combined.mp4 -ss 0 -to 4.922 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 20:06:47,445 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:47,445 - INFO - 目标音频时长: 4.92秒
2025-07-29 20:06:47,446 - INFO - 实际视频时长: 4.98秒
2025-07-29 20:06:47,446 - INFO - 时长差异: 0.06秒 (1.24%)
2025-07-29 20:06:47,446 - INFO - ==========================================
2025-07-29 20:06:47,446 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:47,446 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 20:06:47,446 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphu08wpwv
2025-07-29 20:06:47,489 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:47,489 - INFO -   - 音频时长: 4.92秒
2025-07-29 20:06:47,489 - INFO -   - 视频时长: 4.98秒
2025-07-29 20:06:47,489 - INFO -   - 时长差异: 0.06秒 (1.24%)
2025-07-29 20:06:47,489 - INFO - 
字幕 #63 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:47,490 - INFO - 生成的视频文件:
2025-07-29 20:06:47,490 - INFO -   1. F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 20:06:47,490 - INFO -   2. F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 20:06:47,490 - INFO -   3. F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 20:06:47,490 - INFO - ========== 字幕 #63 处理结束 ==========

