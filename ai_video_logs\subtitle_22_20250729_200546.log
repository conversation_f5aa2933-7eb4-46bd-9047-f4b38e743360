2025-07-29 20:05:46,910 - INFO - ========== 字幕 #22 处理开始 ==========
2025-07-29 20:05:46,910 - INFO - 字幕内容: 男人心如死灰，他看着这个自己爱了七年的女人，冷冷地提出了分手。
2025-07-29 20:05:46,910 - INFO - 字幕序号: [361, 364]
2025-07-29 20:05:46,910 - INFO - 音频文件详情:
2025-07-29 20:05:46,910 - INFO -   - 路径: output\22.wav
2025-07-29 20:05:46,910 - INFO -   - 时长: 4.05秒
2025-07-29 20:05:46,910 - INFO -   - 验证音频时长: 4.05秒
2025-07-29 20:05:46,910 - INFO - 字幕时间戳信息:
2025-07-29 20:05:46,910 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:46,910 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:46,910 - INFO -   - 根据生成的音频时长(4.05秒)已调整字幕时间戳
2025-07-29 20:05:46,910 - INFO - ========== 新模式：为字幕 #22 生成4套场景方案 ==========
2025-07-29 20:05:46,910 - INFO - 字幕序号列表: [361, 364]
2025-07-29 20:05:46,910 - INFO - 
--- 生成方案 #1：基于字幕序号 #361 ---
2025-07-29 20:05:46,910 - INFO - 开始为单个字幕序号 #361 匹配场景，目标时长: 4.05秒
2025-07-29 20:05:46,910 - INFO - 开始查找字幕序号 [361] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:46,911 - INFO - 找到related_overlap场景: scene_id=425, 字幕#361
2025-07-29 20:05:46,912 - INFO - 字幕 #361 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:46,912 - INFO - 字幕序号 #361 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:46,912 - INFO - 选择第一个overlap场景作为起点: scene_id=425
2025-07-29 20:05:46,912 - INFO - 添加起点场景: scene_id=425, 时长=1.16秒, 累计时长=1.16秒
2025-07-29 20:05:46,912 - INFO - 起点场景时长不足，需要延伸填充 2.89秒
2025-07-29 20:05:46,912 - INFO - 起点场景在原始列表中的索引: 424
2025-07-29 20:05:46,912 - INFO - 延伸添加场景: scene_id=426 (完整时长 0.80秒)
2025-07-29 20:05:46,912 - INFO - 累计时长: 1.96秒
2025-07-29 20:05:46,912 - INFO - 延伸添加场景: scene_id=427 (完整时长 1.12秒)
2025-07-29 20:05:46,912 - INFO - 累计时长: 3.08秒
2025-07-29 20:05:46,912 - INFO - 延伸添加场景: scene_id=428 (裁剪至 0.97秒)
2025-07-29 20:05:46,912 - INFO - 累计时长: 4.05秒
2025-07-29 20:05:46,912 - INFO - 字幕序号 #361 场景匹配完成，共选择 4 个场景，总时长: 4.05秒
2025-07-29 20:05:46,912 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:05:46,912 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:05:46,912 - INFO - 
--- 生成方案 #2：基于字幕序号 #364 ---
2025-07-29 20:05:46,912 - INFO - 开始为单个字幕序号 #364 匹配场景，目标时长: 4.05秒
2025-07-29 20:05:46,912 - INFO - 开始查找字幕序号 [364] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:46,912 - INFO - 找到related_overlap场景: scene_id=429, 字幕#364
2025-07-29 20:05:46,913 - INFO - 字幕 #364 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:46,913 - INFO - 字幕序号 #364 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:46,913 - INFO - 选择第一个overlap场景作为起点: scene_id=429
2025-07-29 20:05:46,913 - INFO - 添加起点场景: scene_id=429, 时长=2.80秒, 累计时长=2.80秒
2025-07-29 20:05:46,913 - INFO - 起点场景时长不足，需要延伸填充 1.25秒
2025-07-29 20:05:46,913 - INFO - 起点场景在原始列表中的索引: 428
2025-07-29 20:05:46,913 - INFO - 延伸添加场景: scene_id=430 (裁剪至 1.25秒)
2025-07-29 20:05:46,913 - INFO - 累计时长: 4.05秒
2025-07-29 20:05:46,913 - INFO - 字幕序号 #364 场景匹配完成，共选择 2 个场景，总时长: 4.05秒
2025-07-29 20:05:46,913 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:05:46,913 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:46,913 - INFO - ========== 当前模式：为字幕 #22 生成 1 套场景方案 ==========
2025-07-29 20:05:46,913 - INFO - 开始查找字幕序号 [361, 364] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:46,914 - INFO - 找到related_overlap场景: scene_id=425, 字幕#361
2025-07-29 20:05:46,914 - INFO - 找到related_overlap场景: scene_id=429, 字幕#364
2025-07-29 20:05:46,915 - INFO - 字幕 #361 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:46,915 - INFO - 字幕 #364 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:46,915 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:46,915 - INFO - 开始生成方案 #1
2025-07-29 20:05:46,915 - INFO - 方案 #1: 为字幕#361选择初始化overlap场景id=425
2025-07-29 20:05:46,915 - INFO - 方案 #1: 为字幕#364选择初始化overlap场景id=429
2025-07-29 20:05:46,915 - INFO - 方案 #1: 初始选择后，当前总时长=3.96秒
2025-07-29 20:05:46,915 - INFO - 方案 #1: 额外between选择后，当前总时长=3.96秒
2025-07-29 20:05:46,915 - INFO - 方案 #1: 场景总时长(3.96秒)小于音频时长(4.05秒)，需要延伸填充
2025-07-29 20:05:46,915 - INFO - 方案 #1: 最后一个场景ID: 429
2025-07-29 20:05:46,915 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 428
2025-07-29 20:05:46,915 - INFO - 方案 #1: 需要填充时长: 0.09秒
2025-07-29 20:05:46,915 - INFO - 方案 #1: 追加场景 scene_id=430 (裁剪至 0.09秒)
2025-07-29 20:05:46,915 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:46,915 - INFO - 方案 #1 调整/填充后最终总时长: 4.05秒
2025-07-29 20:05:46,915 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:46,915 - INFO - ========== 当前模式：字幕 #22 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:46,915 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:46,915 - INFO - ========== 新模式：字幕 #22 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:46,915 - INFO - 
----- 处理字幕 #22 的方案 #1 -----
2025-07-29 20:05:46,915 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 20:05:46,916 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqg6403de
2025-07-29 20:05:46,916 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\425.mp4 (确认存在: True)
2025-07-29 20:05:46,916 - INFO - 添加场景ID=425，时长=1.16秒，累计时长=1.16秒
2025-07-29 20:05:46,916 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\426.mp4 (确认存在: True)
2025-07-29 20:05:46,916 - INFO - 添加场景ID=426，时长=0.80秒，累计时长=1.96秒
2025-07-29 20:05:46,916 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\427.mp4 (确认存在: True)
2025-07-29 20:05:46,916 - INFO - 添加场景ID=427，时长=1.12秒，累计时长=3.08秒
2025-07-29 20:05:46,916 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\428.mp4 (确认存在: True)
2025-07-29 20:05:46,917 - INFO - 添加场景ID=428，时长=1.24秒，累计时长=4.32秒
2025-07-29 20:05:46,917 - INFO - 准备合并 4 个场景文件，总时长约 4.32秒
2025-07-29 20:05:46,917 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/425.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/426.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/427.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/428.mp4'

2025-07-29 20:05:46,917 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqg6403de\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqg6403de\temp_combined.mp4
2025-07-29 20:05:47,062 - INFO - 合并后的视频时长: 4.41秒，目标音频时长: 4.05秒
2025-07-29 20:05:47,062 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqg6403de\temp_combined.mp4 -ss 0 -to 4.052 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 20:05:47,340 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:47,340 - INFO - 目标音频时长: 4.05秒
2025-07-29 20:05:47,340 - INFO - 实际视频时长: 4.10秒
2025-07-29 20:05:47,340 - INFO - 时长差异: 0.05秒 (1.26%)
2025-07-29 20:05:47,340 - INFO - ==========================================
2025-07-29 20:05:47,340 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:47,340 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 20:05:47,341 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqg6403de
2025-07-29 20:05:47,383 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:47,383 - INFO -   - 音频时长: 4.05秒
2025-07-29 20:05:47,383 - INFO -   - 视频时长: 4.10秒
2025-07-29 20:05:47,383 - INFO -   - 时长差异: 0.05秒 (1.26%)
2025-07-29 20:05:47,384 - INFO - 
----- 处理字幕 #22 的方案 #2 -----
2025-07-29 20:05:47,384 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 20:05:47,384 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps9gpkpyv
2025-07-29 20:05:47,384 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\429.mp4 (确认存在: True)
2025-07-29 20:05:47,384 - INFO - 添加场景ID=429，时长=2.80秒，累计时长=2.80秒
2025-07-29 20:05:47,384 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\430.mp4 (确认存在: True)
2025-07-29 20:05:47,385 - INFO - 添加场景ID=430，时长=3.20秒，累计时长=6.00秒
2025-07-29 20:05:47,385 - INFO - 准备合并 2 个场景文件，总时长约 6.00秒
2025-07-29 20:05:47,385 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/429.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/430.mp4'

2025-07-29 20:05:47,385 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps9gpkpyv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps9gpkpyv\temp_combined.mp4
2025-07-29 20:05:47,511 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 4.05秒
2025-07-29 20:05:47,511 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps9gpkpyv\temp_combined.mp4 -ss 0 -to 4.052 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 20:05:47,787 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:47,788 - INFO - 目标音频时长: 4.05秒
2025-07-29 20:05:47,788 - INFO - 实际视频时长: 4.10秒
2025-07-29 20:05:47,788 - INFO - 时长差异: 0.05秒 (1.26%)
2025-07-29 20:05:47,788 - INFO - ==========================================
2025-07-29 20:05:47,788 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:47,788 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 20:05:47,788 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps9gpkpyv
2025-07-29 20:05:47,831 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:47,831 - INFO -   - 音频时长: 4.05秒
2025-07-29 20:05:47,831 - INFO -   - 视频时长: 4.10秒
2025-07-29 20:05:47,831 - INFO -   - 时长差异: 0.05秒 (1.26%)
2025-07-29 20:05:47,831 - INFO - 
----- 处理字幕 #22 的方案 #3 -----
2025-07-29 20:05:47,831 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 20:05:47,831 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcr_7zx3v
2025-07-29 20:05:47,832 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\425.mp4 (确认存在: True)
2025-07-29 20:05:47,832 - INFO - 添加场景ID=425，时长=1.16秒，累计时长=1.16秒
2025-07-29 20:05:47,832 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\429.mp4 (确认存在: True)
2025-07-29 20:05:47,832 - INFO - 添加场景ID=429，时长=2.80秒，累计时长=3.96秒
2025-07-29 20:05:47,832 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\430.mp4 (确认存在: True)
2025-07-29 20:05:47,832 - INFO - 添加场景ID=430，时长=3.20秒，累计时长=7.16秒
2025-07-29 20:05:47,832 - INFO - 场景总时长(7.16秒)已达到音频时长(4.05秒)的1.5倍，停止添加场景
2025-07-29 20:05:47,832 - INFO - 准备合并 3 个场景文件，总时长约 7.16秒
2025-07-29 20:05:47,832 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/425.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/429.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/430.mp4'

2025-07-29 20:05:47,832 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcr_7zx3v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcr_7zx3v\temp_combined.mp4
2025-07-29 20:05:47,953 - INFO - 合并后的视频时长: 7.23秒，目标音频时长: 4.05秒
2025-07-29 20:05:47,953 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcr_7zx3v\temp_combined.mp4 -ss 0 -to 4.052 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 20:05:48,208 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:48,208 - INFO - 目标音频时长: 4.05秒
2025-07-29 20:05:48,208 - INFO - 实际视频时长: 4.10秒
2025-07-29 20:05:48,208 - INFO - 时长差异: 0.05秒 (1.26%)
2025-07-29 20:05:48,208 - INFO - ==========================================
2025-07-29 20:05:48,208 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:48,208 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 20:05:48,209 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcr_7zx3v
2025-07-29 20:05:48,253 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:48,253 - INFO -   - 音频时长: 4.05秒
2025-07-29 20:05:48,253 - INFO -   - 视频时长: 4.10秒
2025-07-29 20:05:48,253 - INFO -   - 时长差异: 0.05秒 (1.26%)
2025-07-29 20:05:48,253 - INFO - 
字幕 #22 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:48,253 - INFO - 生成的视频文件:
2025-07-29 20:05:48,253 - INFO -   1. F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 20:05:48,253 - INFO -   2. F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 20:05:48,253 - INFO -   3. F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 20:05:48,253 - INFO - ========== 字幕 #22 处理结束 ==========

