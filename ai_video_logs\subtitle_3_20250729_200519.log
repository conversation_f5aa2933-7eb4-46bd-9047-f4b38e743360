2025-07-29 20:05:19,552 - INFO - ========== 字幕 #3 处理开始 ==========
2025-07-29 20:05:19,552 - INFO - 字幕内容: 妹妹天真地以为，到那时，姐姐早已嫁给了那个深爱她的男人。
2025-07-29 20:05:19,552 - INFO - 字幕序号: [11, 14]
2025-07-29 20:05:19,552 - INFO - 音频文件详情:
2025-07-29 20:05:19,552 - INFO -   - 路径: output\3.wav
2025-07-29 20:05:19,552 - INFO -   - 时长: 4.32秒
2025-07-29 20:05:19,552 - INFO -   - 验证音频时长: 4.32秒
2025-07-29 20:05:19,552 - INFO - 字幕时间戳信息:
2025-07-29 20:05:19,552 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:19,553 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:19,553 - INFO -   - 根据生成的音频时长(4.32秒)已调整字幕时间戳
2025-07-29 20:05:19,553 - INFO - ========== 新模式：为字幕 #3 生成4套场景方案 ==========
2025-07-29 20:05:19,553 - INFO - 字幕序号列表: [11, 14]
2025-07-29 20:05:19,553 - INFO - 
--- 生成方案 #1：基于字幕序号 #11 ---
2025-07-29 20:05:19,553 - INFO - 开始为单个字幕序号 #11 匹配场景，目标时长: 4.32秒
2025-07-29 20:05:19,553 - INFO - 开始查找字幕序号 [11] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:19,553 - INFO - 找到related_overlap场景: scene_id=8, 字幕#11
2025-07-29 20:05:19,553 - INFO - 找到related_overlap场景: scene_id=9, 字幕#11
2025-07-29 20:05:19,554 - INFO - 字幕 #11 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:19,554 - INFO - 字幕序号 #11 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:19,554 - INFO - 选择第一个overlap场景作为起点: scene_id=8
2025-07-29 20:05:19,554 - INFO - 添加起点场景: scene_id=8, 时长=0.80秒, 累计时长=0.80秒
2025-07-29 20:05:19,554 - INFO - 起点场景时长不足，需要延伸填充 3.52秒
2025-07-29 20:05:19,554 - INFO - 起点场景在原始列表中的索引: 7
2025-07-29 20:05:19,554 - INFO - 延伸添加场景: scene_id=9 (完整时长 1.76秒)
2025-07-29 20:05:19,554 - INFO - 累计时长: 2.56秒
2025-07-29 20:05:19,554 - INFO - 延伸添加场景: scene_id=10 (裁剪至 1.76秒)
2025-07-29 20:05:19,554 - INFO - 累计时长: 4.32秒
2025-07-29 20:05:19,554 - INFO - 字幕序号 #11 场景匹配完成，共选择 3 个场景，总时长: 4.32秒
2025-07-29 20:05:19,554 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:19,554 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:19,554 - INFO - 
--- 生成方案 #2：基于字幕序号 #14 ---
2025-07-29 20:05:19,554 - INFO - 开始为单个字幕序号 #14 匹配场景，目标时长: 4.32秒
2025-07-29 20:05:19,554 - INFO - 开始查找字幕序号 [14] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:19,555 - INFO - 找到related_overlap场景: scene_id=11, 字幕#14
2025-07-29 20:05:19,556 - INFO - 字幕 #14 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:19,556 - INFO - 字幕序号 #14 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:19,556 - INFO - 选择第一个overlap场景作为起点: scene_id=11
2025-07-29 20:05:19,556 - INFO - 添加起点场景: scene_id=11, 时长=1.76秒, 累计时长=1.76秒
2025-07-29 20:05:19,556 - INFO - 起点场景时长不足，需要延伸填充 2.56秒
2025-07-29 20:05:19,556 - INFO - 起点场景在原始列表中的索引: 10
2025-07-29 20:05:19,556 - INFO - 延伸添加场景: scene_id=12 (完整时长 1.88秒)
2025-07-29 20:05:19,556 - INFO - 累计时长: 3.64秒
2025-07-29 20:05:19,556 - INFO - 延伸添加场景: scene_id=13 (裁剪至 0.68秒)
2025-07-29 20:05:19,556 - INFO - 累计时长: 4.32秒
2025-07-29 20:05:19,556 - INFO - 字幕序号 #14 场景匹配完成，共选择 3 个场景，总时长: 4.32秒
2025-07-29 20:05:19,556 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:19,556 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:19,556 - INFO - ========== 当前模式：为字幕 #3 生成 1 套场景方案 ==========
2025-07-29 20:05:19,556 - INFO - 开始查找字幕序号 [11, 14] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:19,556 - INFO - 找到related_overlap场景: scene_id=8, 字幕#11
2025-07-29 20:05:19,556 - INFO - 找到related_overlap场景: scene_id=9, 字幕#11
2025-07-29 20:05:19,556 - INFO - 找到related_overlap场景: scene_id=11, 字幕#14
2025-07-29 20:05:19,557 - INFO - 字幕 #11 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:19,557 - INFO - 字幕 #14 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:19,557 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:19,557 - INFO - 开始生成方案 #1
2025-07-29 20:05:19,557 - INFO - 方案 #1: 为字幕#11选择初始化overlap场景id=9
2025-07-29 20:05:19,557 - INFO - 方案 #1: 为字幕#14选择初始化overlap场景id=11
2025-07-29 20:05:19,557 - INFO - 方案 #1: 初始选择后，当前总时长=3.52秒
2025-07-29 20:05:19,557 - INFO - 方案 #1: 额外添加overlap场景id=8, 当前总时长=4.32秒
2025-07-29 20:05:19,557 - INFO - 方案 #1: 额外between选择后，当前总时长=4.32秒
2025-07-29 20:05:19,557 - INFO - 方案 #1: 场景总时长(4.32秒)大于音频时长(4.32秒)，需要裁剪
2025-07-29 20:05:19,557 - INFO - 调整前总时长: 4.32秒, 目标时长: 4.32秒
2025-07-29 20:05:19,557 - INFO - 场景时长已经匹配目标时长，无需调整
2025-07-29 20:05:19,557 - INFO - 方案 #1 调整/填充后最终总时长: 4.32秒
2025-07-29 20:05:19,557 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:19,557 - INFO - ========== 当前模式：字幕 #3 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:19,557 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:19,557 - INFO - ========== 新模式：字幕 #3 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:19,557 - INFO - 
----- 处理字幕 #3 的方案 #1 -----
2025-07-29 20:05:19,557 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 20:05:19,558 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp88te95id
2025-07-29 20:05:19,558 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\8.mp4 (确认存在: True)
2025-07-29 20:05:19,558 - INFO - 添加场景ID=8，时长=0.80秒，累计时长=0.80秒
2025-07-29 20:05:19,558 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\9.mp4 (确认存在: True)
2025-07-29 20:05:19,558 - INFO - 添加场景ID=9，时长=1.76秒，累计时长=2.56秒
2025-07-29 20:05:19,558 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\10.mp4 (确认存在: True)
2025-07-29 20:05:19,558 - INFO - 添加场景ID=10，时长=2.40秒，累计时长=4.96秒
2025-07-29 20:05:19,558 - INFO - 准备合并 3 个场景文件，总时长约 4.96秒
2025-07-29 20:05:19,558 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/8.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/9.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/10.mp4'

2025-07-29 20:05:19,559 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp88te95id\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp88te95id\temp_combined.mp4
2025-07-29 20:05:19,714 - INFO - 合并后的视频时长: 5.03秒，目标音频时长: 4.32秒
2025-07-29 20:05:19,714 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp88te95id\temp_combined.mp4 -ss 0 -to 4.318 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 20:05:20,027 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:20,027 - INFO - 目标音频时长: 4.32秒
2025-07-29 20:05:20,027 - INFO - 实际视频时长: 4.34秒
2025-07-29 20:05:20,027 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:20,027 - INFO - ==========================================
2025-07-29 20:05:20,027 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:20,027 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 20:05:20,028 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp88te95id
2025-07-29 20:05:20,072 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:20,072 - INFO -   - 音频时长: 4.32秒
2025-07-29 20:05:20,072 - INFO -   - 视频时长: 4.34秒
2025-07-29 20:05:20,072 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:20,072 - INFO - 
----- 处理字幕 #3 的方案 #2 -----
2025-07-29 20:05:20,072 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 20:05:20,073 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvp4g3r7q
2025-07-29 20:05:20,073 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\11.mp4 (确认存在: True)
2025-07-29 20:05:20,073 - INFO - 添加场景ID=11，时长=1.76秒，累计时长=1.76秒
2025-07-29 20:05:20,073 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\12.mp4 (确认存在: True)
2025-07-29 20:05:20,073 - INFO - 添加场景ID=12，时长=1.88秒，累计时长=3.64秒
2025-07-29 20:05:20,073 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\13.mp4 (确认存在: True)
2025-07-29 20:05:20,073 - INFO - 添加场景ID=13，时长=3.64秒，累计时长=7.28秒
2025-07-29 20:05:20,073 - INFO - 场景总时长(7.28秒)已达到音频时长(4.32秒)的1.5倍，停止添加场景
2025-07-29 20:05:20,073 - INFO - 准备合并 3 个场景文件，总时长约 7.28秒
2025-07-29 20:05:20,074 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/11.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/12.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/13.mp4'

2025-07-29 20:05:20,074 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvp4g3r7q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvp4g3r7q\temp_combined.mp4
2025-07-29 20:05:20,230 - INFO - 合并后的视频时长: 7.35秒，目标音频时长: 4.32秒
2025-07-29 20:05:20,230 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvp4g3r7q\temp_combined.mp4 -ss 0 -to 4.318 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 20:05:20,533 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:20,533 - INFO - 目标音频时长: 4.32秒
2025-07-29 20:05:20,533 - INFO - 实际视频时长: 4.34秒
2025-07-29 20:05:20,533 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:20,533 - INFO - ==========================================
2025-07-29 20:05:20,533 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:20,533 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 20:05:20,534 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvp4g3r7q
2025-07-29 20:05:20,579 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:20,579 - INFO -   - 音频时长: 4.32秒
2025-07-29 20:05:20,579 - INFO -   - 视频时长: 4.34秒
2025-07-29 20:05:20,579 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:20,579 - INFO - 
----- 处理字幕 #3 的方案 #3 -----
2025-07-29 20:05:20,580 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 20:05:20,580 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppf_wmusb
2025-07-29 20:05:20,592 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\9.mp4 (确认存在: True)
2025-07-29 20:05:20,592 - INFO - 添加场景ID=9，时长=1.76秒，累计时长=1.76秒
2025-07-29 20:05:20,592 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\11.mp4 (确认存在: True)
2025-07-29 20:05:20,592 - INFO - 添加场景ID=11，时长=1.76秒，累计时长=3.52秒
2025-07-29 20:05:20,592 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\8.mp4 (确认存在: True)
2025-07-29 20:05:20,592 - INFO - 添加场景ID=8，时长=0.80秒，累计时长=4.32秒
2025-07-29 20:05:20,592 - INFO - 准备合并 3 个场景文件，总时长约 4.32秒
2025-07-29 20:05:20,592 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/9.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/11.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/8.mp4'

2025-07-29 20:05:20,592 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppf_wmusb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppf_wmusb\temp_combined.mp4
2025-07-29 20:05:20,726 - INFO - 合并后的视频时长: 4.39秒，目标音频时长: 4.32秒
2025-07-29 20:05:20,726 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppf_wmusb\temp_combined.mp4 -ss 0 -to 4.318 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 20:05:21,027 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:21,028 - INFO - 目标音频时长: 4.32秒
2025-07-29 20:05:21,028 - INFO - 实际视频时长: 4.34秒
2025-07-29 20:05:21,028 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:21,028 - INFO - ==========================================
2025-07-29 20:05:21,028 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:21,028 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 20:05:21,028 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppf_wmusb
2025-07-29 20:05:21,075 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:21,075 - INFO -   - 音频时长: 4.32秒
2025-07-29 20:05:21,075 - INFO -   - 视频时长: 4.34秒
2025-07-29 20:05:21,075 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:21,075 - INFO - 
字幕 #3 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:21,075 - INFO - 生成的视频文件:
2025-07-29 20:05:21,075 - INFO -   1. F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 20:05:21,075 - INFO -   2. F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 20:05:21,075 - INFO -   3. F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 20:05:21,075 - INFO - ========== 字幕 #3 处理结束 ==========

