2025-07-29 20:06:17,130 - INFO - ========== 字幕 #42 处理开始 ==========
2025-07-29 20:06:17,130 - INFO - 字幕内容: 女人作为许氏集团的总裁，正为两天后由威尔集团牵头的AI研讨会发愁，她做梦也想不到，那位神秘的总裁An先生，就是她亏欠了五年的男人。
2025-07-29 20:06:17,130 - INFO - 字幕序号: [900, 905]
2025-07-29 20:06:17,130 - INFO - 音频文件详情:
2025-07-29 20:06:17,130 - INFO -   - 路径: output\42.wav
2025-07-29 20:06:17,130 - INFO -   - 时长: 7.91秒
2025-07-29 20:06:17,130 - INFO -   - 验证音频时长: 7.91秒
2025-07-29 20:06:17,140 - INFO - 字幕时间戳信息:
2025-07-29 20:06:17,140 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:17,140 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:17,140 - INFO -   - 根据生成的音频时长(7.91秒)已调整字幕时间戳
2025-07-29 20:06:17,140 - INFO - ========== 新模式：为字幕 #42 生成4套场景方案 ==========
2025-07-29 20:06:17,140 - INFO - 字幕序号列表: [900, 905]
2025-07-29 20:06:17,140 - INFO - 
--- 生成方案 #1：基于字幕序号 #900 ---
2025-07-29 20:06:17,140 - INFO - 开始为单个字幕序号 #900 匹配场景，目标时长: 7.91秒
2025-07-29 20:06:17,140 - INFO - 开始查找字幕序号 [900] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:17,140 - INFO - 找到related_overlap场景: scene_id=938, 字幕#900
2025-07-29 20:06:17,141 - INFO - 字幕 #900 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:17,141 - INFO - 字幕序号 #900 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:17,141 - INFO - 选择第一个overlap场景作为起点: scene_id=938
2025-07-29 20:06:17,141 - INFO - 添加起点场景: scene_id=938, 时长=4.68秒, 累计时长=4.68秒
2025-07-29 20:06:17,141 - INFO - 起点场景时长不足，需要延伸填充 3.23秒
2025-07-29 20:06:17,142 - INFO - 起点场景在原始列表中的索引: 937
2025-07-29 20:06:17,142 - INFO - 延伸添加场景: scene_id=939 (完整时长 2.92秒)
2025-07-29 20:06:17,142 - INFO - 累计时长: 7.60秒
2025-07-29 20:06:17,142 - INFO - 延伸添加场景: scene_id=940 (裁剪至 0.31秒)
2025-07-29 20:06:17,142 - INFO - 累计时长: 7.91秒
2025-07-29 20:06:17,142 - INFO - 字幕序号 #900 场景匹配完成，共选择 3 个场景，总时长: 7.91秒
2025-07-29 20:06:17,142 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:17,142 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:17,142 - INFO - 
--- 生成方案 #2：基于字幕序号 #905 ---
2025-07-29 20:06:17,142 - INFO - 开始为单个字幕序号 #905 匹配场景，目标时长: 7.91秒
2025-07-29 20:06:17,142 - INFO - 开始查找字幕序号 [905] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:17,142 - INFO - 找到related_overlap场景: scene_id=940, 字幕#905
2025-07-29 20:06:17,143 - INFO - 字幕 #905 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:17,143 - INFO - 字幕序号 #905 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:17,143 - ERROR - 字幕序号 #905 没有找到任何可用的匹配场景
2025-07-29 20:06:17,143 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:17,143 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:17,143 - INFO - ========== 当前模式：为字幕 #42 生成 1 套场景方案 ==========
2025-07-29 20:06:17,143 - INFO - 开始查找字幕序号 [900, 905] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:17,143 - INFO - 找到related_overlap场景: scene_id=938, 字幕#900
2025-07-29 20:06:17,143 - INFO - 找到related_overlap场景: scene_id=940, 字幕#905
2025-07-29 20:06:17,144 - INFO - 字幕 #900 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:17,144 - INFO - 字幕 #905 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:17,144 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:17,145 - INFO - 开始生成方案 #1
2025-07-29 20:06:17,145 - INFO - 方案 #1: 为字幕#900选择初始化overlap场景id=938
2025-07-29 20:06:17,145 - INFO - 方案 #1: 为字幕#905选择初始化overlap场景id=940
2025-07-29 20:06:17,145 - INFO - 方案 #1: 初始选择后，当前总时长=7.44秒
2025-07-29 20:06:17,145 - INFO - 方案 #1: 额外between选择后，当前总时长=7.44秒
2025-07-29 20:06:17,145 - INFO - 方案 #1: 场景总时长(7.44秒)小于音频时长(7.91秒)，需要延伸填充
2025-07-29 20:06:17,145 - INFO - 方案 #1: 最后一个场景ID: 940
2025-07-29 20:06:17,145 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 939
2025-07-29 20:06:17,145 - INFO - 方案 #1: 需要填充时长: 0.47秒
2025-07-29 20:06:17,145 - INFO - 方案 #1: 追加场景 scene_id=941 (裁剪至 0.47秒)
2025-07-29 20:06:17,145 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:17,145 - INFO - 方案 #1 调整/填充后最终总时长: 7.91秒
2025-07-29 20:06:17,145 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:17,145 - INFO - ========== 当前模式：字幕 #42 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:17,145 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:17,145 - INFO - ========== 新模式：字幕 #42 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:17,145 - INFO - 
----- 处理字幕 #42 的方案 #1 -----
2025-07-29 20:06:17,145 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 20:06:17,146 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk7tjps52
2025-07-29 20:06:17,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\938.mp4 (确认存在: True)
2025-07-29 20:06:17,146 - INFO - 添加场景ID=938，时长=4.68秒，累计时长=4.68秒
2025-07-29 20:06:17,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\939.mp4 (确认存在: True)
2025-07-29 20:06:17,146 - INFO - 添加场景ID=939，时长=2.92秒，累计时长=7.60秒
2025-07-29 20:06:17,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\940.mp4 (确认存在: True)
2025-07-29 20:06:17,146 - INFO - 添加场景ID=940，时长=2.76秒，累计时长=10.36秒
2025-07-29 20:06:17,146 - INFO - 准备合并 3 个场景文件，总时长约 10.36秒
2025-07-29 20:06:17,146 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/938.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/939.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/940.mp4'

2025-07-29 20:06:17,146 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpk7tjps52\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpk7tjps52\temp_combined.mp4
2025-07-29 20:06:17,291 - INFO - 合并后的视频时长: 10.43秒，目标音频时长: 7.91秒
2025-07-29 20:06:17,291 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpk7tjps52\temp_combined.mp4 -ss 0 -to 7.911 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 20:06:17,683 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:17,683 - INFO - 目标音频时长: 7.91秒
2025-07-29 20:06:17,683 - INFO - 实际视频时长: 7.94秒
2025-07-29 20:06:17,683 - INFO - 时长差异: 0.03秒 (0.40%)
2025-07-29 20:06:17,683 - INFO - ==========================================
2025-07-29 20:06:17,683 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:17,683 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 20:06:17,684 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk7tjps52
2025-07-29 20:06:17,731 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:17,731 - INFO -   - 音频时长: 7.91秒
2025-07-29 20:06:17,731 - INFO -   - 视频时长: 7.94秒
2025-07-29 20:06:17,731 - INFO -   - 时长差异: 0.03秒 (0.40%)
2025-07-29 20:06:17,731 - INFO - 
----- 处理字幕 #42 的方案 #2 -----
2025-07-29 20:06:17,731 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 20:06:17,732 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcl6w3pt1
2025-07-29 20:06:17,732 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\938.mp4 (确认存在: True)
2025-07-29 20:06:17,733 - INFO - 添加场景ID=938，时长=4.68秒，累计时长=4.68秒
2025-07-29 20:06:17,733 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\940.mp4 (确认存在: True)
2025-07-29 20:06:17,733 - INFO - 添加场景ID=940，时长=2.76秒，累计时长=7.44秒
2025-07-29 20:06:17,733 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\941.mp4 (确认存在: True)
2025-07-29 20:06:17,733 - INFO - 添加场景ID=941，时长=3.64秒，累计时长=11.08秒
2025-07-29 20:06:17,733 - INFO - 准备合并 3 个场景文件，总时长约 11.08秒
2025-07-29 20:06:17,733 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/938.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/940.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/941.mp4'

2025-07-29 20:06:17,733 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcl6w3pt1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcl6w3pt1\temp_combined.mp4
2025-07-29 20:06:17,886 - INFO - 合并后的视频时长: 11.15秒，目标音频时长: 7.91秒
2025-07-29 20:06:17,886 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcl6w3pt1\temp_combined.mp4 -ss 0 -to 7.911 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 20:06:18,285 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:18,285 - INFO - 目标音频时长: 7.91秒
2025-07-29 20:06:18,285 - INFO - 实际视频时长: 7.94秒
2025-07-29 20:06:18,285 - INFO - 时长差异: 0.03秒 (0.40%)
2025-07-29 20:06:18,285 - INFO - ==========================================
2025-07-29 20:06:18,285 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:18,285 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 20:06:18,286 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcl6w3pt1
2025-07-29 20:06:18,331 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:18,331 - INFO -   - 音频时长: 7.91秒
2025-07-29 20:06:18,331 - INFO -   - 视频时长: 7.94秒
2025-07-29 20:06:18,331 - INFO -   - 时长差异: 0.03秒 (0.40%)
2025-07-29 20:06:18,331 - INFO - 
字幕 #42 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:18,331 - INFO - 生成的视频文件:
2025-07-29 20:06:18,331 - INFO -   1. F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 20:06:18,331 - INFO -   2. F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 20:06:18,331 - INFO - ========== 字幕 #42 处理结束 ==========

