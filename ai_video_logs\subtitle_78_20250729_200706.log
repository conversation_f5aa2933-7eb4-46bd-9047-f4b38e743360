2025-07-29 20:07:06,796 - INFO - ========== 字幕 #78 处理开始 ==========
2025-07-29 20:07:06,796 - INFO - 字幕内容: 就在这千钧一发之际，女人做出了一个谁也想不到的决定，她用自己的身体，挡住了刺向念念的利刃！
2025-07-29 20:07:06,796 - INFO - 字幕序号: [4241, 4245]
2025-07-29 20:07:06,796 - INFO - 音频文件详情:
2025-07-29 20:07:06,796 - INFO -   - 路径: output\78.wav
2025-07-29 20:07:06,796 - INFO -   - 时长: 4.63秒
2025-07-29 20:07:06,796 - INFO -   - 验证音频时长: 4.63秒
2025-07-29 20:07:06,796 - INFO - 字幕时间戳信息:
2025-07-29 20:07:06,796 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:06,796 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:06,796 - INFO -   - 根据生成的音频时长(4.63秒)已调整字幕时间戳
2025-07-29 20:07:06,796 - INFO - ========== 新模式：为字幕 #78 生成4套场景方案 ==========
2025-07-29 20:07:06,796 - INFO - 字幕序号列表: [4241, 4245]
2025-07-29 20:07:06,796 - INFO - 
--- 生成方案 #1：基于字幕序号 #4241 ---
2025-07-29 20:07:06,796 - INFO - 开始为单个字幕序号 #4241 匹配场景，目标时长: 4.63秒
2025-07-29 20:07:06,796 - INFO - 开始查找字幕序号 [4241] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:06,798 - INFO - 找到related_overlap场景: scene_id=3748, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3745, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3746, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3747, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3749, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3750, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3751, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3752, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 找到related_between场景: scene_id=3753, 字幕#4241
2025-07-29 20:07:06,799 - INFO - 字幕 #4241 找到 1 个overlap场景, 8 个between场景
2025-07-29 20:07:06,799 - INFO - 字幕序号 #4241 找到 1 个可用overlap场景, 8 个可用between场景
2025-07-29 20:07:06,799 - INFO - 选择第一个overlap场景作为起点: scene_id=3748
2025-07-29 20:07:06,799 - INFO - 添加起点场景: scene_id=3748, 时长=1.52秒, 累计时长=1.52秒
2025-07-29 20:07:06,799 - INFO - 起点场景时长不足，需要延伸填充 3.11秒
2025-07-29 20:07:06,799 - INFO - 起点场景在原始列表中的索引: 3747
2025-07-29 20:07:06,799 - INFO - 延伸添加场景: scene_id=3749 (完整时长 1.96秒)
2025-07-29 20:07:06,799 - INFO - 累计时长: 3.48秒
2025-07-29 20:07:06,799 - INFO - 延伸添加场景: scene_id=3750 (裁剪至 1.15秒)
2025-07-29 20:07:06,799 - INFO - 累计时长: 4.63秒
2025-07-29 20:07:06,799 - INFO - 字幕序号 #4241 场景匹配完成，共选择 3 个场景，总时长: 4.63秒
2025-07-29 20:07:06,799 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:07:06,799 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:07:06,799 - INFO - 
--- 生成方案 #2：基于字幕序号 #4245 ---
2025-07-29 20:07:06,799 - INFO - 开始为单个字幕序号 #4245 匹配场景，目标时长: 4.63秒
2025-07-29 20:07:06,799 - INFO - 开始查找字幕序号 [4245] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:06,800 - INFO - 找到related_overlap场景: scene_id=3757, 字幕#4245
2025-07-29 20:07:06,800 - INFO - 找到related_overlap场景: scene_id=3758, 字幕#4245
2025-07-29 20:07:06,800 - INFO - 字幕 #4245 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:07:06,800 - INFO - 字幕序号 #4245 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:06,800 - INFO - 选择第一个overlap场景作为起点: scene_id=3757
2025-07-29 20:07:06,800 - INFO - 添加起点场景: scene_id=3757, 时长=2.52秒, 累计时长=2.52秒
2025-07-29 20:07:06,800 - INFO - 起点场景时长不足，需要延伸填充 2.11秒
2025-07-29 20:07:06,800 - INFO - 起点场景在原始列表中的索引: 3756
2025-07-29 20:07:06,800 - INFO - 延伸添加场景: scene_id=3758 (裁剪至 2.11秒)
2025-07-29 20:07:06,800 - INFO - 累计时长: 4.63秒
2025-07-29 20:07:06,800 - INFO - 字幕序号 #4245 场景匹配完成，共选择 2 个场景，总时长: 4.63秒
2025-07-29 20:07:06,801 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:07:06,801 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:07:06,801 - INFO - ========== 当前模式：为字幕 #78 生成 1 套场景方案 ==========
2025-07-29 20:07:06,801 - INFO - 开始查找字幕序号 [4241, 4245] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:06,801 - INFO - 找到related_overlap场景: scene_id=3748, 字幕#4241
2025-07-29 20:07:06,801 - INFO - 找到related_overlap场景: scene_id=3757, 字幕#4245
2025-07-29 20:07:06,801 - INFO - 找到related_overlap场景: scene_id=3758, 字幕#4245
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3745, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3746, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3747, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3749, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3750, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3751, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3752, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 找到related_between场景: scene_id=3753, 字幕#4241
2025-07-29 20:07:06,802 - INFO - 字幕 #4241 找到 1 个overlap场景, 8 个between场景
2025-07-29 20:07:06,802 - INFO - 字幕 #4245 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:07:06,802 - INFO - 共收集 3 个未使用的overlap场景和 8 个未使用的between场景
2025-07-29 20:07:06,802 - INFO - 开始生成方案 #1
2025-07-29 20:07:06,802 - INFO - 方案 #1: 为字幕#4241选择初始化overlap场景id=3748
2025-07-29 20:07:06,802 - INFO - 方案 #1: 为字幕#4245选择初始化overlap场景id=3757
2025-07-29 20:07:06,802 - INFO - 方案 #1: 初始选择后，当前总时长=4.04秒
2025-07-29 20:07:06,802 - INFO - 方案 #1: 额外添加overlap场景id=3758, 当前总时长=6.28秒
2025-07-29 20:07:06,802 - INFO - 方案 #1: 额外between选择后，当前总时长=6.28秒
2025-07-29 20:07:06,802 - INFO - 方案 #1: 场景总时长(6.28秒)大于音频时长(4.63秒)，需要裁剪
2025-07-29 20:07:06,802 - INFO - 调整前总时长: 6.28秒, 目标时长: 4.63秒
2025-07-29 20:07:06,802 - INFO - 需要裁剪 1.65秒
2025-07-29 20:07:06,802 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:07:06,802 - INFO - 裁剪场景ID=3757：从2.52秒裁剪至1.00秒
2025-07-29 20:07:06,802 - INFO - 裁剪场景ID=3758：从2.24秒裁剪至2.11秒
2025-07-29 20:07:06,802 - INFO - 调整后总时长: 4.63秒，与目标时长差异: 0.00秒
2025-07-29 20:07:06,802 - INFO - 方案 #1 调整/填充后最终总时长: 4.63秒
2025-07-29 20:07:06,802 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:06,802 - INFO - ========== 当前模式：字幕 #78 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:06,802 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:07:06,802 - INFO - ========== 新模式：字幕 #78 共生成 3 套有效场景方案 ==========
2025-07-29 20:07:06,803 - INFO - 
----- 处理字幕 #78 的方案 #1 -----
2025-07-29 20:07:06,803 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\78_1.mp4
2025-07-29 20:07:06,803 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp12m91fow
2025-07-29 20:07:06,804 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3748.mp4 (确认存在: True)
2025-07-29 20:07:06,804 - INFO - 添加场景ID=3748，时长=1.52秒，累计时长=1.52秒
2025-07-29 20:07:06,804 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3749.mp4 (确认存在: True)
2025-07-29 20:07:06,804 - INFO - 添加场景ID=3749，时长=1.96秒，累计时长=3.48秒
2025-07-29 20:07:06,804 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3750.mp4 (确认存在: True)
2025-07-29 20:07:06,804 - INFO - 添加场景ID=3750，时长=1.20秒，累计时长=4.68秒
2025-07-29 20:07:06,804 - INFO - 准备合并 3 个场景文件，总时长约 4.68秒
2025-07-29 20:07:06,804 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3748.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3749.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3750.mp4'

2025-07-29 20:07:06,804 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp12m91fow\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp12m91fow\temp_combined.mp4
2025-07-29 20:07:06,939 - INFO - 合并后的视频时长: 4.75秒，目标音频时长: 4.63秒
2025-07-29 20:07:06,939 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp12m91fow\temp_combined.mp4 -ss 0 -to 4.63 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\78_1.mp4
2025-07-29 20:07:07,241 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:07,241 - INFO - 目标音频时长: 4.63秒
2025-07-29 20:07:07,241 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:07:07,241 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-29 20:07:07,241 - INFO - ==========================================
2025-07-29 20:07:07,241 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:07,241 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\78_1.mp4
2025-07-29 20:07:07,241 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp12m91fow
2025-07-29 20:07:07,284 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:07,284 - INFO -   - 音频时长: 4.63秒
2025-07-29 20:07:07,284 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:07:07,284 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-29 20:07:07,284 - INFO - 
----- 处理字幕 #78 的方案 #2 -----
2025-07-29 20:07:07,284 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\78_2.mp4
2025-07-29 20:07:07,285 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1uko3t2r
2025-07-29 20:07:07,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3757.mp4 (确认存在: True)
2025-07-29 20:07:07,285 - INFO - 添加场景ID=3757，时长=2.52秒，累计时长=2.52秒
2025-07-29 20:07:07,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3758.mp4 (确认存在: True)
2025-07-29 20:07:07,285 - INFO - 添加场景ID=3758，时长=2.24秒，累计时长=4.76秒
2025-07-29 20:07:07,286 - INFO - 准备合并 2 个场景文件，总时长约 4.76秒
2025-07-29 20:07:07,286 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3757.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3758.mp4'

2025-07-29 20:07:07,286 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1uko3t2r\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1uko3t2r\temp_combined.mp4
2025-07-29 20:07:07,411 - INFO - 合并后的视频时长: 4.81秒，目标音频时长: 4.63秒
2025-07-29 20:07:07,411 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1uko3t2r\temp_combined.mp4 -ss 0 -to 4.63 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\78_2.mp4
2025-07-29 20:07:07,713 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:07,713 - INFO - 目标音频时长: 4.63秒
2025-07-29 20:07:07,713 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:07:07,713 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-29 20:07:07,713 - INFO - ==========================================
2025-07-29 20:07:07,713 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:07,713 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\78_2.mp4
2025-07-29 20:07:07,713 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1uko3t2r
2025-07-29 20:07:07,761 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:07,761 - INFO -   - 音频时长: 4.63秒
2025-07-29 20:07:07,761 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:07:07,761 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-29 20:07:07,761 - INFO - 
----- 处理字幕 #78 的方案 #3 -----
2025-07-29 20:07:07,761 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\78_3.mp4
2025-07-29 20:07:07,762 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2g1zfrsu
2025-07-29 20:07:07,762 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3748.mp4 (确认存在: True)
2025-07-29 20:07:07,762 - INFO - 添加场景ID=3748，时长=1.52秒，累计时长=1.52秒
2025-07-29 20:07:07,762 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3757.mp4 (确认存在: True)
2025-07-29 20:07:07,762 - INFO - 添加场景ID=3757，时长=2.52秒，累计时长=4.04秒
2025-07-29 20:07:07,762 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3758.mp4 (确认存在: True)
2025-07-29 20:07:07,762 - INFO - 添加场景ID=3758，时长=2.24秒，累计时长=6.28秒
2025-07-29 20:07:07,763 - INFO - 准备合并 3 个场景文件，总时长约 6.28秒
2025-07-29 20:07:07,763 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3748.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3757.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3758.mp4'

2025-07-29 20:07:07,763 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2g1zfrsu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2g1zfrsu\temp_combined.mp4
2025-07-29 20:07:07,889 - INFO - 合并后的视频时长: 6.35秒，目标音频时长: 4.63秒
2025-07-29 20:07:07,889 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2g1zfrsu\temp_combined.mp4 -ss 0 -to 4.63 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\78_3.mp4
2025-07-29 20:07:08,188 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:08,188 - INFO - 目标音频时长: 4.63秒
2025-07-29 20:07:08,188 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:07:08,188 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-29 20:07:08,188 - INFO - ==========================================
2025-07-29 20:07:08,188 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:08,188 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\78_3.mp4
2025-07-29 20:07:08,189 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2g1zfrsu
2025-07-29 20:07:08,235 - INFO - 方案 #3 处理完成:
2025-07-29 20:07:08,235 - INFO -   - 音频时长: 4.63秒
2025-07-29 20:07:08,235 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:07:08,235 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-29 20:07:08,235 - INFO - 
字幕 #78 处理完成，成功生成 3/3 套方案
2025-07-29 20:07:08,235 - INFO - 生成的视频文件:
2025-07-29 20:07:08,235 - INFO -   1. F:/github/aicut_auto/newcut_ai\78_1.mp4
2025-07-29 20:07:08,235 - INFO -   2. F:/github/aicut_auto/newcut_ai\78_2.mp4
2025-07-29 20:07:08,235 - INFO -   3. F:/github/aicut_auto/newcut_ai\78_3.mp4
2025-07-29 20:07:08,235 - INFO - ========== 字幕 #78 处理结束 ==========

