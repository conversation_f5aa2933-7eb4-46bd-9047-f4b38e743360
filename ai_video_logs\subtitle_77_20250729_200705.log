2025-07-29 20:07:05,803 - INFO - ========== 字幕 #77 处理开始 ==========
2025-07-29 20:07:05,803 - INFO - 字幕内容: 看着癫狂的绑匪，为了保护两个无辜的女孩，男人提出用自己交换人质。
2025-07-29 20:07:05,803 - INFO - 字幕序号: [4225, 4231]
2025-07-29 20:07:05,803 - INFO - 音频文件详情:
2025-07-29 20:07:05,803 - INFO -   - 路径: output\77.wav
2025-07-29 20:07:05,803 - INFO -   - 时长: 5.07秒
2025-07-29 20:07:05,803 - INFO -   - 验证音频时长: 5.07秒
2025-07-29 20:07:05,804 - INFO - 字幕时间戳信息:
2025-07-29 20:07:05,813 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:05,813 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:05,813 - INFO -   - 根据生成的音频时长(5.07秒)已调整字幕时间戳
2025-07-29 20:07:05,813 - INFO - ========== 新模式：为字幕 #77 生成4套场景方案 ==========
2025-07-29 20:07:05,813 - INFO - 字幕序号列表: [4225, 4231]
2025-07-29 20:07:05,813 - INFO - 
--- 生成方案 #1：基于字幕序号 #4225 ---
2025-07-29 20:07:05,813 - INFO - 开始为单个字幕序号 #4225 匹配场景，目标时长: 5.07秒
2025-07-29 20:07:05,813 - INFO - 开始查找字幕序号 [4225] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:05,814 - INFO - 找到related_overlap场景: scene_id=3736, 字幕#4225
2025-07-29 20:07:05,814 - INFO - 字幕 #4225 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:05,814 - INFO - 字幕序号 #4225 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:05,814 - INFO - 选择第一个overlap场景作为起点: scene_id=3736
2025-07-29 20:07:05,814 - INFO - 添加起点场景: scene_id=3736, 时长=1.64秒, 累计时长=1.64秒
2025-07-29 20:07:05,815 - INFO - 起点场景时长不足，需要延伸填充 3.43秒
2025-07-29 20:07:05,815 - INFO - 起点场景在原始列表中的索引: 3735
2025-07-29 20:07:05,815 - INFO - 延伸添加场景: scene_id=3737 (完整时长 2.36秒)
2025-07-29 20:07:05,815 - INFO - 累计时长: 4.00秒
2025-07-29 20:07:05,815 - INFO - 延伸添加场景: scene_id=3738 (裁剪至 1.07秒)
2025-07-29 20:07:05,815 - INFO - 累计时长: 5.07秒
2025-07-29 20:07:05,815 - INFO - 字幕序号 #4225 场景匹配完成，共选择 3 个场景，总时长: 5.07秒
2025-07-29 20:07:05,815 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:07:05,815 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:07:05,815 - INFO - 
--- 生成方案 #2：基于字幕序号 #4231 ---
2025-07-29 20:07:05,815 - INFO - 开始为单个字幕序号 #4231 匹配场景，目标时长: 5.07秒
2025-07-29 20:07:05,815 - INFO - 开始查找字幕序号 [4231] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:05,815 - INFO - 找到related_overlap场景: scene_id=3738, 字幕#4231
2025-07-29 20:07:05,816 - INFO - 字幕 #4231 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:05,816 - INFO - 字幕序号 #4231 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:05,816 - ERROR - 字幕序号 #4231 没有找到任何可用的匹配场景
2025-07-29 20:07:05,816 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:07:05,816 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:07:05,816 - INFO - ========== 当前模式：为字幕 #77 生成 1 套场景方案 ==========
2025-07-29 20:07:05,816 - INFO - 开始查找字幕序号 [4225, 4231] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:05,816 - INFO - 找到related_overlap场景: scene_id=3736, 字幕#4225
2025-07-29 20:07:05,816 - INFO - 找到related_overlap场景: scene_id=3738, 字幕#4231
2025-07-29 20:07:05,817 - INFO - 字幕 #4225 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:05,817 - INFO - 字幕 #4231 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:05,817 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:07:05,817 - INFO - 开始生成方案 #1
2025-07-29 20:07:05,817 - INFO - 方案 #1: 为字幕#4225选择初始化overlap场景id=3736
2025-07-29 20:07:05,817 - INFO - 方案 #1: 为字幕#4231选择初始化overlap场景id=3738
2025-07-29 20:07:05,817 - INFO - 方案 #1: 初始选择后，当前总时长=4.76秒
2025-07-29 20:07:05,817 - INFO - 方案 #1: 额外between选择后，当前总时长=4.76秒
2025-07-29 20:07:05,817 - INFO - 方案 #1: 场景总时长(4.76秒)小于音频时长(5.07秒)，需要延伸填充
2025-07-29 20:07:05,817 - INFO - 方案 #1: 最后一个场景ID: 3738
2025-07-29 20:07:05,817 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 3737
2025-07-29 20:07:05,817 - INFO - 方案 #1: 需要填充时长: 0.31秒
2025-07-29 20:07:05,817 - INFO - 方案 #1: 追加场景 scene_id=3739 (裁剪至 0.31秒)
2025-07-29 20:07:05,817 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:07:05,817 - INFO - 方案 #1 调整/填充后最终总时长: 5.07秒
2025-07-29 20:07:05,817 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:05,817 - INFO - ========== 当前模式：字幕 #77 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:05,817 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:07:05,817 - INFO - ========== 新模式：字幕 #77 共生成 2 套有效场景方案 ==========
2025-07-29 20:07:05,817 - INFO - 
----- 处理字幕 #77 的方案 #1 -----
2025-07-29 20:07:05,817 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\77_1.mp4
2025-07-29 20:07:05,818 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfgqf0q5q
2025-07-29 20:07:05,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3736.mp4 (确认存在: True)
2025-07-29 20:07:05,819 - INFO - 添加场景ID=3736，时长=1.64秒，累计时长=1.64秒
2025-07-29 20:07:05,819 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3737.mp4 (确认存在: True)
2025-07-29 20:07:05,819 - INFO - 添加场景ID=3737，时长=2.36秒，累计时长=4.00秒
2025-07-29 20:07:05,819 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3738.mp4 (确认存在: True)
2025-07-29 20:07:05,819 - INFO - 添加场景ID=3738，时长=3.12秒，累计时长=7.12秒
2025-07-29 20:07:05,819 - INFO - 准备合并 3 个场景文件，总时长约 7.12秒
2025-07-29 20:07:05,819 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3736.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3737.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3738.mp4'

2025-07-29 20:07:05,819 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfgqf0q5q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfgqf0q5q\temp_combined.mp4
2025-07-29 20:07:05,956 - INFO - 合并后的视频时长: 7.19秒，目标音频时长: 5.07秒
2025-07-29 20:07:05,956 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfgqf0q5q\temp_combined.mp4 -ss 0 -to 5.069 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\77_1.mp4
2025-07-29 20:07:06,260 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:06,260 - INFO - 目标音频时长: 5.07秒
2025-07-29 20:07:06,260 - INFO - 实际视频时长: 5.10秒
2025-07-29 20:07:06,260 - INFO - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:07:06,260 - INFO - ==========================================
2025-07-29 20:07:06,260 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:06,260 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\77_1.mp4
2025-07-29 20:07:06,260 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfgqf0q5q
2025-07-29 20:07:06,303 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:06,303 - INFO -   - 音频时长: 5.07秒
2025-07-29 20:07:06,303 - INFO -   - 视频时长: 5.10秒
2025-07-29 20:07:06,303 - INFO -   - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:07:06,303 - INFO - 
----- 处理字幕 #77 的方案 #2 -----
2025-07-29 20:07:06,303 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\77_2.mp4
2025-07-29 20:07:06,304 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe25ana4_
2025-07-29 20:07:06,304 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3736.mp4 (确认存在: True)
2025-07-29 20:07:06,304 - INFO - 添加场景ID=3736，时长=1.64秒，累计时长=1.64秒
2025-07-29 20:07:06,304 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3738.mp4 (确认存在: True)
2025-07-29 20:07:06,304 - INFO - 添加场景ID=3738，时长=3.12秒，累计时长=4.76秒
2025-07-29 20:07:06,304 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3739.mp4 (确认存在: True)
2025-07-29 20:07:06,305 - INFO - 添加场景ID=3739，时长=2.76秒，累计时长=7.52秒
2025-07-29 20:07:06,305 - INFO - 准备合并 3 个场景文件，总时长约 7.52秒
2025-07-29 20:07:06,305 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3736.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3738.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3739.mp4'

2025-07-29 20:07:06,305 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe25ana4_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe25ana4_\temp_combined.mp4
2025-07-29 20:07:06,431 - INFO - 合并后的视频时长: 7.59秒，目标音频时长: 5.07秒
2025-07-29 20:07:06,431 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe25ana4_\temp_combined.mp4 -ss 0 -to 5.069 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\77_2.mp4
2025-07-29 20:07:06,740 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:06,740 - INFO - 目标音频时长: 5.07秒
2025-07-29 20:07:06,740 - INFO - 实际视频时长: 5.10秒
2025-07-29 20:07:06,740 - INFO - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:07:06,740 - INFO - ==========================================
2025-07-29 20:07:06,740 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:06,740 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\77_2.mp4
2025-07-29 20:07:06,740 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe25ana4_
2025-07-29 20:07:06,785 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:06,785 - INFO -   - 音频时长: 5.07秒
2025-07-29 20:07:06,785 - INFO -   - 视频时长: 5.10秒
2025-07-29 20:07:06,785 - INFO -   - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:07:06,785 - INFO - 
字幕 #77 处理完成，成功生成 2/2 套方案
2025-07-29 20:07:06,786 - INFO - 生成的视频文件:
2025-07-29 20:07:06,786 - INFO -   1. F:/github/aicut_auto/newcut_ai\77_1.mp4
2025-07-29 20:07:06,786 - INFO -   2. F:/github/aicut_auto/newcut_ai\77_2.mp4
2025-07-29 20:07:06,786 - INFO - ========== 字幕 #77 处理结束 ==========

