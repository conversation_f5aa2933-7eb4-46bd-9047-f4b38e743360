2025-07-29 20:06:19,394 - INFO - ========== 字幕 #44 处理开始 ==========
2025-07-29 20:06:19,394 - INFO - 字幕内容: 她终于承认，是自己当年有眼无珠，才错失了这样一个天才。
2025-07-29 20:06:19,394 - INFO - 字幕序号: [920, 921]
2025-07-29 20:06:19,394 - INFO - 音频文件详情:
2025-07-29 20:06:19,394 - INFO -   - 路径: output\44.wav
2025-07-29 20:06:19,394 - INFO -   - 时长: 2.92秒
2025-07-29 20:06:19,394 - INFO -   - 验证音频时长: 2.92秒
2025-07-29 20:06:19,394 - INFO - 字幕时间戳信息:
2025-07-29 20:06:19,395 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:19,395 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:19,395 - INFO -   - 根据生成的音频时长(2.92秒)已调整字幕时间戳
2025-07-29 20:06:19,395 - INFO - ========== 新模式：为字幕 #44 生成4套场景方案 ==========
2025-07-29 20:06:19,395 - INFO - 字幕序号列表: [920, 921]
2025-07-29 20:06:19,395 - INFO - 
--- 生成方案 #1：基于字幕序号 #920 ---
2025-07-29 20:06:19,395 - INFO - 开始为单个字幕序号 #920 匹配场景，目标时长: 2.92秒
2025-07-29 20:06:19,395 - INFO - 开始查找字幕序号 [920] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:19,395 - INFO - 找到related_overlap场景: scene_id=942, 字幕#920
2025-07-29 20:06:19,396 - INFO - 字幕 #920 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:19,396 - INFO - 字幕序号 #920 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:19,396 - ERROR - 字幕序号 #920 没有找到任何可用的匹配场景
2025-07-29 20:06:19,396 - WARNING - 方案 #1 生成失败，未找到合适的场景
2025-07-29 20:06:19,396 - INFO - 
--- 生成方案 #2：基于字幕序号 #921 ---
2025-07-29 20:06:19,396 - INFO - 开始为单个字幕序号 #921 匹配场景，目标时长: 2.92秒
2025-07-29 20:06:19,396 - INFO - 开始查找字幕序号 [921] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:19,397 - INFO - 找到related_overlap场景: scene_id=942, 字幕#921
2025-07-29 20:06:19,397 - INFO - 找到related_overlap场景: scene_id=943, 字幕#921
2025-07-29 20:06:19,397 - INFO - 找到related_between场景: scene_id=944, 字幕#921
2025-07-29 20:06:19,397 - INFO - 找到related_between场景: scene_id=945, 字幕#921
2025-07-29 20:06:19,397 - INFO - 字幕 #921 找到 2 个overlap场景, 2 个between场景
2025-07-29 20:06:19,397 - INFO - 字幕序号 #921 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:06:19,397 - INFO - 选择第一个overlap场景作为起点: scene_id=943
2025-07-29 20:06:19,397 - INFO - 添加起点场景: scene_id=943, 时长=2.44秒, 累计时长=2.44秒
2025-07-29 20:06:19,397 - INFO - 起点场景时长不足，需要延伸填充 0.48秒
2025-07-29 20:06:19,399 - INFO - 起点场景在原始列表中的索引: 942
2025-07-29 20:06:19,399 - INFO - 延伸添加场景: scene_id=944 (裁剪至 0.48秒)
2025-07-29 20:06:19,399 - INFO - 累计时长: 2.92秒
2025-07-29 20:06:19,399 - INFO - 字幕序号 #921 场景匹配完成，共选择 2 个场景，总时长: 2.92秒
2025-07-29 20:06:19,399 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:06:19,399 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:19,399 - INFO - ========== 当前模式：为字幕 #44 生成 1 套场景方案 ==========
2025-07-29 20:06:19,399 - INFO - 开始查找字幕序号 [920, 921] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:19,399 - INFO - 找到related_overlap场景: scene_id=942, 字幕#920
2025-07-29 20:06:19,399 - INFO - 找到related_overlap场景: scene_id=943, 字幕#921
2025-07-29 20:06:19,400 - INFO - 找到related_between场景: scene_id=944, 字幕#921
2025-07-29 20:06:19,400 - INFO - 找到related_between场景: scene_id=945, 字幕#921
2025-07-29 20:06:19,400 - INFO - 字幕 #920 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:19,400 - INFO - 字幕 #921 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:19,400 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 20:06:19,400 - INFO - 开始生成方案 #1
2025-07-29 20:06:19,400 - INFO - 方案 #1: 为字幕#920选择初始化overlap场景id=942
2025-07-29 20:06:19,400 - INFO - 方案 #1: 为字幕#921选择初始化overlap场景id=943
2025-07-29 20:06:19,400 - INFO - 方案 #1: 初始选择后，当前总时长=15.80秒
2025-07-29 20:06:19,400 - INFO - 方案 #1: 额外between选择后，当前总时长=15.80秒
2025-07-29 20:06:19,400 - INFO - 方案 #1: 场景总时长(15.80秒)大于音频时长(2.92秒)，需要裁剪
2025-07-29 20:06:19,400 - INFO - 调整前总时长: 15.80秒, 目标时长: 2.92秒
2025-07-29 20:06:19,400 - INFO - 需要裁剪 12.87秒
2025-07-29 20:06:19,400 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:19,400 - INFO - 裁剪场景ID=942：从13.36秒裁剪至4.01秒
2025-07-29 20:06:19,400 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 3.52秒
2025-07-29 20:06:19,400 - INFO - 移除场景ID=943，时长=2.44秒
2025-07-29 20:06:19,400 - INFO - 调整后总时长: 4.01秒，与目标时长差异: 1.08秒
2025-07-29 20:06:19,400 - INFO - 方案 #1 调整/填充后最终总时长: 4.01秒
2025-07-29 20:06:19,400 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:19,400 - INFO - ========== 当前模式：字幕 #44 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:19,400 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:19,400 - INFO - ========== 新模式：字幕 #44 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:19,400 - INFO - 
----- 处理字幕 #44 的方案 #1 -----
2025-07-29 20:06:19,400 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 20:06:19,401 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps1ei_f5p
2025-07-29 20:06:19,401 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\943.mp4 (确认存在: True)
2025-07-29 20:06:19,401 - INFO - 添加场景ID=943，时长=2.44秒，累计时长=2.44秒
2025-07-29 20:06:19,401 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\944.mp4 (确认存在: True)
2025-07-29 20:06:19,401 - INFO - 添加场景ID=944，时长=1.36秒，累计时长=3.80秒
2025-07-29 20:06:19,402 - INFO - 准备合并 2 个场景文件，总时长约 3.80秒
2025-07-29 20:06:19,402 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/943.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/944.mp4'

2025-07-29 20:06:19,402 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps1ei_f5p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps1ei_f5p\temp_combined.mp4
2025-07-29 20:06:19,519 - INFO - 合并后的视频时长: 3.85秒，目标音频时长: 2.92秒
2025-07-29 20:06:19,519 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps1ei_f5p\temp_combined.mp4 -ss 0 -to 2.925 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 20:06:19,759 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:19,759 - INFO - 目标音频时长: 2.92秒
2025-07-29 20:06:19,759 - INFO - 实际视频时长: 2.98秒
2025-07-29 20:06:19,759 - INFO - 时长差异: 0.06秒 (1.98%)
2025-07-29 20:06:19,759 - INFO - ==========================================
2025-07-29 20:06:19,759 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:19,759 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 20:06:19,759 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps1ei_f5p
2025-07-29 20:06:19,803 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:19,803 - INFO -   - 音频时长: 2.92秒
2025-07-29 20:06:19,803 - INFO -   - 视频时长: 2.98秒
2025-07-29 20:06:19,803 - INFO -   - 时长差异: 0.06秒 (1.98%)
2025-07-29 20:06:19,803 - INFO - 
----- 处理字幕 #44 的方案 #2 -----
2025-07-29 20:06:19,803 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 20:06:19,803 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt08wf7mt
2025-07-29 20:06:19,804 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\942.mp4 (确认存在: True)
2025-07-29 20:06:19,804 - INFO - 添加场景ID=942，时长=13.36秒，累计时长=13.36秒
2025-07-29 20:06:19,804 - INFO - 场景总时长(13.36秒)已达到音频时长(2.92秒)的1.5倍，停止添加场景
2025-07-29 20:06:19,804 - INFO - 准备合并 1 个场景文件，总时长约 13.36秒
2025-07-29 20:06:19,804 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/942.mp4'

2025-07-29 20:06:19,804 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt08wf7mt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt08wf7mt\temp_combined.mp4
2025-07-29 20:06:19,919 - INFO - 合并后的视频时长: 13.38秒，目标音频时长: 2.92秒
2025-07-29 20:06:19,919 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt08wf7mt\temp_combined.mp4 -ss 0 -to 2.925 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 20:06:20,167 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:20,167 - INFO - 目标音频时长: 2.92秒
2025-07-29 20:06:20,167 - INFO - 实际视频时长: 2.98秒
2025-07-29 20:06:20,167 - INFO - 时长差异: 0.06秒 (1.98%)
2025-07-29 20:06:20,167 - INFO - ==========================================
2025-07-29 20:06:20,167 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:20,167 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 20:06:20,169 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt08wf7mt
2025-07-29 20:06:20,211 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:20,211 - INFO -   - 音频时长: 2.92秒
2025-07-29 20:06:20,211 - INFO -   - 视频时长: 2.98秒
2025-07-29 20:06:20,211 - INFO -   - 时长差异: 0.06秒 (1.98%)
2025-07-29 20:06:20,211 - INFO - 
字幕 #44 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:20,211 - INFO - 生成的视频文件:
2025-07-29 20:06:20,211 - INFO -   1. F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 20:06:20,211 - INFO -   2. F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 20:06:20,211 - INFO - ========== 字幕 #44 处理结束 ==========

