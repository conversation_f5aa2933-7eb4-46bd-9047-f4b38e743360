2025-07-29 20:05:41,252 - INFO - ========== 字幕 #18 处理开始 ==========
2025-07-29 20:05:41,252 - INFO - 字幕内容: 她用尽最后的力气告诉哥哥，是自己当初撮合错了人，那个女人，是个坏人！
2025-07-29 20:05:41,252 - INFO - 字幕序号: [196, 201]
2025-07-29 20:05:41,252 - INFO - 音频文件详情:
2025-07-29 20:05:41,252 - INFO -   - 路径: output\18.wav
2025-07-29 20:05:41,252 - INFO -   - 时长: 4.27秒
2025-07-29 20:05:41,253 - INFO -   - 验证音频时长: 4.27秒
2025-07-29 20:05:41,253 - INFO - 字幕时间戳信息:
2025-07-29 20:05:41,253 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:41,253 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:41,253 - INFO -   - 根据生成的音频时长(4.27秒)已调整字幕时间戳
2025-07-29 20:05:41,253 - INFO - ========== 新模式：为字幕 #18 生成4套场景方案 ==========
2025-07-29 20:05:41,253 - INFO - 字幕序号列表: [196, 201]
2025-07-29 20:05:41,253 - INFO - 
--- 生成方案 #1：基于字幕序号 #196 ---
2025-07-29 20:05:41,253 - INFO - 开始为单个字幕序号 #196 匹配场景，目标时长: 4.27秒
2025-07-29 20:05:41,253 - INFO - 开始查找字幕序号 [196] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:41,253 - INFO - 找到related_overlap场景: scene_id=224, 字幕#196
2025-07-29 20:05:41,254 - INFO - 字幕 #196 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:41,254 - INFO - 字幕序号 #196 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:41,254 - INFO - 选择第一个overlap场景作为起点: scene_id=224
2025-07-29 20:05:41,254 - INFO - 添加起点场景: scene_id=224, 时长=4.00秒, 累计时长=4.00秒
2025-07-29 20:05:41,254 - INFO - 起点场景时长不足，需要延伸填充 0.27秒
2025-07-29 20:05:41,254 - INFO - 起点场景在原始列表中的索引: 223
2025-07-29 20:05:41,255 - INFO - 延伸添加场景: scene_id=225 (裁剪至 0.27秒)
2025-07-29 20:05:41,255 - INFO - 累计时长: 4.27秒
2025-07-29 20:05:41,255 - INFO - 字幕序号 #196 场景匹配完成，共选择 2 个场景，总时长: 4.27秒
2025-07-29 20:05:41,255 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:05:41,255 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:05:41,255 - INFO - 
--- 生成方案 #2：基于字幕序号 #201 ---
2025-07-29 20:05:41,255 - INFO - 开始为单个字幕序号 #201 匹配场景，目标时长: 4.27秒
2025-07-29 20:05:41,255 - INFO - 开始查找字幕序号 [201] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:41,255 - INFO - 找到related_overlap场景: scene_id=227, 字幕#201
2025-07-29 20:05:41,256 - INFO - 字幕 #201 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:41,256 - INFO - 字幕序号 #201 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:41,256 - INFO - 选择第一个overlap场景作为起点: scene_id=227
2025-07-29 20:05:41,256 - INFO - 添加起点场景: scene_id=227, 时长=2.32秒, 累计时长=2.32秒
2025-07-29 20:05:41,256 - INFO - 起点场景时长不足，需要延伸填充 1.95秒
2025-07-29 20:05:41,256 - INFO - 起点场景在原始列表中的索引: 226
2025-07-29 20:05:41,256 - INFO - 延伸添加场景: scene_id=228 (裁剪至 1.95秒)
2025-07-29 20:05:41,256 - INFO - 累计时长: 4.27秒
2025-07-29 20:05:41,256 - INFO - 字幕序号 #201 场景匹配完成，共选择 2 个场景，总时长: 4.27秒
2025-07-29 20:05:41,256 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:05:41,256 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:41,256 - INFO - ========== 当前模式：为字幕 #18 生成 1 套场景方案 ==========
2025-07-29 20:05:41,256 - INFO - 开始查找字幕序号 [196, 201] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:41,256 - INFO - 找到related_overlap场景: scene_id=224, 字幕#196
2025-07-29 20:05:41,256 - INFO - 找到related_overlap场景: scene_id=227, 字幕#201
2025-07-29 20:05:41,257 - INFO - 字幕 #196 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:41,257 - INFO - 字幕 #201 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:41,257 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:41,257 - INFO - 开始生成方案 #1
2025-07-29 20:05:41,257 - INFO - 方案 #1: 为字幕#196选择初始化overlap场景id=224
2025-07-29 20:05:41,257 - INFO - 方案 #1: 为字幕#201选择初始化overlap场景id=227
2025-07-29 20:05:41,257 - INFO - 方案 #1: 初始选择后，当前总时长=6.32秒
2025-07-29 20:05:41,257 - INFO - 方案 #1: 额外between选择后，当前总时长=6.32秒
2025-07-29 20:05:41,257 - INFO - 方案 #1: 场景总时长(6.32秒)大于音频时长(4.27秒)，需要裁剪
2025-07-29 20:05:41,257 - INFO - 调整前总时长: 6.32秒, 目标时长: 4.27秒
2025-07-29 20:05:41,257 - INFO - 需要裁剪 2.05秒
2025-07-29 20:05:41,257 - INFO - 裁剪最长场景ID=224：从4.00秒裁剪至1.95秒
2025-07-29 20:05:41,257 - INFO - 调整后总时长: 4.27秒，与目标时长差异: 0.00秒
2025-07-29 20:05:41,257 - INFO - 方案 #1 调整/填充后最终总时长: 4.27秒
2025-07-29 20:05:41,257 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:41,257 - INFO - ========== 当前模式：字幕 #18 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:41,257 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:41,257 - INFO - ========== 新模式：字幕 #18 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:41,258 - INFO - 
----- 处理字幕 #18 的方案 #1 -----
2025-07-29 20:05:41,258 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 20:05:41,258 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxqp70mx7
2025-07-29 20:05:41,259 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\224.mp4 (确认存在: True)
2025-07-29 20:05:41,259 - INFO - 添加场景ID=224，时长=4.00秒，累计时长=4.00秒
2025-07-29 20:05:41,259 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\225.mp4 (确认存在: True)
2025-07-29 20:05:41,259 - INFO - 添加场景ID=225，时长=1.96秒，累计时长=5.96秒
2025-07-29 20:05:41,259 - INFO - 准备合并 2 个场景文件，总时长约 5.96秒
2025-07-29 20:05:41,259 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/224.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/225.mp4'

2025-07-29 20:05:41,259 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxqp70mx7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxqp70mx7\temp_combined.mp4
2025-07-29 20:05:41,397 - INFO - 合并后的视频时长: 6.01秒，目标音频时长: 4.27秒
2025-07-29 20:05:41,397 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxqp70mx7\temp_combined.mp4 -ss 0 -to 4.272 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 20:05:41,677 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:41,677 - INFO - 目标音频时长: 4.27秒
2025-07-29 20:05:41,677 - INFO - 实际视频时长: 4.30秒
2025-07-29 20:05:41,677 - INFO - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:05:41,677 - INFO - ==========================================
2025-07-29 20:05:41,677 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:41,677 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 20:05:41,677 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxqp70mx7
2025-07-29 20:05:41,722 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:41,722 - INFO -   - 音频时长: 4.27秒
2025-07-29 20:05:41,722 - INFO -   - 视频时长: 4.30秒
2025-07-29 20:05:41,722 - INFO -   - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:05:41,722 - INFO - 
----- 处理字幕 #18 的方案 #2 -----
2025-07-29 20:05:41,722 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 20:05:41,723 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnc0bls28
2025-07-29 20:05:41,723 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\227.mp4 (确认存在: True)
2025-07-29 20:05:41,723 - INFO - 添加场景ID=227，时长=2.32秒，累计时长=2.32秒
2025-07-29 20:05:41,723 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\228.mp4 (确认存在: True)
2025-07-29 20:05:41,723 - INFO - 添加场景ID=228，时长=2.36秒，累计时长=4.68秒
2025-07-29 20:05:41,723 - INFO - 准备合并 2 个场景文件，总时长约 4.68秒
2025-07-29 20:05:41,724 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/227.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/228.mp4'

2025-07-29 20:05:41,724 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnc0bls28\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnc0bls28\temp_combined.mp4
2025-07-29 20:05:41,873 - INFO - 合并后的视频时长: 4.73秒，目标音频时长: 4.27秒
2025-07-29 20:05:41,873 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnc0bls28\temp_combined.mp4 -ss 0 -to 4.272 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 20:05:42,176 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:42,176 - INFO - 目标音频时长: 4.27秒
2025-07-29 20:05:42,176 - INFO - 实际视频时长: 4.30秒
2025-07-29 20:05:42,176 - INFO - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:05:42,176 - INFO - ==========================================
2025-07-29 20:05:42,176 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:42,176 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 20:05:42,176 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnc0bls28
2025-07-29 20:05:42,223 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:42,223 - INFO -   - 音频时长: 4.27秒
2025-07-29 20:05:42,223 - INFO -   - 视频时长: 4.30秒
2025-07-29 20:05:42,223 - INFO -   - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:05:42,223 - INFO - 
----- 处理字幕 #18 的方案 #3 -----
2025-07-29 20:05:42,223 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 20:05:42,223 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplnshd__m
2025-07-29 20:05:42,224 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\224.mp4 (确认存在: True)
2025-07-29 20:05:42,224 - INFO - 添加场景ID=224，时长=4.00秒，累计时长=4.00秒
2025-07-29 20:05:42,224 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\227.mp4 (确认存在: True)
2025-07-29 20:05:42,224 - INFO - 添加场景ID=227，时长=2.32秒，累计时长=6.32秒
2025-07-29 20:05:42,224 - INFO - 准备合并 2 个场景文件，总时长约 6.32秒
2025-07-29 20:05:42,224 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/224.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/227.mp4'

2025-07-29 20:05:42,224 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplnshd__m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplnshd__m\temp_combined.mp4
2025-07-29 20:05:42,343 - INFO - 合并后的视频时长: 6.37秒，目标音频时长: 4.27秒
2025-07-29 20:05:42,343 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplnshd__m\temp_combined.mp4 -ss 0 -to 4.272 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 20:05:42,617 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:42,617 - INFO - 目标音频时长: 4.27秒
2025-07-29 20:05:42,617 - INFO - 实际视频时长: 4.30秒
2025-07-29 20:05:42,617 - INFO - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:05:42,617 - INFO - ==========================================
2025-07-29 20:05:42,617 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:42,617 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 20:05:42,618 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplnshd__m
2025-07-29 20:05:42,661 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:42,661 - INFO -   - 音频时长: 4.27秒
2025-07-29 20:05:42,661 - INFO -   - 视频时长: 4.30秒
2025-07-29 20:05:42,661 - INFO -   - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:05:42,662 - INFO - 
字幕 #18 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:42,662 - INFO - 生成的视频文件:
2025-07-29 20:05:42,662 - INFO -   1. F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 20:05:42,662 - INFO -   2. F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 20:05:42,662 - INFO -   3. F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 20:05:42,662 - INFO - ========== 字幕 #18 处理结束 ==========

