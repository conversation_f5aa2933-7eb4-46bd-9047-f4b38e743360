2025-07-29 20:05:43,971 - INFO - ========== 字幕 #20 处理开始 ==========
2025-07-29 20:05:43,971 - INFO - 字幕内容: 男人的世界瞬间崩塌，他抱着妹妹冰冷的身体，发出了撕心裂肺的哀嚎。
2025-07-29 20:05:43,971 - INFO - 字幕序号: [209, 212]
2025-07-29 20:05:43,972 - INFO - 音频文件详情:
2025-07-29 20:05:43,972 - INFO -   - 路径: output\20.wav
2025-07-29 20:05:43,972 - INFO -   - 时长: 4.21秒
2025-07-29 20:05:43,972 - INFO -   - 验证音频时长: 4.21秒
2025-07-29 20:05:43,972 - INFO - 字幕时间戳信息:
2025-07-29 20:05:43,972 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:43,972 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:43,972 - INFO -   - 根据生成的音频时长(4.21秒)已调整字幕时间戳
2025-07-29 20:05:43,972 - INFO - ========== 新模式：为字幕 #20 生成4套场景方案 ==========
2025-07-29 20:05:43,972 - INFO - 字幕序号列表: [209, 212]
2025-07-29 20:05:43,972 - INFO - 
--- 生成方案 #1：基于字幕序号 #209 ---
2025-07-29 20:05:43,972 - INFO - 开始为单个字幕序号 #209 匹配场景，目标时长: 4.21秒
2025-07-29 20:05:43,972 - INFO - 开始查找字幕序号 [209] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:43,972 - INFO - 找到related_overlap场景: scene_id=234, 字幕#209
2025-07-29 20:05:43,973 - INFO - 找到related_between场景: scene_id=233, 字幕#209
2025-07-29 20:05:43,973 - INFO - 找到related_between场景: scene_id=235, 字幕#209
2025-07-29 20:05:43,974 - INFO - 字幕 #209 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:43,974 - INFO - 字幕序号 #209 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:05:43,974 - INFO - 选择第一个overlap场景作为起点: scene_id=234
2025-07-29 20:05:43,974 - INFO - 添加起点场景: scene_id=234, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 20:05:43,974 - INFO - 起点场景时长不足，需要延伸填充 2.33秒
2025-07-29 20:05:43,974 - INFO - 起点场景在原始列表中的索引: 233
2025-07-29 20:05:43,974 - INFO - 延伸添加场景: scene_id=235 (完整时长 1.44秒)
2025-07-29 20:05:43,974 - INFO - 累计时长: 3.32秒
2025-07-29 20:05:43,974 - INFO - 延伸添加场景: scene_id=236 (裁剪至 0.89秒)
2025-07-29 20:05:43,974 - INFO - 累计时长: 4.21秒
2025-07-29 20:05:43,974 - INFO - 字幕序号 #209 场景匹配完成，共选择 3 个场景，总时长: 4.21秒
2025-07-29 20:05:43,974 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:43,974 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:43,974 - INFO - 
--- 生成方案 #2：基于字幕序号 #212 ---
2025-07-29 20:05:43,974 - INFO - 开始为单个字幕序号 #212 匹配场景，目标时长: 4.21秒
2025-07-29 20:05:43,974 - INFO - 开始查找字幕序号 [212] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:43,974 - INFO - 找到related_overlap场景: scene_id=238, 字幕#212
2025-07-29 20:05:43,975 - INFO - 找到related_between场景: scene_id=239, 字幕#212
2025-07-29 20:05:43,975 - INFO - 字幕 #212 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:43,975 - INFO - 字幕序号 #212 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:43,975 - INFO - 选择第一个overlap场景作为起点: scene_id=238
2025-07-29 20:05:43,975 - INFO - 添加起点场景: scene_id=238, 时长=1.84秒, 累计时长=1.84秒
2025-07-29 20:05:43,975 - INFO - 起点场景时长不足，需要延伸填充 2.37秒
2025-07-29 20:05:43,975 - INFO - 起点场景在原始列表中的索引: 237
2025-07-29 20:05:43,975 - INFO - 延伸添加场景: scene_id=239 (完整时长 1.44秒)
2025-07-29 20:05:43,975 - INFO - 累计时长: 3.28秒
2025-07-29 20:05:43,975 - INFO - 延伸添加场景: scene_id=240 (裁剪至 0.93秒)
2025-07-29 20:05:43,975 - INFO - 累计时长: 4.21秒
2025-07-29 20:05:43,975 - INFO - 字幕序号 #212 场景匹配完成，共选择 3 个场景，总时长: 4.21秒
2025-07-29 20:05:43,975 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:43,975 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:43,975 - INFO - ========== 当前模式：为字幕 #20 生成 1 套场景方案 ==========
2025-07-29 20:05:43,975 - INFO - 开始查找字幕序号 [209, 212] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:43,975 - INFO - 找到related_overlap场景: scene_id=234, 字幕#209
2025-07-29 20:05:43,975 - INFO - 找到related_overlap场景: scene_id=238, 字幕#212
2025-07-29 20:05:43,976 - INFO - 找到related_between场景: scene_id=233, 字幕#209
2025-07-29 20:05:43,976 - INFO - 找到related_between场景: scene_id=235, 字幕#209
2025-07-29 20:05:43,976 - INFO - 找到related_between场景: scene_id=239, 字幕#212
2025-07-29 20:05:43,976 - INFO - 字幕 #209 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:43,976 - INFO - 字幕 #212 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:43,976 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:05:43,977 - INFO - 开始生成方案 #1
2025-07-29 20:05:43,977 - INFO - 方案 #1: 为字幕#209选择初始化overlap场景id=234
2025-07-29 20:05:43,977 - INFO - 方案 #1: 为字幕#212选择初始化overlap场景id=238
2025-07-29 20:05:43,977 - INFO - 方案 #1: 初始选择后，当前总时长=3.72秒
2025-07-29 20:05:43,977 - INFO - 方案 #1: 额外between选择后，当前总时长=3.72秒
2025-07-29 20:05:43,977 - INFO - 方案 #1: 额外添加between场景id=239, 当前总时长=5.16秒
2025-07-29 20:05:43,977 - INFO - 方案 #1: 场景总时长(5.16秒)大于音频时长(4.21秒)，需要裁剪
2025-07-29 20:05:43,977 - INFO - 调整前总时长: 5.16秒, 目标时长: 4.21秒
2025-07-29 20:05:43,977 - INFO - 需要裁剪 0.95秒
2025-07-29 20:05:43,977 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:05:43,977 - INFO - 裁剪场景ID=234：从1.88秒裁剪至1.00秒
2025-07-29 20:05:43,977 - INFO - 裁剪场景ID=238：从1.84秒裁剪至1.77秒
2025-07-29 20:05:43,977 - INFO - 调整后总时长: 4.21秒，与目标时长差异: 0.00秒
2025-07-29 20:05:43,977 - INFO - 方案 #1 调整/填充后最终总时长: 4.21秒
2025-07-29 20:05:43,977 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:43,977 - INFO - ========== 当前模式：字幕 #20 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:43,977 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:43,977 - INFO - ========== 新模式：字幕 #20 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:43,977 - INFO - 
----- 处理字幕 #20 的方案 #1 -----
2025-07-29 20:05:43,977 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 20:05:43,977 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpunyw6qy3
2025-07-29 20:05:43,978 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\234.mp4 (确认存在: True)
2025-07-29 20:05:43,978 - INFO - 添加场景ID=234，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:05:43,978 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\235.mp4 (确认存在: True)
2025-07-29 20:05:43,978 - INFO - 添加场景ID=235，时长=1.44秒，累计时长=3.32秒
2025-07-29 20:05:43,978 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\236.mp4 (确认存在: True)
2025-07-29 20:05:43,978 - INFO - 添加场景ID=236，时长=1.16秒，累计时长=4.48秒
2025-07-29 20:05:43,978 - INFO - 准备合并 3 个场景文件，总时长约 4.48秒
2025-07-29 20:05:43,978 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/234.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/235.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/236.mp4'

2025-07-29 20:05:43,978 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpunyw6qy3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpunyw6qy3\temp_combined.mp4
2025-07-29 20:05:44,122 - INFO - 合并后的视频时长: 4.55秒，目标音频时长: 4.21秒
2025-07-29 20:05:44,122 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpunyw6qy3\temp_combined.mp4 -ss 0 -to 4.209 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 20:05:44,411 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:44,411 - INFO - 目标音频时长: 4.21秒
2025-07-29 20:05:44,411 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:44,411 - INFO - 时长差异: 0.05秒 (1.28%)
2025-07-29 20:05:44,411 - INFO - ==========================================
2025-07-29 20:05:44,411 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:44,411 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 20:05:44,411 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpunyw6qy3
2025-07-29 20:05:44,456 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:44,456 - INFO -   - 音频时长: 4.21秒
2025-07-29 20:05:44,456 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:44,456 - INFO -   - 时长差异: 0.05秒 (1.28%)
2025-07-29 20:05:44,456 - INFO - 
----- 处理字幕 #20 的方案 #2 -----
2025-07-29 20:05:44,456 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 20:05:44,457 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpicscznbt
2025-07-29 20:05:44,457 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\238.mp4 (确认存在: True)
2025-07-29 20:05:44,457 - INFO - 添加场景ID=238，时长=1.84秒，累计时长=1.84秒
2025-07-29 20:05:44,457 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\239.mp4 (确认存在: True)
2025-07-29 20:05:44,457 - INFO - 添加场景ID=239，时长=1.44秒，累计时长=3.28秒
2025-07-29 20:05:44,457 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\240.mp4 (确认存在: True)
2025-07-29 20:05:44,457 - INFO - 添加场景ID=240，时长=1.48秒，累计时长=4.76秒
2025-07-29 20:05:44,457 - INFO - 准备合并 3 个场景文件，总时长约 4.76秒
2025-07-29 20:05:44,457 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/238.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/239.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/240.mp4'

2025-07-29 20:05:44,458 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpicscznbt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpicscznbt\temp_combined.mp4
2025-07-29 20:05:44,601 - INFO - 合并后的视频时长: 4.83秒，目标音频时长: 4.21秒
2025-07-29 20:05:44,601 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpicscznbt\temp_combined.mp4 -ss 0 -to 4.209 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 20:05:44,903 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:44,903 - INFO - 目标音频时长: 4.21秒
2025-07-29 20:05:44,903 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:44,903 - INFO - 时长差异: 0.05秒 (1.28%)
2025-07-29 20:05:44,903 - INFO - ==========================================
2025-07-29 20:05:44,903 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:44,903 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 20:05:44,904 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpicscznbt
2025-07-29 20:05:44,948 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:44,948 - INFO -   - 音频时长: 4.21秒
2025-07-29 20:05:44,948 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:44,948 - INFO -   - 时长差异: 0.05秒 (1.28%)
2025-07-29 20:05:44,948 - INFO - 
----- 处理字幕 #20 的方案 #3 -----
2025-07-29 20:05:44,948 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 20:05:44,948 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplm7z8wt3
2025-07-29 20:05:44,950 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\234.mp4 (确认存在: True)
2025-07-29 20:05:44,950 - INFO - 添加场景ID=234，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:05:44,950 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\238.mp4 (确认存在: True)
2025-07-29 20:05:44,950 - INFO - 添加场景ID=238，时长=1.84秒，累计时长=3.72秒
2025-07-29 20:05:44,950 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\239.mp4 (确认存在: True)
2025-07-29 20:05:44,950 - INFO - 添加场景ID=239，时长=1.44秒，累计时长=5.16秒
2025-07-29 20:05:44,950 - INFO - 准备合并 3 个场景文件，总时长约 5.16秒
2025-07-29 20:05:44,950 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/234.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/238.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/239.mp4'

2025-07-29 20:05:44,950 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplm7z8wt3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplm7z8wt3\temp_combined.mp4
2025-07-29 20:05:45,073 - INFO - 合并后的视频时长: 5.22秒，目标音频时长: 4.21秒
2025-07-29 20:05:45,073 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplm7z8wt3\temp_combined.mp4 -ss 0 -to 4.209 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 20:05:45,365 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:45,365 - INFO - 目标音频时长: 4.21秒
2025-07-29 20:05:45,365 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:45,365 - INFO - 时长差异: 0.05秒 (1.28%)
2025-07-29 20:05:45,365 - INFO - ==========================================
2025-07-29 20:05:45,365 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:45,365 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 20:05:45,366 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplm7z8wt3
2025-07-29 20:05:45,412 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:45,412 - INFO -   - 音频时长: 4.21秒
2025-07-29 20:05:45,412 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:45,412 - INFO -   - 时长差异: 0.05秒 (1.28%)
2025-07-29 20:05:45,412 - INFO - 
字幕 #20 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:45,412 - INFO - 生成的视频文件:
2025-07-29 20:05:45,412 - INFO -   1. F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 20:05:45,412 - INFO -   2. F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 20:05:45,412 - INFO -   3. F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 20:05:45,412 - INFO - ========== 字幕 #20 处理结束 ==========

