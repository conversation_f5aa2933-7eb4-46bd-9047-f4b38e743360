2025-07-29 20:06:33,099 - INFO - ========== 字幕 #53 处理开始 ==========
2025-07-29 20:06:33,099 - INFO - 字幕内容: 情急之下，女人拿出了男人七年前写的那份计划书，希望能挽回局面。
2025-07-29 20:06:33,099 - INFO - 字幕序号: [2177, 2179]
2025-07-29 20:06:33,099 - INFO - 音频文件详情:
2025-07-29 20:06:33,099 - INFO -   - 路径: output\53.wav
2025-07-29 20:06:33,099 - INFO -   - 时长: 5.05秒
2025-07-29 20:06:33,099 - INFO -   - 验证音频时长: 5.05秒
2025-07-29 20:06:33,099 - INFO - 字幕时间戳信息:
2025-07-29 20:06:33,099 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:33,099 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:33,099 - INFO -   - 根据生成的音频时长(5.05秒)已调整字幕时间戳
2025-07-29 20:06:33,099 - INFO - ========== 新模式：为字幕 #53 生成4套场景方案 ==========
2025-07-29 20:06:33,099 - INFO - 字幕序号列表: [2177, 2179]
2025-07-29 20:06:33,099 - INFO - 
--- 生成方案 #1：基于字幕序号 #2177 ---
2025-07-29 20:06:33,100 - INFO - 开始为单个字幕序号 #2177 匹配场景，目标时长: 5.05秒
2025-07-29 20:06:33,100 - INFO - 开始查找字幕序号 [2177] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:33,100 - INFO - 找到related_overlap场景: scene_id=2135, 字幕#2177
2025-07-29 20:06:33,101 - INFO - 字幕 #2177 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:33,101 - INFO - 字幕序号 #2177 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:33,101 - INFO - 选择第一个overlap场景作为起点: scene_id=2135
2025-07-29 20:06:33,101 - INFO - 添加起点场景: scene_id=2135, 时长=1.00秒, 累计时长=1.00秒
2025-07-29 20:06:33,101 - INFO - 起点场景时长不足，需要延伸填充 4.05秒
2025-07-29 20:06:33,101 - INFO - 起点场景在原始列表中的索引: 2134
2025-07-29 20:06:33,101 - INFO - 延伸添加场景: scene_id=2136 (完整时长 0.96秒)
2025-07-29 20:06:33,101 - INFO - 累计时长: 1.96秒
2025-07-29 20:06:33,101 - INFO - 延伸添加场景: scene_id=2137 (完整时长 0.96秒)
2025-07-29 20:06:33,101 - INFO - 累计时长: 2.92秒
2025-07-29 20:06:33,101 - INFO - 延伸添加场景: scene_id=2138 (完整时长 1.56秒)
2025-07-29 20:06:33,101 - INFO - 累计时长: 4.48秒
2025-07-29 20:06:33,101 - INFO - 延伸添加场景: scene_id=2139 (裁剪至 0.57秒)
2025-07-29 20:06:33,101 - INFO - 累计时长: 5.05秒
2025-07-29 20:06:33,101 - INFO - 字幕序号 #2177 场景匹配完成，共选择 5 个场景，总时长: 5.05秒
2025-07-29 20:06:33,101 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:06:33,101 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:06:33,101 - INFO - 
--- 生成方案 #2：基于字幕序号 #2179 ---
2025-07-29 20:06:33,101 - INFO - 开始为单个字幕序号 #2179 匹配场景，目标时长: 5.05秒
2025-07-29 20:06:33,101 - INFO - 开始查找字幕序号 [2179] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:33,102 - INFO - 找到related_overlap场景: scene_id=2137, 字幕#2179
2025-07-29 20:06:33,102 - INFO - 找到related_overlap场景: scene_id=2138, 字幕#2179
2025-07-29 20:06:33,103 - INFO - 字幕 #2179 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:33,103 - INFO - 字幕序号 #2179 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:33,103 - ERROR - 字幕序号 #2179 没有找到任何可用的匹配场景
2025-07-29 20:06:33,103 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:33,103 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:33,103 - INFO - ========== 当前模式：为字幕 #53 生成 1 套场景方案 ==========
2025-07-29 20:06:33,103 - INFO - 开始查找字幕序号 [2177, 2179] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:33,103 - INFO - 找到related_overlap场景: scene_id=2135, 字幕#2177
2025-07-29 20:06:33,103 - INFO - 找到related_overlap场景: scene_id=2137, 字幕#2179
2025-07-29 20:06:33,103 - INFO - 找到related_overlap场景: scene_id=2138, 字幕#2179
2025-07-29 20:06:33,104 - INFO - 字幕 #2177 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:33,104 - INFO - 字幕 #2179 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:33,104 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:33,104 - INFO - 开始生成方案 #1
2025-07-29 20:06:33,104 - INFO - 方案 #1: 为字幕#2177选择初始化overlap场景id=2135
2025-07-29 20:06:33,104 - INFO - 方案 #1: 为字幕#2179选择初始化overlap场景id=2137
2025-07-29 20:06:33,104 - INFO - 方案 #1: 初始选择后，当前总时长=1.96秒
2025-07-29 20:06:33,104 - INFO - 方案 #1: 额外添加overlap场景id=2138, 当前总时长=3.52秒
2025-07-29 20:06:33,104 - INFO - 方案 #1: 额外between选择后，当前总时长=3.52秒
2025-07-29 20:06:33,104 - INFO - 方案 #1: 场景总时长(3.52秒)小于音频时长(5.05秒)，需要延伸填充
2025-07-29 20:06:33,104 - INFO - 方案 #1: 最后一个场景ID: 2138
2025-07-29 20:06:33,104 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2137
2025-07-29 20:06:33,104 - INFO - 方案 #1: 需要填充时长: 1.53秒
2025-07-29 20:06:33,104 - INFO - 方案 #1: 追加场景 scene_id=2139 (裁剪至 1.53秒)
2025-07-29 20:06:33,104 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:33,104 - INFO - 方案 #1 调整/填充后最终总时长: 5.05秒
2025-07-29 20:06:33,104 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:33,104 - INFO - ========== 当前模式：字幕 #53 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:33,104 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:33,104 - INFO - ========== 新模式：字幕 #53 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:33,104 - INFO - 
----- 处理字幕 #53 的方案 #1 -----
2025-07-29 20:06:33,104 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 20:06:33,105 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi5nmflra
2025-07-29 20:06:33,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2135.mp4 (确认存在: True)
2025-07-29 20:06:33,105 - INFO - 添加场景ID=2135，时长=1.00秒，累计时长=1.00秒
2025-07-29 20:06:33,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2136.mp4 (确认存在: True)
2025-07-29 20:06:33,105 - INFO - 添加场景ID=2136，时长=0.96秒，累计时长=1.96秒
2025-07-29 20:06:33,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2137.mp4 (确认存在: True)
2025-07-29 20:06:33,105 - INFO - 添加场景ID=2137，时长=0.96秒，累计时长=2.92秒
2025-07-29 20:06:33,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2138.mp4 (确认存在: True)
2025-07-29 20:06:33,105 - INFO - 添加场景ID=2138，时长=1.56秒，累计时长=4.48秒
2025-07-29 20:06:33,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2139.mp4 (确认存在: True)
2025-07-29 20:06:33,105 - INFO - 添加场景ID=2139，时长=2.80秒，累计时长=7.28秒
2025-07-29 20:06:33,106 - INFO - 准备合并 5 个场景文件，总时长约 7.28秒
2025-07-29 20:06:33,106 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2135.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2136.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2137.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2138.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2139.mp4'

2025-07-29 20:06:33,106 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpi5nmflra\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpi5nmflra\temp_combined.mp4
2025-07-29 20:06:33,287 - INFO - 合并后的视频时长: 7.40秒，目标音频时长: 5.05秒
2025-07-29 20:06:33,287 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpi5nmflra\temp_combined.mp4 -ss 0 -to 5.052 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 20:06:33,617 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:33,617 - INFO - 目标音频时长: 5.05秒
2025-07-29 20:06:33,617 - INFO - 实际视频时长: 5.10秒
2025-07-29 20:06:33,617 - INFO - 时长差异: 0.05秒 (1.01%)
2025-07-29 20:06:33,617 - INFO - ==========================================
2025-07-29 20:06:33,617 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:33,617 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 20:06:33,617 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi5nmflra
2025-07-29 20:06:33,663 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:33,663 - INFO -   - 音频时长: 5.05秒
2025-07-29 20:06:33,663 - INFO -   - 视频时长: 5.10秒
2025-07-29 20:06:33,663 - INFO -   - 时长差异: 0.05秒 (1.01%)
2025-07-29 20:06:33,663 - INFO - 
----- 处理字幕 #53 的方案 #2 -----
2025-07-29 20:06:33,663 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 20:06:33,664 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd336ji2k
2025-07-29 20:06:33,664 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2135.mp4 (确认存在: True)
2025-07-29 20:06:33,664 - INFO - 添加场景ID=2135，时长=1.00秒，累计时长=1.00秒
2025-07-29 20:06:33,664 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2137.mp4 (确认存在: True)
2025-07-29 20:06:33,664 - INFO - 添加场景ID=2137，时长=0.96秒，累计时长=1.96秒
2025-07-29 20:06:33,664 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2138.mp4 (确认存在: True)
2025-07-29 20:06:33,664 - INFO - 添加场景ID=2138，时长=1.56秒，累计时长=3.52秒
2025-07-29 20:06:33,665 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2139.mp4 (确认存在: True)
2025-07-29 20:06:33,665 - INFO - 添加场景ID=2139，时长=2.80秒，累计时长=6.32秒
2025-07-29 20:06:33,665 - INFO - 准备合并 4 个场景文件，总时长约 6.32秒
2025-07-29 20:06:33,665 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2135.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2137.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2138.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2139.mp4'

2025-07-29 20:06:33,665 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpd336ji2k\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpd336ji2k\temp_combined.mp4
2025-07-29 20:06:33,824 - INFO - 合并后的视频时长: 6.41秒，目标音频时长: 5.05秒
2025-07-29 20:06:33,824 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpd336ji2k\temp_combined.mp4 -ss 0 -to 5.052 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 20:06:34,176 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:34,176 - INFO - 目标音频时长: 5.05秒
2025-07-29 20:06:34,176 - INFO - 实际视频时长: 5.10秒
2025-07-29 20:06:34,176 - INFO - 时长差异: 0.05秒 (1.01%)
2025-07-29 20:06:34,176 - INFO - ==========================================
2025-07-29 20:06:34,176 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:34,176 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 20:06:34,177 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd336ji2k
2025-07-29 20:06:34,223 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:34,223 - INFO -   - 音频时长: 5.05秒
2025-07-29 20:06:34,223 - INFO -   - 视频时长: 5.10秒
2025-07-29 20:06:34,223 - INFO -   - 时长差异: 0.05秒 (1.01%)
2025-07-29 20:06:34,223 - INFO - 
字幕 #53 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:34,223 - INFO - 生成的视频文件:
2025-07-29 20:06:34,223 - INFO -   1. F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 20:06:34,223 - INFO -   2. F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 20:06:34,223 - INFO - ========== 字幕 #53 处理结束 ==========

