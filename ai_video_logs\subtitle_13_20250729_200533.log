2025-07-29 20:05:33,212 - INFO - ========== 字幕 #13 处理开始 ==========
2025-07-29 20:05:33,213 - INFO - 字幕内容: 她一声令下，保安如狼似虎地冲了上来，将男人无情地架了出去，任凭他如何嘶喊也无动于衷。
2025-07-29 20:05:33,213 - INFO - 字幕序号: [182, 187]
2025-07-29 20:05:33,213 - INFO - 音频文件详情:
2025-07-29 20:05:33,213 - INFO -   - 路径: output\13.wav
2025-07-29 20:05:33,213 - INFO -   - 时长: 5.11秒
2025-07-29 20:05:33,213 - INFO -   - 验证音频时长: 5.11秒
2025-07-29 20:05:33,213 - INFO - 字幕时间戳信息:
2025-07-29 20:05:33,213 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:33,213 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:33,213 - INFO -   - 根据生成的音频时长(5.11秒)已调整字幕时间戳
2025-07-29 20:05:33,213 - INFO - ========== 新模式：为字幕 #13 生成4套场景方案 ==========
2025-07-29 20:05:33,213 - INFO - 字幕序号列表: [182, 187]
2025-07-29 20:05:33,213 - INFO - 
--- 生成方案 #1：基于字幕序号 #182 ---
2025-07-29 20:05:33,213 - INFO - 开始为单个字幕序号 #182 匹配场景，目标时长: 5.11秒
2025-07-29 20:05:33,213 - INFO - 开始查找字幕序号 [182] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:33,214 - INFO - 找到related_overlap场景: scene_id=203, 字幕#182
2025-07-29 20:05:33,214 - INFO - 找到related_overlap场景: scene_id=204, 字幕#182
2025-07-29 20:05:33,215 - INFO - 字幕 #182 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:33,215 - INFO - 字幕序号 #182 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:33,215 - INFO - 选择第一个overlap场景作为起点: scene_id=203
2025-07-29 20:05:33,215 - INFO - 添加起点场景: scene_id=203, 时长=2.36秒, 累计时长=2.36秒
2025-07-29 20:05:33,215 - INFO - 起点场景时长不足，需要延伸填充 2.75秒
2025-07-29 20:05:33,215 - INFO - 起点场景在原始列表中的索引: 202
2025-07-29 20:05:33,215 - INFO - 延伸添加场景: scene_id=204 (完整时长 1.36秒)
2025-07-29 20:05:33,215 - INFO - 累计时长: 3.72秒
2025-07-29 20:05:33,215 - INFO - 延伸添加场景: scene_id=205 (裁剪至 1.39秒)
2025-07-29 20:05:33,215 - INFO - 累计时长: 5.11秒
2025-07-29 20:05:33,215 - INFO - 字幕序号 #182 场景匹配完成，共选择 3 个场景，总时长: 5.11秒
2025-07-29 20:05:33,215 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:33,215 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:33,215 - INFO - 
--- 生成方案 #2：基于字幕序号 #187 ---
2025-07-29 20:05:33,215 - INFO - 开始为单个字幕序号 #187 匹配场景，目标时长: 5.11秒
2025-07-29 20:05:33,215 - INFO - 开始查找字幕序号 [187] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:33,215 - INFO - 找到related_overlap场景: scene_id=211, 字幕#187
2025-07-29 20:05:33,215 - INFO - 找到related_overlap场景: scene_id=212, 字幕#187
2025-07-29 20:05:33,216 - INFO - 找到related_between场景: scene_id=213, 字幕#187
2025-07-29 20:05:33,216 - INFO - 找到related_between场景: scene_id=214, 字幕#187
2025-07-29 20:05:33,217 - INFO - 字幕 #187 找到 2 个overlap场景, 2 个between场景
2025-07-29 20:05:33,217 - INFO - 字幕序号 #187 找到 2 个可用overlap场景, 2 个可用between场景
2025-07-29 20:05:33,217 - INFO - 选择第一个overlap场景作为起点: scene_id=211
2025-07-29 20:05:33,217 - INFO - 添加起点场景: scene_id=211, 时长=1.16秒, 累计时长=1.16秒
2025-07-29 20:05:33,217 - INFO - 起点场景时长不足，需要延伸填充 3.95秒
2025-07-29 20:05:33,217 - INFO - 起点场景在原始列表中的索引: 210
2025-07-29 20:05:33,217 - INFO - 延伸添加场景: scene_id=212 (完整时长 1.56秒)
2025-07-29 20:05:33,217 - INFO - 累计时长: 2.72秒
2025-07-29 20:05:33,217 - INFO - 延伸添加场景: scene_id=213 (完整时长 1.32秒)
2025-07-29 20:05:33,217 - INFO - 累计时长: 4.04秒
2025-07-29 20:05:33,217 - INFO - 延伸添加场景: scene_id=214 (裁剪至 1.07秒)
2025-07-29 20:05:33,217 - INFO - 累计时长: 5.11秒
2025-07-29 20:05:33,217 - INFO - 字幕序号 #187 场景匹配完成，共选择 4 个场景，总时长: 5.11秒
2025-07-29 20:05:33,217 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:33,217 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:33,217 - INFO - ========== 当前模式：为字幕 #13 生成 1 套场景方案 ==========
2025-07-29 20:05:33,217 - INFO - 开始查找字幕序号 [182, 187] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:33,217 - INFO - 找到related_overlap场景: scene_id=203, 字幕#182
2025-07-29 20:05:33,217 - INFO - 找到related_overlap场景: scene_id=204, 字幕#182
2025-07-29 20:05:33,217 - INFO - 找到related_overlap场景: scene_id=211, 字幕#187
2025-07-29 20:05:33,217 - INFO - 找到related_overlap场景: scene_id=212, 字幕#187
2025-07-29 20:05:33,218 - INFO - 找到related_between场景: scene_id=213, 字幕#187
2025-07-29 20:05:33,218 - INFO - 找到related_between场景: scene_id=214, 字幕#187
2025-07-29 20:05:33,218 - INFO - 字幕 #182 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:33,218 - INFO - 字幕 #187 找到 2 个overlap场景, 2 个between场景
2025-07-29 20:05:33,218 - INFO - 共收集 4 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 20:05:33,218 - INFO - 开始生成方案 #1
2025-07-29 20:05:33,218 - INFO - 方案 #1: 为字幕#182选择初始化overlap场景id=203
2025-07-29 20:05:33,218 - INFO - 方案 #1: 为字幕#187选择初始化overlap场景id=212
2025-07-29 20:05:33,218 - INFO - 方案 #1: 初始选择后，当前总时长=3.92秒
2025-07-29 20:05:33,218 - INFO - 方案 #1: 额外添加overlap场景id=211, 当前总时长=5.08秒
2025-07-29 20:05:33,218 - INFO - 方案 #1: 额外添加overlap场景id=204, 当前总时长=6.44秒
2025-07-29 20:05:33,218 - INFO - 方案 #1: 额外between选择后，当前总时长=6.44秒
2025-07-29 20:05:33,218 - INFO - 方案 #1: 场景总时长(6.44秒)大于音频时长(5.11秒)，需要裁剪
2025-07-29 20:05:33,218 - INFO - 调整前总时长: 6.44秒, 目标时长: 5.11秒
2025-07-29 20:05:33,218 - INFO - 需要裁剪 1.33秒
2025-07-29 20:05:33,218 - INFO - 裁剪最长场景ID=203：从2.36秒裁剪至1.03秒
2025-07-29 20:05:33,218 - INFO - 调整后总时长: 5.11秒，与目标时长差异: 0.00秒
2025-07-29 20:05:33,218 - INFO - 方案 #1 调整/填充后最终总时长: 5.11秒
2025-07-29 20:05:33,219 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:33,219 - INFO - ========== 当前模式：字幕 #13 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:33,219 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:33,219 - INFO - ========== 新模式：字幕 #13 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:33,219 - INFO - 
----- 处理字幕 #13 的方案 #1 -----
2025-07-29 20:05:33,219 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 20:05:33,219 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiqnlh7wo
2025-07-29 20:05:33,219 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\203.mp4 (确认存在: True)
2025-07-29 20:05:33,220 - INFO - 添加场景ID=203，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:05:33,220 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 20:05:33,220 - INFO - 添加场景ID=204，时长=1.36秒，累计时长=3.72秒
2025-07-29 20:05:33,220 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\205.mp4 (确认存在: True)
2025-07-29 20:05:33,220 - INFO - 添加场景ID=205，时长=1.56秒，累计时长=5.28秒
2025-07-29 20:05:33,220 - INFO - 准备合并 3 个场景文件，总时长约 5.28秒
2025-07-29 20:05:33,220 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/203.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/205.mp4'

2025-07-29 20:05:33,220 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiqnlh7wo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiqnlh7wo\temp_combined.mp4
2025-07-29 20:05:33,353 - INFO - 合并后的视频时长: 5.35秒，目标音频时长: 5.11秒
2025-07-29 20:05:33,353 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiqnlh7wo\temp_combined.mp4 -ss 0 -to 5.107 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 20:05:33,691 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:33,691 - INFO - 目标音频时长: 5.11秒
2025-07-29 20:05:33,692 - INFO - 实际视频时长: 5.14秒
2025-07-29 20:05:33,692 - INFO - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:05:33,692 - INFO - ==========================================
2025-07-29 20:05:33,692 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:33,692 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 20:05:33,692 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiqnlh7wo
2025-07-29 20:05:33,737 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:33,737 - INFO -   - 音频时长: 5.11秒
2025-07-29 20:05:33,737 - INFO -   - 视频时长: 5.14秒
2025-07-29 20:05:33,737 - INFO -   - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:05:33,737 - INFO - 
----- 处理字幕 #13 的方案 #2 -----
2025-07-29 20:05:33,737 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 20:05:33,738 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpem93w0w6
2025-07-29 20:05:33,738 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\211.mp4 (确认存在: True)
2025-07-29 20:05:33,738 - INFO - 添加场景ID=211，时长=1.16秒，累计时长=1.16秒
2025-07-29 20:05:33,738 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\212.mp4 (确认存在: True)
2025-07-29 20:05:33,738 - INFO - 添加场景ID=212，时长=1.56秒，累计时长=2.72秒
2025-07-29 20:05:33,738 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\213.mp4 (确认存在: True)
2025-07-29 20:05:33,738 - INFO - 添加场景ID=213，时长=1.32秒，累计时长=4.04秒
2025-07-29 20:05:33,738 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\214.mp4 (确认存在: True)
2025-07-29 20:05:33,738 - INFO - 添加场景ID=214，时长=1.20秒，累计时长=5.24秒
2025-07-29 20:05:33,739 - INFO - 准备合并 4 个场景文件，总时长约 5.24秒
2025-07-29 20:05:33,739 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/211.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/212.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/213.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/214.mp4'

2025-07-29 20:05:33,739 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpem93w0w6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpem93w0w6\temp_combined.mp4
2025-07-29 20:05:33,882 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 5.11秒
2025-07-29 20:05:33,882 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpem93w0w6\temp_combined.mp4 -ss 0 -to 5.107 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 20:05:34,220 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:34,220 - INFO - 目标音频时长: 5.11秒
2025-07-29 20:05:34,220 - INFO - 实际视频时长: 5.14秒
2025-07-29 20:05:34,220 - INFO - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:05:34,220 - INFO - ==========================================
2025-07-29 20:05:34,220 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:34,220 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 20:05:34,221 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpem93w0w6
2025-07-29 20:05:34,265 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:34,265 - INFO -   - 音频时长: 5.11秒
2025-07-29 20:05:34,265 - INFO -   - 视频时长: 5.14秒
2025-07-29 20:05:34,265 - INFO -   - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:05:34,265 - INFO - 
----- 处理字幕 #13 的方案 #3 -----
2025-07-29 20:05:34,265 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 20:05:34,266 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpeceoehiz
2025-07-29 20:05:34,266 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\203.mp4 (确认存在: True)
2025-07-29 20:05:34,266 - INFO - 添加场景ID=203，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:05:34,266 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\212.mp4 (确认存在: True)
2025-07-29 20:05:34,266 - INFO - 添加场景ID=212，时长=1.56秒，累计时长=3.92秒
2025-07-29 20:05:34,266 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\211.mp4 (确认存在: True)
2025-07-29 20:05:34,266 - INFO - 添加场景ID=211，时长=1.16秒，累计时长=5.08秒
2025-07-29 20:05:34,266 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 20:05:34,266 - INFO - 添加场景ID=204，时长=1.36秒，累计时长=6.44秒
2025-07-29 20:05:34,266 - INFO - 准备合并 4 个场景文件，总时长约 6.44秒
2025-07-29 20:05:34,266 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/203.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/212.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/211.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'

2025-07-29 20:05:34,267 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpeceoehiz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpeceoehiz\temp_combined.mp4
2025-07-29 20:05:34,401 - INFO - 合并后的视频时长: 6.53秒，目标音频时长: 5.11秒
2025-07-29 20:05:34,401 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpeceoehiz\temp_combined.mp4 -ss 0 -to 5.107 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 20:05:34,722 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:34,723 - INFO - 目标音频时长: 5.11秒
2025-07-29 20:05:34,723 - INFO - 实际视频时长: 5.14秒
2025-07-29 20:05:34,723 - INFO - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:05:34,723 - INFO - ==========================================
2025-07-29 20:05:34,723 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:34,723 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 20:05:34,723 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpeceoehiz
2025-07-29 20:05:34,767 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:34,768 - INFO -   - 音频时长: 5.11秒
2025-07-29 20:05:34,768 - INFO -   - 视频时长: 5.14秒
2025-07-29 20:05:34,768 - INFO -   - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:05:34,768 - INFO - 
字幕 #13 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:34,768 - INFO - 生成的视频文件:
2025-07-29 20:05:34,768 - INFO -   1. F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 20:05:34,768 - INFO -   2. F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 20:05:34,768 - INFO -   3. F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 20:05:34,768 - INFO - ========== 字幕 #13 处理结束 ==========

