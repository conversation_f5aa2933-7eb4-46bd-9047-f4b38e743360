2025-07-29 20:06:15,437 - INFO - ========== 字幕 #41 处理开始 ==========
2025-07-29 20:06:15,437 - INFO - 字幕内容: 五年后，男人以威尔集团华夏区总裁“An”的身份荣耀归国，入住的正是女人旗下的五星级酒店，这何尝不是一种讽刺。
2025-07-29 20:06:15,437 - INFO - 字幕序号: [877, 885]
2025-07-29 20:06:15,437 - INFO - 音频文件详情:
2025-07-29 20:06:15,437 - INFO -   - 路径: output\41.wav
2025-07-29 20:06:15,437 - INFO -   - 时长: 6.51秒
2025-07-29 20:06:15,439 - INFO -   - 验证音频时长: 6.51秒
2025-07-29 20:06:15,447 - INFO - 字幕时间戳信息:
2025-07-29 20:06:15,447 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:15,447 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:15,447 - INFO -   - 根据生成的音频时长(6.51秒)已调整字幕时间戳
2025-07-29 20:06:15,447 - INFO - ========== 新模式：为字幕 #41 生成4套场景方案 ==========
2025-07-29 20:06:15,447 - INFO - 字幕序号列表: [877, 885]
2025-07-29 20:06:15,449 - INFO - 
--- 生成方案 #1：基于字幕序号 #877 ---
2025-07-29 20:06:15,449 - INFO - 开始为单个字幕序号 #877 匹配场景，目标时长: 6.51秒
2025-07-29 20:06:15,449 - INFO - 开始查找字幕序号 [877] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:15,449 - INFO - 找到related_overlap场景: scene_id=905, 字幕#877
2025-07-29 20:06:15,450 - INFO - 找到related_between场景: scene_id=904, 字幕#877
2025-07-29 20:06:15,450 - INFO - 字幕 #877 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:15,450 - INFO - 字幕序号 #877 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:15,450 - INFO - 选择第一个overlap场景作为起点: scene_id=905
2025-07-29 20:06:15,450 - INFO - 添加起点场景: scene_id=905, 时长=3.40秒, 累计时长=3.40秒
2025-07-29 20:06:15,450 - INFO - 起点场景时长不足，需要延伸填充 3.11秒
2025-07-29 20:06:15,450 - INFO - 起点场景在原始列表中的索引: 904
2025-07-29 20:06:15,450 - INFO - 延伸添加场景: scene_id=906 (完整时长 3.00秒)
2025-07-29 20:06:15,450 - INFO - 累计时长: 6.40秒
2025-07-29 20:06:15,450 - INFO - 延伸添加场景: scene_id=907 (裁剪至 0.11秒)
2025-07-29 20:06:15,450 - INFO - 累计时长: 6.51秒
2025-07-29 20:06:15,450 - INFO - 字幕序号 #877 场景匹配完成，共选择 3 个场景，总时长: 6.51秒
2025-07-29 20:06:15,450 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:15,450 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:15,450 - INFO - 
--- 生成方案 #2：基于字幕序号 #885 ---
2025-07-29 20:06:15,450 - INFO - 开始为单个字幕序号 #885 匹配场景，目标时长: 6.51秒
2025-07-29 20:06:15,450 - INFO - 开始查找字幕序号 [885] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:15,451 - INFO - 找到related_overlap场景: scene_id=911, 字幕#885
2025-07-29 20:06:15,452 - INFO - 字幕 #885 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:15,452 - INFO - 字幕序号 #885 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:15,452 - INFO - 选择第一个overlap场景作为起点: scene_id=911
2025-07-29 20:06:15,452 - INFO - 添加起点场景: scene_id=911, 时长=2.60秒, 累计时长=2.60秒
2025-07-29 20:06:15,452 - INFO - 起点场景时长不足，需要延伸填充 3.91秒
2025-07-29 20:06:15,452 - INFO - 起点场景在原始列表中的索引: 910
2025-07-29 20:06:15,452 - INFO - 延伸添加场景: scene_id=912 (完整时长 0.80秒)
2025-07-29 20:06:15,452 - INFO - 累计时长: 3.40秒
2025-07-29 20:06:15,452 - INFO - 延伸添加场景: scene_id=913 (完整时长 1.04秒)
2025-07-29 20:06:15,452 - INFO - 累计时长: 4.44秒
2025-07-29 20:06:15,452 - INFO - 延伸添加场景: scene_id=914 (裁剪至 2.07秒)
2025-07-29 20:06:15,452 - INFO - 累计时长: 6.51秒
2025-07-29 20:06:15,452 - INFO - 字幕序号 #885 场景匹配完成，共选择 4 个场景，总时长: 6.51秒
2025-07-29 20:06:15,452 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:15,452 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:15,452 - INFO - ========== 当前模式：为字幕 #41 生成 1 套场景方案 ==========
2025-07-29 20:06:15,452 - INFO - 开始查找字幕序号 [877, 885] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:15,452 - INFO - 找到related_overlap场景: scene_id=905, 字幕#877
2025-07-29 20:06:15,452 - INFO - 找到related_overlap场景: scene_id=911, 字幕#885
2025-07-29 20:06:15,453 - INFO - 找到related_between场景: scene_id=904, 字幕#877
2025-07-29 20:06:15,453 - INFO - 字幕 #877 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:15,453 - INFO - 字幕 #885 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:15,453 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:06:15,453 - INFO - 开始生成方案 #1
2025-07-29 20:06:15,453 - INFO - 方案 #1: 为字幕#877选择初始化overlap场景id=905
2025-07-29 20:06:15,453 - INFO - 方案 #1: 为字幕#885选择初始化overlap场景id=911
2025-07-29 20:06:15,453 - INFO - 方案 #1: 初始选择后，当前总时长=6.00秒
2025-07-29 20:06:15,453 - INFO - 方案 #1: 额外between选择后，当前总时长=6.00秒
2025-07-29 20:06:15,453 - INFO - 方案 #1: 额外添加between场景id=904, 当前总时长=8.96秒
2025-07-29 20:06:15,453 - INFO - 方案 #1: 场景总时长(8.96秒)大于音频时长(6.51秒)，需要裁剪
2025-07-29 20:06:15,453 - INFO - 调整前总时长: 8.96秒, 目标时长: 6.51秒
2025-07-29 20:06:15,453 - INFO - 需要裁剪 2.45秒
2025-07-29 20:06:15,453 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:15,453 - INFO - 裁剪场景ID=905：从3.40秒裁剪至1.02秒
2025-07-29 20:06:15,453 - INFO - 裁剪场景ID=904：从2.96秒裁剪至2.89秒
2025-07-29 20:06:15,453 - INFO - 调整后总时长: 6.51秒，与目标时长差异: 0.00秒
2025-07-29 20:06:15,453 - INFO - 方案 #1 调整/填充后最终总时长: 6.51秒
2025-07-29 20:06:15,453 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:15,453 - INFO - ========== 当前模式：字幕 #41 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:15,453 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:15,453 - INFO - ========== 新模式：字幕 #41 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:15,453 - INFO - 
----- 处理字幕 #41 的方案 #1 -----
2025-07-29 20:06:15,454 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 20:06:15,454 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbefxjyy9
2025-07-29 20:06:15,454 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\905.mp4 (确认存在: True)
2025-07-29 20:06:15,454 - INFO - 添加场景ID=905，时长=3.40秒，累计时长=3.40秒
2025-07-29 20:06:15,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\906.mp4 (确认存在: True)
2025-07-29 20:06:15,455 - INFO - 添加场景ID=906，时长=3.00秒，累计时长=6.40秒
2025-07-29 20:06:15,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\907.mp4 (确认存在: True)
2025-07-29 20:06:15,455 - INFO - 添加场景ID=907，时长=3.76秒，累计时长=10.16秒
2025-07-29 20:06:15,455 - INFO - 场景总时长(10.16秒)已达到音频时长(6.51秒)的1.5倍，停止添加场景
2025-07-29 20:06:15,455 - INFO - 准备合并 3 个场景文件，总时长约 10.16秒
2025-07-29 20:06:15,455 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/905.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/906.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/907.mp4'

2025-07-29 20:06:15,455 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbefxjyy9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbefxjyy9\temp_combined.mp4
2025-07-29 20:06:15,602 - INFO - 合并后的视频时长: 10.23秒，目标音频时长: 6.51秒
2025-07-29 20:06:15,602 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbefxjyy9\temp_combined.mp4 -ss 0 -to 6.51 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 20:06:15,978 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:15,978 - INFO - 目标音频时长: 6.51秒
2025-07-29 20:06:15,978 - INFO - 实际视频时长: 6.54秒
2025-07-29 20:06:15,978 - INFO - 时长差异: 0.03秒 (0.51%)
2025-07-29 20:06:15,978 - INFO - ==========================================
2025-07-29 20:06:15,978 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:15,978 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 20:06:15,979 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbefxjyy9
2025-07-29 20:06:16,023 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:16,023 - INFO -   - 音频时长: 6.51秒
2025-07-29 20:06:16,023 - INFO -   - 视频时长: 6.54秒
2025-07-29 20:06:16,023 - INFO -   - 时长差异: 0.03秒 (0.51%)
2025-07-29 20:06:16,023 - INFO - 
----- 处理字幕 #41 的方案 #2 -----
2025-07-29 20:06:16,023 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 20:06:16,023 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1k2b5_6_
2025-07-29 20:06:16,025 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\911.mp4 (确认存在: True)
2025-07-29 20:06:16,025 - INFO - 添加场景ID=911，时长=2.60秒，累计时长=2.60秒
2025-07-29 20:06:16,025 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\912.mp4 (确认存在: True)
2025-07-29 20:06:16,025 - INFO - 添加场景ID=912，时长=0.80秒，累计时长=3.40秒
2025-07-29 20:06:16,025 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\913.mp4 (确认存在: True)
2025-07-29 20:06:16,025 - INFO - 添加场景ID=913，时长=1.04秒，累计时长=4.44秒
2025-07-29 20:06:16,025 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\914.mp4 (确认存在: True)
2025-07-29 20:06:16,025 - INFO - 添加场景ID=914，时长=3.72秒，累计时长=8.16秒
2025-07-29 20:06:16,025 - INFO - 准备合并 4 个场景文件，总时长约 8.16秒
2025-07-29 20:06:16,025 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/911.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/912.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/913.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/914.mp4'

2025-07-29 20:06:16,026 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1k2b5_6_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1k2b5_6_\temp_combined.mp4
2025-07-29 20:06:16,181 - INFO - 合并后的视频时长: 8.25秒，目标音频时长: 6.51秒
2025-07-29 20:06:16,181 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1k2b5_6_\temp_combined.mp4 -ss 0 -to 6.51 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 20:06:16,532 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:16,532 - INFO - 目标音频时长: 6.51秒
2025-07-29 20:06:16,532 - INFO - 实际视频时长: 6.54秒
2025-07-29 20:06:16,532 - INFO - 时长差异: 0.03秒 (0.51%)
2025-07-29 20:06:16,532 - INFO - ==========================================
2025-07-29 20:06:16,532 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:16,532 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 20:06:16,533 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1k2b5_6_
2025-07-29 20:06:16,579 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:16,579 - INFO -   - 音频时长: 6.51秒
2025-07-29 20:06:16,579 - INFO -   - 视频时长: 6.54秒
2025-07-29 20:06:16,579 - INFO -   - 时长差异: 0.03秒 (0.51%)
2025-07-29 20:06:16,579 - INFO - 
----- 处理字幕 #41 的方案 #3 -----
2025-07-29 20:06:16,579 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 20:06:16,580 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5yjeu_ut
2025-07-29 20:06:16,580 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\905.mp4 (确认存在: True)
2025-07-29 20:06:16,580 - INFO - 添加场景ID=905，时长=3.40秒，累计时长=3.40秒
2025-07-29 20:06:16,580 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\911.mp4 (确认存在: True)
2025-07-29 20:06:16,580 - INFO - 添加场景ID=911，时长=2.60秒，累计时长=6.00秒
2025-07-29 20:06:16,580 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\904.mp4 (确认存在: True)
2025-07-29 20:06:16,580 - INFO - 添加场景ID=904，时长=2.96秒，累计时长=8.96秒
2025-07-29 20:06:16,580 - INFO - 准备合并 3 个场景文件，总时长约 8.96秒
2025-07-29 20:06:16,581 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/905.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/911.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/904.mp4'

2025-07-29 20:06:16,581 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5yjeu_ut\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5yjeu_ut\temp_combined.mp4
2025-07-29 20:06:16,722 - INFO - 合并后的视频时长: 9.03秒，目标音频时长: 6.51秒
2025-07-29 20:06:16,722 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5yjeu_ut\temp_combined.mp4 -ss 0 -to 6.51 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 20:06:17,084 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:17,084 - INFO - 目标音频时长: 6.51秒
2025-07-29 20:06:17,084 - INFO - 实际视频时长: 6.54秒
2025-07-29 20:06:17,084 - INFO - 时长差异: 0.03秒 (0.51%)
2025-07-29 20:06:17,085 - INFO - ==========================================
2025-07-29 20:06:17,085 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:17,085 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 20:06:17,085 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5yjeu_ut
2025-07-29 20:06:17,129 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:17,129 - INFO -   - 音频时长: 6.51秒
2025-07-29 20:06:17,129 - INFO -   - 视频时长: 6.54秒
2025-07-29 20:06:17,129 - INFO -   - 时长差异: 0.03秒 (0.51%)
2025-07-29 20:06:17,129 - INFO - 
字幕 #41 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:17,129 - INFO - 生成的视频文件:
2025-07-29 20:06:17,129 - INFO -   1. F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 20:06:17,129 - INFO -   2. F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 20:06:17,129 - INFO -   3. F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 20:06:17,129 - INFO - ========== 字幕 #41 处理结束 ==========

