2025-07-29 20:06:59,140 - INFO - ========== 字幕 #72 处理开始 ==========
2025-07-29 20:06:59,140 - INFO - 字幕内容: 此时，南宫家大小姐，男人现在的女友霸气登场，戳穿了女人母亲的丑恶嘴脸，并宣告了自己和男人的关系。
2025-07-29 20:06:59,140 - INFO - 字幕序号: [2984, 2989]
2025-07-29 20:06:59,140 - INFO - 音频文件详情:
2025-07-29 20:06:59,140 - INFO -   - 路径: output\72.wav
2025-07-29 20:06:59,140 - INFO -   - 时长: 6.88秒
2025-07-29 20:06:59,140 - INFO -   - 验证音频时长: 6.88秒
2025-07-29 20:06:59,140 - INFO - 字幕时间戳信息:
2025-07-29 20:06:59,140 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:59,140 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:59,140 - INFO -   - 根据生成的音频时长(6.88秒)已调整字幕时间戳
2025-07-29 20:06:59,140 - INFO - ========== 新模式：为字幕 #72 生成4套场景方案 ==========
2025-07-29 20:06:59,140 - INFO - 字幕序号列表: [2984, 2989]
2025-07-29 20:06:59,141 - INFO - 
--- 生成方案 #1：基于字幕序号 #2984 ---
2025-07-29 20:06:59,141 - INFO - 开始为单个字幕序号 #2984 匹配场景，目标时长: 6.88秒
2025-07-29 20:06:59,141 - INFO - 开始查找字幕序号 [2984] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:59,141 - INFO - 找到related_overlap场景: scene_id=2751, 字幕#2984
2025-07-29 20:06:59,142 - INFO - 找到related_between场景: scene_id=2752, 字幕#2984
2025-07-29 20:06:59,142 - INFO - 字幕 #2984 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:59,142 - INFO - 字幕序号 #2984 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:59,142 - INFO - 选择第一个overlap场景作为起点: scene_id=2751
2025-07-29 20:06:59,142 - INFO - 添加起点场景: scene_id=2751, 时长=2.52秒, 累计时长=2.52秒
2025-07-29 20:06:59,142 - INFO - 起点场景时长不足，需要延伸填充 4.36秒
2025-07-29 20:06:59,142 - INFO - 起点场景在原始列表中的索引: 2750
2025-07-29 20:06:59,142 - INFO - 延伸添加场景: scene_id=2752 (完整时长 1.28秒)
2025-07-29 20:06:59,142 - INFO - 累计时长: 3.80秒
2025-07-29 20:06:59,143 - INFO - 延伸添加场景: scene_id=2753 (完整时长 2.08秒)
2025-07-29 20:06:59,143 - INFO - 累计时长: 5.88秒
2025-07-29 20:06:59,143 - INFO - 延伸添加场景: scene_id=2754 (裁剪至 1.00秒)
2025-07-29 20:06:59,143 - INFO - 累计时长: 6.88秒
2025-07-29 20:06:59,143 - INFO - 字幕序号 #2984 场景匹配完成，共选择 4 个场景，总时长: 6.88秒
2025-07-29 20:06:59,143 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:59,143 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:59,143 - INFO - 
--- 生成方案 #2：基于字幕序号 #2989 ---
2025-07-29 20:06:59,143 - INFO - 开始为单个字幕序号 #2989 匹配场景，目标时长: 6.88秒
2025-07-29 20:06:59,143 - INFO - 开始查找字幕序号 [2989] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:59,143 - INFO - 找到related_overlap场景: scene_id=2754, 字幕#2989
2025-07-29 20:06:59,144 - INFO - 字幕 #2989 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:59,144 - INFO - 字幕序号 #2989 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:59,144 - ERROR - 字幕序号 #2989 没有找到任何可用的匹配场景
2025-07-29 20:06:59,144 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:59,144 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:59,144 - INFO - ========== 当前模式：为字幕 #72 生成 1 套场景方案 ==========
2025-07-29 20:06:59,144 - INFO - 开始查找字幕序号 [2984, 2989] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:59,144 - INFO - 找到related_overlap场景: scene_id=2751, 字幕#2984
2025-07-29 20:06:59,144 - INFO - 找到related_overlap场景: scene_id=2754, 字幕#2989
2025-07-29 20:06:59,145 - INFO - 找到related_between场景: scene_id=2752, 字幕#2984
2025-07-29 20:06:59,145 - INFO - 字幕 #2984 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:59,145 - INFO - 字幕 #2989 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:59,145 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:06:59,145 - INFO - 开始生成方案 #1
2025-07-29 20:06:59,145 - INFO - 方案 #1: 为字幕#2984选择初始化overlap场景id=2751
2025-07-29 20:06:59,145 - INFO - 方案 #1: 为字幕#2989选择初始化overlap场景id=2754
2025-07-29 20:06:59,145 - INFO - 方案 #1: 初始选择后，当前总时长=5.64秒
2025-07-29 20:06:59,145 - INFO - 方案 #1: 额外between选择后，当前总时长=5.64秒
2025-07-29 20:06:59,145 - INFO - 方案 #1: 额外添加between场景id=2752, 当前总时长=6.92秒
2025-07-29 20:06:59,145 - INFO - 方案 #1: 场景总时长(6.92秒)大于音频时长(6.88秒)，需要裁剪
2025-07-29 20:06:59,145 - INFO - 调整前总时长: 6.92秒, 目标时长: 6.88秒
2025-07-29 20:06:59,145 - INFO - 需要裁剪 0.04秒
2025-07-29 20:06:59,145 - INFO - 裁剪最长场景ID=2754：从3.12秒裁剪至3.08秒
2025-07-29 20:06:59,145 - INFO - 调整后总时长: 6.88秒，与目标时长差异: 0.00秒
2025-07-29 20:06:59,145 - INFO - 方案 #1 调整/填充后最终总时长: 6.88秒
2025-07-29 20:06:59,145 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:59,145 - INFO - ========== 当前模式：字幕 #72 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:59,145 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:59,145 - INFO - ========== 新模式：字幕 #72 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:59,146 - INFO - 
----- 处理字幕 #72 的方案 #1 -----
2025-07-29 20:06:59,146 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 20:06:59,146 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkw_m46wx
2025-07-29 20:06:59,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2751.mp4 (确认存在: True)
2025-07-29 20:06:59,146 - INFO - 添加场景ID=2751，时长=2.52秒，累计时长=2.52秒
2025-07-29 20:06:59,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2752.mp4 (确认存在: True)
2025-07-29 20:06:59,146 - INFO - 添加场景ID=2752，时长=1.28秒，累计时长=3.80秒
2025-07-29 20:06:59,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2753.mp4 (确认存在: True)
2025-07-29 20:06:59,146 - INFO - 添加场景ID=2753，时长=2.08秒，累计时长=5.88秒
2025-07-29 20:06:59,146 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2754.mp4 (确认存在: True)
2025-07-29 20:06:59,146 - INFO - 添加场景ID=2754，时长=3.12秒，累计时长=9.00秒
2025-07-29 20:06:59,146 - INFO - 准备合并 4 个场景文件，总时长约 9.00秒
2025-07-29 20:06:59,148 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2751.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2752.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2753.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2754.mp4'

2025-07-29 20:06:59,148 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkw_m46wx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkw_m46wx\temp_combined.mp4
2025-07-29 20:06:59,306 - INFO - 合并后的视频时长: 9.09秒，目标音频时长: 6.88秒
2025-07-29 20:06:59,306 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkw_m46wx\temp_combined.mp4 -ss 0 -to 6.882 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 20:06:59,693 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:59,693 - INFO - 目标音频时长: 6.88秒
2025-07-29 20:06:59,693 - INFO - 实际视频时长: 6.94秒
2025-07-29 20:06:59,693 - INFO - 时长差异: 0.06秒 (0.89%)
2025-07-29 20:06:59,693 - INFO - ==========================================
2025-07-29 20:06:59,693 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:59,693 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 20:06:59,694 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkw_m46wx
2025-07-29 20:06:59,755 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:59,755 - INFO -   - 音频时长: 6.88秒
2025-07-29 20:06:59,755 - INFO -   - 视频时长: 6.94秒
2025-07-29 20:06:59,755 - INFO -   - 时长差异: 0.06秒 (0.89%)
2025-07-29 20:06:59,755 - INFO - 
----- 处理字幕 #72 的方案 #2 -----
2025-07-29 20:06:59,755 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 20:06:59,755 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgfe4q8xb
2025-07-29 20:06:59,756 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2751.mp4 (确认存在: True)
2025-07-29 20:06:59,756 - INFO - 添加场景ID=2751，时长=2.52秒，累计时长=2.52秒
2025-07-29 20:06:59,756 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2754.mp4 (确认存在: True)
2025-07-29 20:06:59,756 - INFO - 添加场景ID=2754，时长=3.12秒，累计时长=5.64秒
2025-07-29 20:06:59,756 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2752.mp4 (确认存在: True)
2025-07-29 20:06:59,756 - INFO - 添加场景ID=2752，时长=1.28秒，累计时长=6.92秒
2025-07-29 20:06:59,756 - INFO - 准备合并 3 个场景文件，总时长约 6.92秒
2025-07-29 20:06:59,756 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2751.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2754.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2752.mp4'

2025-07-29 20:06:59,756 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgfe4q8xb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgfe4q8xb\temp_combined.mp4
2025-07-29 20:06:59,897 - INFO - 合并后的视频时长: 6.99秒，目标音频时长: 6.88秒
2025-07-29 20:06:59,897 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgfe4q8xb\temp_combined.mp4 -ss 0 -to 6.882 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 20:07:00,274 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:00,274 - INFO - 目标音频时长: 6.88秒
2025-07-29 20:07:00,274 - INFO - 实际视频时长: 6.94秒
2025-07-29 20:07:00,274 - INFO - 时长差异: 0.06秒 (0.89%)
2025-07-29 20:07:00,274 - INFO - ==========================================
2025-07-29 20:07:00,274 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:00,274 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 20:07:00,275 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgfe4q8xb
2025-07-29 20:07:00,320 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:00,320 - INFO -   - 音频时长: 6.88秒
2025-07-29 20:07:00,320 - INFO -   - 视频时长: 6.94秒
2025-07-29 20:07:00,320 - INFO -   - 时长差异: 0.06秒 (0.89%)
2025-07-29 20:07:00,320 - INFO - 
字幕 #72 处理完成，成功生成 2/2 套方案
2025-07-29 20:07:00,320 - INFO - 生成的视频文件:
2025-07-29 20:07:00,320 - INFO -   1. F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 20:07:00,320 - INFO -   2. F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 20:07:00,320 - INFO - ========== 字幕 #72 处理结束 ==========

