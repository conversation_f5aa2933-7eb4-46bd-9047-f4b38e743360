2025-07-29 20:06:57,354 - INFO - ========== 字幕 #70 处理开始 ==========
2025-07-29 20:06:57,354 - INFO - 字幕内容: 忍无可忍的男人，终于给了她一记响亮的耳光，这是他对她无休止纠缠的最后警告。
2025-07-29 20:06:57,354 - INFO - 字幕序号: [2872, 2875]
2025-07-29 20:06:57,354 - INFO - 音频文件详情:
2025-07-29 20:06:57,354 - INFO -   - 路径: output\70.wav
2025-07-29 20:06:57,354 - INFO -   - 时长: 4.80秒
2025-07-29 20:06:57,354 - INFO -   - 验证音频时长: 4.80秒
2025-07-29 20:06:57,354 - INFO - 字幕时间戳信息:
2025-07-29 20:06:57,354 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:57,354 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:57,354 - INFO -   - 根据生成的音频时长(4.80秒)已调整字幕时间戳
2025-07-29 20:06:57,354 - INFO - ========== 新模式：为字幕 #70 生成4套场景方案 ==========
2025-07-29 20:06:57,354 - INFO - 字幕序号列表: [2872, 2875]
2025-07-29 20:06:57,354 - INFO - 
--- 生成方案 #1：基于字幕序号 #2872 ---
2025-07-29 20:06:57,354 - INFO - 开始为单个字幕序号 #2872 匹配场景，目标时长: 4.80秒
2025-07-29 20:06:57,354 - INFO - 开始查找字幕序号 [2872] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:57,355 - INFO - 找到related_overlap场景: scene_id=2670, 字幕#2872
2025-07-29 20:06:57,356 - INFO - 字幕 #2872 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:57,356 - INFO - 字幕序号 #2872 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:57,356 - INFO - 选择第一个overlap场景作为起点: scene_id=2670
2025-07-29 20:06:57,356 - INFO - 添加起点场景: scene_id=2670, 时长=1.52秒, 累计时长=1.52秒
2025-07-29 20:06:57,356 - INFO - 起点场景时长不足，需要延伸填充 3.28秒
2025-07-29 20:06:57,356 - INFO - 起点场景在原始列表中的索引: 2669
2025-07-29 20:06:57,356 - INFO - 延伸添加场景: scene_id=2671 (完整时长 1.40秒)
2025-07-29 20:06:57,356 - INFO - 累计时长: 2.92秒
2025-07-29 20:06:57,356 - INFO - 延伸添加场景: scene_id=2672 (裁剪至 1.88秒)
2025-07-29 20:06:57,356 - INFO - 累计时长: 4.80秒
2025-07-29 20:06:57,356 - INFO - 字幕序号 #2872 场景匹配完成，共选择 3 个场景，总时长: 4.80秒
2025-07-29 20:06:57,356 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:57,356 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:57,356 - INFO - 
--- 生成方案 #2：基于字幕序号 #2875 ---
2025-07-29 20:06:57,356 - INFO - 开始为单个字幕序号 #2875 匹配场景，目标时长: 4.80秒
2025-07-29 20:06:57,356 - INFO - 开始查找字幕序号 [2875] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:57,356 - INFO - 找到related_overlap场景: scene_id=2672, 字幕#2875
2025-07-29 20:06:57,358 - INFO - 字幕 #2875 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:57,358 - INFO - 字幕序号 #2875 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:57,358 - ERROR - 字幕序号 #2875 没有找到任何可用的匹配场景
2025-07-29 20:06:57,358 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:57,358 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:57,358 - INFO - ========== 当前模式：为字幕 #70 生成 1 套场景方案 ==========
2025-07-29 20:06:57,358 - INFO - 开始查找字幕序号 [2872, 2875] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:57,359 - INFO - 找到related_overlap场景: scene_id=2670, 字幕#2872
2025-07-29 20:06:57,359 - INFO - 找到related_overlap场景: scene_id=2672, 字幕#2875
2025-07-29 20:06:57,359 - INFO - 字幕 #2872 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:57,359 - INFO - 字幕 #2875 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:57,359 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:57,359 - INFO - 开始生成方案 #1
2025-07-29 20:06:57,359 - INFO - 方案 #1: 为字幕#2872选择初始化overlap场景id=2670
2025-07-29 20:06:57,359 - INFO - 方案 #1: 为字幕#2875选择初始化overlap场景id=2672
2025-07-29 20:06:57,359 - INFO - 方案 #1: 初始选择后，当前总时长=3.84秒
2025-07-29 20:06:57,359 - INFO - 方案 #1: 额外between选择后，当前总时长=3.84秒
2025-07-29 20:06:57,359 - INFO - 方案 #1: 场景总时长(3.84秒)小于音频时长(4.80秒)，需要延伸填充
2025-07-29 20:06:57,359 - INFO - 方案 #1: 最后一个场景ID: 2672
2025-07-29 20:06:57,359 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2671
2025-07-29 20:06:57,359 - INFO - 方案 #1: 需要填充时长: 0.96秒
2025-07-29 20:06:57,359 - INFO - 方案 #1: 追加场景 scene_id=2673 (裁剪至 0.96秒)
2025-07-29 20:06:57,359 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:57,359 - INFO - 方案 #1 调整/填充后最终总时长: 4.80秒
2025-07-29 20:06:57,359 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:57,359 - INFO - ========== 当前模式：字幕 #70 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:57,359 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:57,359 - INFO - ========== 新模式：字幕 #70 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:57,359 - INFO - 
----- 处理字幕 #70 的方案 #1 -----
2025-07-29 20:06:57,359 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 20:06:57,360 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq31kx2hy
2025-07-29 20:06:57,360 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2670.mp4 (确认存在: True)
2025-07-29 20:06:57,361 - INFO - 添加场景ID=2670，时长=1.52秒，累计时长=1.52秒
2025-07-29 20:06:57,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2671.mp4 (确认存在: True)
2025-07-29 20:06:57,361 - INFO - 添加场景ID=2671，时长=1.40秒，累计时长=2.92秒
2025-07-29 20:06:57,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2672.mp4 (确认存在: True)
2025-07-29 20:06:57,361 - INFO - 添加场景ID=2672，时长=2.32秒，累计时长=5.24秒
2025-07-29 20:06:57,361 - INFO - 准备合并 3 个场景文件，总时长约 5.24秒
2025-07-29 20:06:57,361 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2670.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2671.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2672.mp4'

2025-07-29 20:06:57,361 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq31kx2hy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq31kx2hy\temp_combined.mp4
2025-07-29 20:06:57,502 - INFO - 合并后的视频时长: 5.31秒，目标音频时长: 4.80秒
2025-07-29 20:06:57,502 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq31kx2hy\temp_combined.mp4 -ss 0 -to 4.8 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 20:06:57,809 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:57,809 - INFO - 目标音频时长: 4.80秒
2025-07-29 20:06:57,809 - INFO - 实际视频时长: 4.82秒
2025-07-29 20:06:57,809 - INFO - 时长差异: 0.02秒 (0.48%)
2025-07-29 20:06:57,809 - INFO - ==========================================
2025-07-29 20:06:57,809 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:57,809 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 20:06:57,810 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq31kx2hy
2025-07-29 20:06:57,854 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:57,854 - INFO -   - 音频时长: 4.80秒
2025-07-29 20:06:57,854 - INFO -   - 视频时长: 4.82秒
2025-07-29 20:06:57,854 - INFO -   - 时长差异: 0.02秒 (0.48%)
2025-07-29 20:06:57,854 - INFO - 
----- 处理字幕 #70 的方案 #2 -----
2025-07-29 20:06:57,854 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 20:06:57,855 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5ud46eby
2025-07-29 20:06:57,855 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2670.mp4 (确认存在: True)
2025-07-29 20:06:57,855 - INFO - 添加场景ID=2670，时长=1.52秒，累计时长=1.52秒
2025-07-29 20:06:57,855 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2672.mp4 (确认存在: True)
2025-07-29 20:06:57,855 - INFO - 添加场景ID=2672，时长=2.32秒，累计时长=3.84秒
2025-07-29 20:06:57,855 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2673.mp4 (确认存在: True)
2025-07-29 20:06:57,855 - INFO - 添加场景ID=2673，时长=1.24秒，累计时长=5.08秒
2025-07-29 20:06:57,856 - INFO - 准备合并 3 个场景文件，总时长约 5.08秒
2025-07-29 20:06:57,856 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2670.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2672.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2673.mp4'

2025-07-29 20:06:57,856 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5ud46eby\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5ud46eby\temp_combined.mp4
2025-07-29 20:06:57,985 - INFO - 合并后的视频时长: 5.15秒，目标音频时长: 4.80秒
2025-07-29 20:06:57,985 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5ud46eby\temp_combined.mp4 -ss 0 -to 4.8 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 20:06:58,267 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:58,267 - INFO - 目标音频时长: 4.80秒
2025-07-29 20:06:58,267 - INFO - 实际视频时长: 4.82秒
2025-07-29 20:06:58,267 - INFO - 时长差异: 0.02秒 (0.48%)
2025-07-29 20:06:58,267 - INFO - ==========================================
2025-07-29 20:06:58,267 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:58,268 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 20:06:58,268 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5ud46eby
2025-07-29 20:06:58,315 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:58,315 - INFO -   - 音频时长: 4.80秒
2025-07-29 20:06:58,315 - INFO -   - 视频时长: 4.82秒
2025-07-29 20:06:58,315 - INFO -   - 时长差异: 0.02秒 (0.48%)
2025-07-29 20:06:58,315 - INFO - 
字幕 #70 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:58,315 - INFO - 生成的视频文件:
2025-07-29 20:06:58,315 - INFO -   1. F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 20:06:58,315 - INFO -   2. F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 20:06:58,315 - INFO - ========== 字幕 #70 处理结束 ==========

