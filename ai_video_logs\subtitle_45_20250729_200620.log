2025-07-29 20:06:20,212 - INFO - ========== 字幕 #45 处理开始 ==========
2025-07-29 20:06:20,212 - INFO - 字幕内容: 为了拿到研讨会的入场券，女人和新欢决定去机场围堵An先生传说中从不露面的妹妹。
2025-07-29 20:06:20,212 - INFO - 字幕序号: [1223, 1234]
2025-07-29 20:06:20,212 - INFO - 音频文件详情:
2025-07-29 20:06:20,212 - INFO -   - 路径: output\45.wav
2025-07-29 20:06:20,212 - INFO -   - 时长: 5.50秒
2025-07-29 20:06:20,212 - INFO -   - 验证音频时长: 5.50秒
2025-07-29 20:06:20,213 - INFO - 字幕时间戳信息:
2025-07-29 20:06:20,213 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:20,213 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:20,213 - INFO -   - 根据生成的音频时长(5.50秒)已调整字幕时间戳
2025-07-29 20:06:20,213 - INFO - ========== 新模式：为字幕 #45 生成4套场景方案 ==========
2025-07-29 20:06:20,213 - INFO - 字幕序号列表: [1223, 1234]
2025-07-29 20:06:20,213 - INFO - 
--- 生成方案 #1：基于字幕序号 #1223 ---
2025-07-29 20:06:20,213 - INFO - 开始为单个字幕序号 #1223 匹配场景，目标时长: 5.50秒
2025-07-29 20:06:20,213 - INFO - 开始查找字幕序号 [1223] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:20,213 - INFO - 找到related_overlap场景: scene_id=1309, 字幕#1223
2025-07-29 20:06:20,215 - INFO - 字幕 #1223 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:20,215 - INFO - 字幕序号 #1223 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:20,215 - INFO - 选择第一个overlap场景作为起点: scene_id=1309
2025-07-29 20:06:20,215 - INFO - 添加起点场景: scene_id=1309, 时长=0.96秒, 累计时长=0.96秒
2025-07-29 20:06:20,215 - INFO - 起点场景时长不足，需要延伸填充 4.54秒
2025-07-29 20:06:20,215 - INFO - 起点场景在原始列表中的索引: 1308
2025-07-29 20:06:20,215 - INFO - 延伸添加场景: scene_id=1310 (完整时长 2.44秒)
2025-07-29 20:06:20,215 - INFO - 累计时长: 3.40秒
2025-07-29 20:06:20,215 - INFO - 延伸添加场景: scene_id=1311 (完整时长 1.80秒)
2025-07-29 20:06:20,215 - INFO - 累计时长: 5.20秒
2025-07-29 20:06:20,215 - INFO - 延伸添加场景: scene_id=1312 (裁剪至 0.30秒)
2025-07-29 20:06:20,215 - INFO - 累计时长: 5.50秒
2025-07-29 20:06:20,215 - INFO - 字幕序号 #1223 场景匹配完成，共选择 4 个场景，总时长: 5.50秒
2025-07-29 20:06:20,215 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:20,215 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:20,215 - INFO - 
--- 生成方案 #2：基于字幕序号 #1234 ---
2025-07-29 20:06:20,215 - INFO - 开始为单个字幕序号 #1234 匹配场景，目标时长: 5.50秒
2025-07-29 20:06:20,215 - INFO - 开始查找字幕序号 [1234] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:20,215 - INFO - 找到related_overlap场景: scene_id=1313, 字幕#1234
2025-07-29 20:06:20,215 - INFO - 找到related_overlap场景: scene_id=1314, 字幕#1234
2025-07-29 20:06:20,216 - INFO - 字幕 #1234 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:20,216 - INFO - 字幕序号 #1234 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:20,216 - INFO - 选择第一个overlap场景作为起点: scene_id=1313
2025-07-29 20:06:20,216 - INFO - 添加起点场景: scene_id=1313, 时长=3.76秒, 累计时长=3.76秒
2025-07-29 20:06:20,216 - INFO - 起点场景时长不足，需要延伸填充 1.74秒
2025-07-29 20:06:20,216 - INFO - 起点场景在原始列表中的索引: 1312
2025-07-29 20:06:20,216 - INFO - 延伸添加场景: scene_id=1314 (裁剪至 1.74秒)
2025-07-29 20:06:20,216 - INFO - 累计时长: 5.50秒
2025-07-29 20:06:20,216 - INFO - 字幕序号 #1234 场景匹配完成，共选择 2 个场景，总时长: 5.50秒
2025-07-29 20:06:20,216 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:06:20,216 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:20,216 - INFO - ========== 当前模式：为字幕 #45 生成 1 套场景方案 ==========
2025-07-29 20:06:20,216 - INFO - 开始查找字幕序号 [1223, 1234] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:20,217 - INFO - 找到related_overlap场景: scene_id=1309, 字幕#1223
2025-07-29 20:06:20,217 - INFO - 找到related_overlap场景: scene_id=1313, 字幕#1234
2025-07-29 20:06:20,217 - INFO - 找到related_overlap场景: scene_id=1314, 字幕#1234
2025-07-29 20:06:20,217 - INFO - 字幕 #1223 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:20,217 - INFO - 字幕 #1234 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:20,217 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:20,217 - INFO - 开始生成方案 #1
2025-07-29 20:06:20,217 - INFO - 方案 #1: 为字幕#1223选择初始化overlap场景id=1309
2025-07-29 20:06:20,217 - INFO - 方案 #1: 为字幕#1234选择初始化overlap场景id=1313
2025-07-29 20:06:20,217 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-07-29 20:06:20,217 - INFO - 方案 #1: 额外添加overlap场景id=1314, 当前总时长=7.08秒
2025-07-29 20:06:20,217 - INFO - 方案 #1: 额外between选择后，当前总时长=7.08秒
2025-07-29 20:06:20,217 - INFO - 方案 #1: 场景总时长(7.08秒)大于音频时长(5.50秒)，需要裁剪
2025-07-29 20:06:20,217 - INFO - 调整前总时长: 7.08秒, 目标时长: 5.50秒
2025-07-29 20:06:20,217 - INFO - 需要裁剪 1.58秒
2025-07-29 20:06:20,217 - INFO - 裁剪最长场景ID=1313：从3.76秒裁剪至2.18秒
2025-07-29 20:06:20,217 - INFO - 调整后总时长: 5.50秒，与目标时长差异: 0.00秒
2025-07-29 20:06:20,217 - INFO - 方案 #1 调整/填充后最终总时长: 5.50秒
2025-07-29 20:06:20,217 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:20,217 - INFO - ========== 当前模式：字幕 #45 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:20,217 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:20,217 - INFO - ========== 新模式：字幕 #45 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:20,217 - INFO - 
----- 处理字幕 #45 的方案 #1 -----
2025-07-29 20:06:20,219 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 20:06:20,219 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkain7_m8
2025-07-29 20:06:20,219 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1309.mp4 (确认存在: True)
2025-07-29 20:06:20,219 - INFO - 添加场景ID=1309，时长=0.96秒，累计时长=0.96秒
2025-07-29 20:06:20,219 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1310.mp4 (确认存在: True)
2025-07-29 20:06:20,219 - INFO - 添加场景ID=1310，时长=2.44秒，累计时长=3.40秒
2025-07-29 20:06:20,219 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1311.mp4 (确认存在: True)
2025-07-29 20:06:20,220 - INFO - 添加场景ID=1311，时长=1.80秒，累计时长=5.20秒
2025-07-29 20:06:20,220 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1312.mp4 (确认存在: True)
2025-07-29 20:06:20,220 - INFO - 添加场景ID=1312，时长=2.24秒，累计时长=7.44秒
2025-07-29 20:06:20,220 - INFO - 准备合并 4 个场景文件，总时长约 7.44秒
2025-07-29 20:06:20,220 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1309.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1310.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1311.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1312.mp4'

2025-07-29 20:06:20,220 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkain7_m8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkain7_m8\temp_combined.mp4
2025-07-29 20:06:20,360 - INFO - 合并后的视频时长: 7.53秒，目标音频时长: 5.50秒
2025-07-29 20:06:20,360 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkain7_m8\temp_combined.mp4 -ss 0 -to 5.5 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 20:06:20,687 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:20,687 - INFO - 目标音频时长: 5.50秒
2025-07-29 20:06:20,687 - INFO - 实际视频时长: 5.54秒
2025-07-29 20:06:20,687 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:20,687 - INFO - ==========================================
2025-07-29 20:06:20,687 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:20,687 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 20:06:20,688 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkain7_m8
2025-07-29 20:06:20,731 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:20,731 - INFO -   - 音频时长: 5.50秒
2025-07-29 20:06:20,731 - INFO -   - 视频时长: 5.54秒
2025-07-29 20:06:20,731 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:20,732 - INFO - 
----- 处理字幕 #45 的方案 #2 -----
2025-07-29 20:06:20,732 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 20:06:20,732 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6eb81572
2025-07-29 20:06:20,732 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1313.mp4 (确认存在: True)
2025-07-29 20:06:20,732 - INFO - 添加场景ID=1313，时长=3.76秒，累计时长=3.76秒
2025-07-29 20:06:20,732 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1314.mp4 (确认存在: True)
2025-07-29 20:06:20,732 - INFO - 添加场景ID=1314，时长=2.36秒，累计时长=6.12秒
2025-07-29 20:06:20,733 - INFO - 准备合并 2 个场景文件，总时长约 6.12秒
2025-07-29 20:06:20,733 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1313.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1314.mp4'

2025-07-29 20:06:20,733 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6eb81572\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6eb81572\temp_combined.mp4
2025-07-29 20:06:20,863 - INFO - 合并后的视频时长: 6.17秒，目标音频时长: 5.50秒
2025-07-29 20:06:20,863 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6eb81572\temp_combined.mp4 -ss 0 -to 5.5 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 20:06:21,167 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:21,167 - INFO - 目标音频时长: 5.50秒
2025-07-29 20:06:21,167 - INFO - 实际视频时长: 5.54秒
2025-07-29 20:06:21,167 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:21,167 - INFO - ==========================================
2025-07-29 20:06:21,167 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:21,167 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 20:06:21,167 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6eb81572
2025-07-29 20:06:21,212 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:21,212 - INFO -   - 音频时长: 5.50秒
2025-07-29 20:06:21,212 - INFO -   - 视频时长: 5.54秒
2025-07-29 20:06:21,212 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:21,212 - INFO - 
----- 处理字幕 #45 的方案 #3 -----
2025-07-29 20:06:21,212 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 20:06:21,212 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpplmjvoce
2025-07-29 20:06:21,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1309.mp4 (确认存在: True)
2025-07-29 20:06:21,213 - INFO - 添加场景ID=1309，时长=0.96秒，累计时长=0.96秒
2025-07-29 20:06:21,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1313.mp4 (确认存在: True)
2025-07-29 20:06:21,213 - INFO - 添加场景ID=1313，时长=3.76秒，累计时长=4.72秒
2025-07-29 20:06:21,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1314.mp4 (确认存在: True)
2025-07-29 20:06:21,213 - INFO - 添加场景ID=1314，时长=2.36秒，累计时长=7.08秒
2025-07-29 20:06:21,213 - INFO - 准备合并 3 个场景文件，总时长约 7.08秒
2025-07-29 20:06:21,213 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1309.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1313.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1314.mp4'

2025-07-29 20:06:21,213 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpplmjvoce\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpplmjvoce\temp_combined.mp4
2025-07-29 20:06:21,339 - INFO - 合并后的视频时长: 7.15秒，目标音频时长: 5.50秒
2025-07-29 20:06:21,339 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpplmjvoce\temp_combined.mp4 -ss 0 -to 5.5 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 20:06:21,646 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:21,646 - INFO - 目标音频时长: 5.50秒
2025-07-29 20:06:21,646 - INFO - 实际视频时长: 5.54秒
2025-07-29 20:06:21,646 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:21,646 - INFO - ==========================================
2025-07-29 20:06:21,646 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:21,646 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 20:06:21,647 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpplmjvoce
2025-07-29 20:06:21,697 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:21,697 - INFO -   - 音频时长: 5.50秒
2025-07-29 20:06:21,697 - INFO -   - 视频时长: 5.54秒
2025-07-29 20:06:21,697 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:21,698 - INFO - 
字幕 #45 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:21,698 - INFO - 生成的视频文件:
2025-07-29 20:06:21,698 - INFO -   1. F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 20:06:21,698 - INFO -   2. F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 20:06:21,698 - INFO -   3. F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 20:06:21,698 - INFO - ========== 字幕 #45 处理结束 ==========

