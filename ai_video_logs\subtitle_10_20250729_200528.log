2025-07-29 20:05:28,815 - INFO - ========== 字幕 #10 处理开始 ==========
2025-07-29 20:05:28,815 - INFO - 字幕内容: 可女人却以为这只是男人博取关注的伎俩，一脸不耐烦地指责他总拿妹妹的病说事。
2025-07-29 20:05:28,815 - INFO - 字幕序号: [166, 171]
2025-07-29 20:05:28,815 - INFO - 音频文件详情:
2025-07-29 20:05:28,815 - INFO -   - 路径: output\10.wav
2025-07-29 20:05:28,815 - INFO -   - 时长: 5.39秒
2025-07-29 20:05:28,815 - INFO -   - 验证音频时长: 5.39秒
2025-07-29 20:05:28,816 - INFO - 字幕时间戳信息:
2025-07-29 20:05:28,816 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:28,816 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:28,816 - INFO -   - 根据生成的音频时长(5.39秒)已调整字幕时间戳
2025-07-29 20:05:28,816 - INFO - ========== 新模式：为字幕 #10 生成4套场景方案 ==========
2025-07-29 20:05:28,816 - INFO - 字幕序号列表: [166, 171]
2025-07-29 20:05:28,816 - INFO - 
--- 生成方案 #1：基于字幕序号 #166 ---
2025-07-29 20:05:28,816 - INFO - 开始为单个字幕序号 #166 匹配场景，目标时长: 5.39秒
2025-07-29 20:05:28,816 - INFO - 开始查找字幕序号 [166] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:28,816 - INFO - 找到related_overlap场景: scene_id=186, 字幕#166
2025-07-29 20:05:28,816 - INFO - 找到related_overlap场景: scene_id=187, 字幕#166
2025-07-29 20:05:28,817 - INFO - 字幕 #166 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:28,817 - INFO - 字幕序号 #166 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:28,817 - INFO - 选择第一个overlap场景作为起点: scene_id=186
2025-07-29 20:05:28,817 - INFO - 添加起点场景: scene_id=186, 时长=2.40秒, 累计时长=2.40秒
2025-07-29 20:05:28,817 - INFO - 起点场景时长不足，需要延伸填充 2.99秒
2025-07-29 20:05:28,817 - INFO - 起点场景在原始列表中的索引: 185
2025-07-29 20:05:28,817 - INFO - 延伸添加场景: scene_id=187 (完整时长 0.80秒)
2025-07-29 20:05:28,817 - INFO - 累计时长: 3.20秒
2025-07-29 20:05:28,817 - INFO - 延伸添加场景: scene_id=188 (完整时长 1.64秒)
2025-07-29 20:05:28,817 - INFO - 累计时长: 4.84秒
2025-07-29 20:05:28,817 - INFO - 延伸添加场景: scene_id=189 (裁剪至 0.55秒)
2025-07-29 20:05:28,817 - INFO - 累计时长: 5.39秒
2025-07-29 20:05:28,817 - INFO - 字幕序号 #166 场景匹配完成，共选择 4 个场景，总时长: 5.39秒
2025-07-29 20:05:28,817 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:05:28,818 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:05:28,818 - INFO - 
--- 生成方案 #2：基于字幕序号 #171 ---
2025-07-29 20:05:28,818 - INFO - 开始为单个字幕序号 #171 匹配场景，目标时长: 5.39秒
2025-07-29 20:05:28,818 - INFO - 开始查找字幕序号 [171] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:28,818 - INFO - 找到related_overlap场景: scene_id=190, 字幕#171
2025-07-29 20:05:28,819 - INFO - 字幕 #171 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:28,819 - INFO - 字幕序号 #171 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:28,819 - INFO - 选择第一个overlap场景作为起点: scene_id=190
2025-07-29 20:05:28,819 - INFO - 添加起点场景: scene_id=190, 时长=1.56秒, 累计时长=1.56秒
2025-07-29 20:05:28,819 - INFO - 起点场景时长不足，需要延伸填充 3.83秒
2025-07-29 20:05:28,819 - INFO - 起点场景在原始列表中的索引: 189
2025-07-29 20:05:28,819 - INFO - 延伸添加场景: scene_id=191 (完整时长 1.96秒)
2025-07-29 20:05:28,819 - INFO - 累计时长: 3.52秒
2025-07-29 20:05:28,819 - INFO - 延伸添加场景: scene_id=192 (完整时长 1.28秒)
2025-07-29 20:05:28,819 - INFO - 累计时长: 4.80秒
2025-07-29 20:05:28,819 - INFO - 延伸添加场景: scene_id=193 (裁剪至 0.59秒)
2025-07-29 20:05:28,819 - INFO - 累计时长: 5.39秒
2025-07-29 20:05:28,819 - INFO - 字幕序号 #171 场景匹配完成，共选择 4 个场景，总时长: 5.39秒
2025-07-29 20:05:28,819 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:28,819 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:28,819 - INFO - ========== 当前模式：为字幕 #10 生成 1 套场景方案 ==========
2025-07-29 20:05:28,819 - INFO - 开始查找字幕序号 [166, 171] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:28,819 - INFO - 找到related_overlap场景: scene_id=186, 字幕#166
2025-07-29 20:05:28,819 - INFO - 找到related_overlap场景: scene_id=187, 字幕#166
2025-07-29 20:05:28,819 - INFO - 找到related_overlap场景: scene_id=190, 字幕#171
2025-07-29 20:05:28,820 - INFO - 字幕 #166 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:28,820 - INFO - 字幕 #171 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:28,820 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:28,820 - INFO - 开始生成方案 #1
2025-07-29 20:05:28,820 - INFO - 方案 #1: 为字幕#166选择初始化overlap场景id=187
2025-07-29 20:05:28,820 - INFO - 方案 #1: 为字幕#171选择初始化overlap场景id=190
2025-07-29 20:05:28,820 - INFO - 方案 #1: 初始选择后，当前总时长=2.36秒
2025-07-29 20:05:28,820 - INFO - 方案 #1: 额外添加overlap场景id=186, 当前总时长=4.76秒
2025-07-29 20:05:28,820 - INFO - 方案 #1: 额外between选择后，当前总时长=4.76秒
2025-07-29 20:05:28,820 - INFO - 方案 #1: 场景总时长(4.76秒)小于音频时长(5.39秒)，需要延伸填充
2025-07-29 20:05:28,820 - INFO - 方案 #1: 最后一个场景ID: 186
2025-07-29 20:05:28,820 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 185
2025-07-29 20:05:28,820 - INFO - 方案 #1: 需要填充时长: 0.63秒
2025-07-29 20:05:28,820 - INFO - 方案 #1: 跳过已使用的场景: scene_id=187
2025-07-29 20:05:28,820 - INFO - 方案 #1: 追加场景 scene_id=188 (裁剪至 0.63秒)
2025-07-29 20:05:28,820 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:28,820 - INFO - 方案 #1 调整/填充后最终总时长: 5.39秒
2025-07-29 20:05:28,820 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:28,820 - INFO - ========== 当前模式：字幕 #10 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:28,820 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:28,820 - INFO - ========== 新模式：字幕 #10 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:28,820 - INFO - 
----- 处理字幕 #10 的方案 #1 -----
2025-07-29 20:05:28,820 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 20:05:28,820 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm0msilgy
2025-07-29 20:05:28,821 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\186.mp4 (确认存在: True)
2025-07-29 20:05:28,821 - INFO - 添加场景ID=186，时长=2.40秒，累计时长=2.40秒
2025-07-29 20:05:28,821 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\187.mp4 (确认存在: True)
2025-07-29 20:05:28,821 - INFO - 添加场景ID=187，时长=0.80秒，累计时长=3.20秒
2025-07-29 20:05:28,821 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\188.mp4 (确认存在: True)
2025-07-29 20:05:28,821 - INFO - 添加场景ID=188，时长=1.64秒，累计时长=4.84秒
2025-07-29 20:05:28,821 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\189.mp4 (确认存在: True)
2025-07-29 20:05:28,821 - INFO - 添加场景ID=189，时长=1.60秒，累计时长=6.44秒
2025-07-29 20:05:28,821 - INFO - 准备合并 4 个场景文件，总时长约 6.44秒
2025-07-29 20:05:28,821 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/186.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/187.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/188.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/189.mp4'

2025-07-29 20:05:28,821 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpm0msilgy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpm0msilgy\temp_combined.mp4
2025-07-29 20:05:28,977 - INFO - 合并后的视频时长: 6.53秒，目标音频时长: 5.39秒
2025-07-29 20:05:28,977 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpm0msilgy\temp_combined.mp4 -ss 0 -to 5.388 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 20:05:29,299 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:29,300 - INFO - 目标音频时长: 5.39秒
2025-07-29 20:05:29,300 - INFO - 实际视频时长: 5.42秒
2025-07-29 20:05:29,300 - INFO - 时长差异: 0.04秒 (0.65%)
2025-07-29 20:05:29,300 - INFO - ==========================================
2025-07-29 20:05:29,300 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:29,300 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 20:05:29,300 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm0msilgy
2025-07-29 20:05:29,343 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:29,343 - INFO -   - 音频时长: 5.39秒
2025-07-29 20:05:29,344 - INFO -   - 视频时长: 5.42秒
2025-07-29 20:05:29,344 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-07-29 20:05:29,344 - INFO - 
----- 处理字幕 #10 的方案 #2 -----
2025-07-29 20:05:29,344 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 20:05:29,344 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpja9irudl
2025-07-29 20:05:29,345 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\190.mp4 (确认存在: True)
2025-07-29 20:05:29,345 - INFO - 添加场景ID=190，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:05:29,345 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\191.mp4 (确认存在: True)
2025-07-29 20:05:29,345 - INFO - 添加场景ID=191，时长=1.96秒，累计时长=3.52秒
2025-07-29 20:05:29,345 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\192.mp4 (确认存在: True)
2025-07-29 20:05:29,345 - INFO - 添加场景ID=192，时长=1.28秒，累计时长=4.80秒
2025-07-29 20:05:29,345 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\193.mp4 (确认存在: True)
2025-07-29 20:05:29,345 - INFO - 添加场景ID=193，时长=1.44秒，累计时长=6.24秒
2025-07-29 20:05:29,345 - INFO - 准备合并 4 个场景文件，总时长约 6.24秒
2025-07-29 20:05:29,345 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/190.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/191.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/192.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/193.mp4'

2025-07-29 20:05:29,345 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpja9irudl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpja9irudl\temp_combined.mp4
2025-07-29 20:05:29,524 - INFO - 合并后的视频时长: 6.33秒，目标音频时长: 5.39秒
2025-07-29 20:05:29,524 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpja9irudl\temp_combined.mp4 -ss 0 -to 5.388 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 20:05:29,873 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:29,873 - INFO - 目标音频时长: 5.39秒
2025-07-29 20:05:29,874 - INFO - 实际视频时长: 5.42秒
2025-07-29 20:05:29,874 - INFO - 时长差异: 0.04秒 (0.65%)
2025-07-29 20:05:29,874 - INFO - ==========================================
2025-07-29 20:05:29,874 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:29,874 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 20:05:29,874 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpja9irudl
2025-07-29 20:05:29,918 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:29,918 - INFO -   - 音频时长: 5.39秒
2025-07-29 20:05:29,918 - INFO -   - 视频时长: 5.42秒
2025-07-29 20:05:29,918 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-07-29 20:05:29,918 - INFO - 
----- 处理字幕 #10 的方案 #3 -----
2025-07-29 20:05:29,918 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 20:05:29,918 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9e6138ze
2025-07-29 20:05:29,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\187.mp4 (确认存在: True)
2025-07-29 20:05:29,919 - INFO - 添加场景ID=187，时长=0.80秒，累计时长=0.80秒
2025-07-29 20:05:29,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\190.mp4 (确认存在: True)
2025-07-29 20:05:29,919 - INFO - 添加场景ID=190，时长=1.56秒，累计时长=2.36秒
2025-07-29 20:05:29,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\186.mp4 (确认存在: True)
2025-07-29 20:05:29,919 - INFO - 添加场景ID=186，时长=2.40秒，累计时长=4.76秒
2025-07-29 20:05:29,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\188.mp4 (确认存在: True)
2025-07-29 20:05:29,919 - INFO - 添加场景ID=188，时长=1.64秒，累计时长=6.40秒
2025-07-29 20:05:29,919 - INFO - 准备合并 4 个场景文件，总时长约 6.40秒
2025-07-29 20:05:29,919 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/187.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/190.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/186.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/188.mp4'

2025-07-29 20:05:29,919 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9e6138ze\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9e6138ze\temp_combined.mp4
2025-07-29 20:05:30,063 - INFO - 合并后的视频时长: 6.49秒，目标音频时长: 5.39秒
2025-07-29 20:05:30,063 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9e6138ze\temp_combined.mp4 -ss 0 -to 5.388 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 20:05:30,410 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:30,410 - INFO - 目标音频时长: 5.39秒
2025-07-29 20:05:30,410 - INFO - 实际视频时长: 5.42秒
2025-07-29 20:05:30,410 - INFO - 时长差异: 0.04秒 (0.65%)
2025-07-29 20:05:30,410 - INFO - ==========================================
2025-07-29 20:05:30,410 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:30,410 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 20:05:30,411 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9e6138ze
2025-07-29 20:05:30,455 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:30,455 - INFO -   - 音频时长: 5.39秒
2025-07-29 20:05:30,455 - INFO -   - 视频时长: 5.42秒
2025-07-29 20:05:30,455 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-07-29 20:05:30,455 - INFO - 
字幕 #10 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:30,455 - INFO - 生成的视频文件:
2025-07-29 20:05:30,455 - INFO -   1. F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 20:05:30,455 - INFO -   2. F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 20:05:30,455 - INFO -   3. F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 20:05:30,455 - INFO - ========== 字幕 #10 处理结束 ==========

