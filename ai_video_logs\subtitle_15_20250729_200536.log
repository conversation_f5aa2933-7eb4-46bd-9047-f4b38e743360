2025-07-29 20:05:36,603 - INFO - ========== 字幕 #15 处理开始 ==========
2025-07-29 20:05:36,603 - INFO - 字幕内容: 医生痛惜地告诉他，哪怕早来十分钟，妹妹都还有救，可现在，一切都已无法挽回。
2025-07-29 20:05:36,603 - INFO - 字幕序号: [112, 116]
2025-07-29 20:05:36,603 - INFO - 音频文件详情:
2025-07-29 20:05:36,603 - INFO -   - 路径: output\15.wav
2025-07-29 20:05:36,603 - INFO -   - 时长: 5.55秒
2025-07-29 20:05:36,603 - INFO -   - 验证音频时长: 5.55秒
2025-07-29 20:05:36,603 - INFO - 字幕时间戳信息:
2025-07-29 20:05:36,603 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:36,603 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:36,604 - INFO -   - 根据生成的音频时长(5.55秒)已调整字幕时间戳
2025-07-29 20:05:36,604 - INFO - ========== 新模式：为字幕 #15 生成4套场景方案 ==========
2025-07-29 20:05:36,604 - INFO - 字幕序号列表: [112, 116]
2025-07-29 20:05:36,604 - INFO - 
--- 生成方案 #1：基于字幕序号 #112 ---
2025-07-29 20:05:36,604 - INFO - 开始为单个字幕序号 #112 匹配场景，目标时长: 5.55秒
2025-07-29 20:05:36,604 - INFO - 开始查找字幕序号 [112] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:36,604 - INFO - 找到related_overlap场景: scene_id=131, 字幕#112
2025-07-29 20:05:36,604 - INFO - 找到related_overlap场景: scene_id=133, 字幕#112
2025-07-29 20:05:36,605 - INFO - 字幕 #112 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:36,605 - INFO - 字幕序号 #112 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:36,605 - INFO - 选择第一个overlap场景作为起点: scene_id=131
2025-07-29 20:05:36,605 - INFO - 添加起点场景: scene_id=131, 时长=0.96秒, 累计时长=0.96秒
2025-07-29 20:05:36,605 - INFO - 起点场景时长不足，需要延伸填充 4.59秒
2025-07-29 20:05:36,605 - INFO - 起点场景在原始列表中的索引: 130
2025-07-29 20:05:36,605 - INFO - 延伸添加场景: scene_id=132 (完整时长 0.84秒)
2025-07-29 20:05:36,605 - INFO - 累计时长: 1.80秒
2025-07-29 20:05:36,605 - INFO - 延伸添加场景: scene_id=133 (完整时长 1.72秒)
2025-07-29 20:05:36,605 - INFO - 累计时长: 3.52秒
2025-07-29 20:05:36,605 - INFO - 延伸添加场景: scene_id=134 (完整时长 1.88秒)
2025-07-29 20:05:36,605 - INFO - 累计时长: 5.40秒
2025-07-29 20:05:36,605 - INFO - 延伸添加场景: scene_id=135 (裁剪至 0.16秒)
2025-07-29 20:05:36,605 - INFO - 累计时长: 5.55秒
2025-07-29 20:05:36,605 - INFO - 字幕序号 #112 场景匹配完成，共选择 5 个场景，总时长: 5.55秒
2025-07-29 20:05:36,605 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:36,605 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:36,605 - INFO - 
--- 生成方案 #2：基于字幕序号 #116 ---
2025-07-29 20:05:36,605 - INFO - 开始为单个字幕序号 #116 匹配场景，目标时长: 5.55秒
2025-07-29 20:05:36,605 - INFO - 开始查找字幕序号 [116] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:36,605 - INFO - 找到related_overlap场景: scene_id=135, 字幕#116
2025-07-29 20:05:36,606 - INFO - 找到related_between场景: scene_id=136, 字幕#116
2025-07-29 20:05:36,607 - INFO - 字幕 #116 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:36,607 - INFO - 字幕序号 #116 找到 0 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:36,607 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=136
2025-07-29 20:05:36,607 - INFO - 添加起点场景: scene_id=136, 时长=2.60秒, 累计时长=2.60秒
2025-07-29 20:05:36,607 - INFO - 起点场景时长不足，需要延伸填充 2.95秒
2025-07-29 20:05:36,607 - INFO - 起点场景在原始列表中的索引: 135
2025-07-29 20:05:36,607 - INFO - 延伸添加场景: scene_id=137 (裁剪至 2.95秒)
2025-07-29 20:05:36,607 - INFO - 累计时长: 5.55秒
2025-07-29 20:05:36,607 - INFO - 字幕序号 #116 场景匹配完成，共选择 2 个场景，总时长: 5.55秒
2025-07-29 20:05:36,607 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:05:36,607 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:36,607 - INFO - ========== 当前模式：为字幕 #15 生成 1 套场景方案 ==========
2025-07-29 20:05:36,607 - INFO - 开始查找字幕序号 [112, 116] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:36,607 - INFO - 找到related_overlap场景: scene_id=131, 字幕#112
2025-07-29 20:05:36,607 - INFO - 找到related_overlap场景: scene_id=133, 字幕#112
2025-07-29 20:05:36,607 - INFO - 找到related_overlap场景: scene_id=135, 字幕#116
2025-07-29 20:05:36,608 - INFO - 找到related_between场景: scene_id=136, 字幕#116
2025-07-29 20:05:36,608 - INFO - 字幕 #112 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:36,608 - INFO - 字幕 #116 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:36,608 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:05:36,608 - INFO - 开始生成方案 #1
2025-07-29 20:05:36,608 - INFO - 方案 #1: 为字幕#112选择初始化overlap场景id=133
2025-07-29 20:05:36,608 - INFO - 方案 #1: 为字幕#116选择初始化overlap场景id=135
2025-07-29 20:05:36,608 - INFO - 方案 #1: 初始选择后，当前总时长=5.72秒
2025-07-29 20:05:36,608 - INFO - 方案 #1: 额外between选择后，当前总时长=5.72秒
2025-07-29 20:05:36,608 - INFO - 方案 #1: 场景总时长(5.72秒)大于音频时长(5.55秒)，需要裁剪
2025-07-29 20:05:36,608 - INFO - 调整前总时长: 5.72秒, 目标时长: 5.55秒
2025-07-29 20:05:36,608 - INFO - 需要裁剪 0.17秒
2025-07-29 20:05:36,608 - INFO - 裁剪最长场景ID=135：从4.00秒裁剪至3.83秒
2025-07-29 20:05:36,608 - INFO - 调整后总时长: 5.55秒，与目标时长差异: 0.00秒
2025-07-29 20:05:36,608 - INFO - 方案 #1 调整/填充后最终总时长: 5.55秒
2025-07-29 20:05:36,608 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:36,608 - INFO - ========== 当前模式：字幕 #15 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:36,608 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:36,608 - INFO - ========== 新模式：字幕 #15 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:36,608 - INFO - 
----- 处理字幕 #15 的方案 #1 -----
2025-07-29 20:05:36,608 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 20:05:36,609 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcojrlwb6
2025-07-29 20:05:36,609 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-07-29 20:05:36,609 - INFO - 添加场景ID=131，时长=0.96秒，累计时长=0.96秒
2025-07-29 20:05:36,609 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\132.mp4 (确认存在: True)
2025-07-29 20:05:36,609 - INFO - 添加场景ID=132，时长=0.84秒，累计时长=1.80秒
2025-07-29 20:05:36,609 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\133.mp4 (确认存在: True)
2025-07-29 20:05:36,609 - INFO - 添加场景ID=133，时长=1.72秒，累计时长=3.52秒
2025-07-29 20:05:36,609 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\134.mp4 (确认存在: True)
2025-07-29 20:05:36,609 - INFO - 添加场景ID=134，时长=1.88秒，累计时长=5.40秒
2025-07-29 20:05:36,609 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\135.mp4 (确认存在: True)
2025-07-29 20:05:36,609 - INFO - 添加场景ID=135，时长=4.00秒，累计时长=9.40秒
2025-07-29 20:05:36,609 - INFO - 场景总时长(9.40秒)已达到音频时长(5.55秒)的1.5倍，停止添加场景
2025-07-29 20:05:36,609 - INFO - 准备合并 5 个场景文件，总时长约 9.40秒
2025-07-29 20:05:36,609 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/132.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/133.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/134.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/135.mp4'

2025-07-29 20:05:36,609 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcojrlwb6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcojrlwb6\temp_combined.mp4
2025-07-29 20:05:36,782 - INFO - 合并后的视频时长: 9.52秒，目标音频时长: 5.55秒
2025-07-29 20:05:36,782 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcojrlwb6\temp_combined.mp4 -ss 0 -to 5.551 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 20:05:37,143 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:37,143 - INFO - 目标音频时长: 5.55秒
2025-07-29 20:05:37,143 - INFO - 实际视频时长: 5.58秒
2025-07-29 20:05:37,143 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:37,143 - INFO - ==========================================
2025-07-29 20:05:37,143 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:37,143 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 20:05:37,144 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcojrlwb6
2025-07-29 20:05:37,188 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:37,188 - INFO -   - 音频时长: 5.55秒
2025-07-29 20:05:37,188 - INFO -   - 视频时长: 5.58秒
2025-07-29 20:05:37,188 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:37,188 - INFO - 
----- 处理字幕 #15 的方案 #2 -----
2025-07-29 20:05:37,188 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 20:05:37,188 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbv_cmj8y
2025-07-29 20:05:37,189 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\136.mp4 (确认存在: True)
2025-07-29 20:05:37,189 - INFO - 添加场景ID=136，时长=2.60秒，累计时长=2.60秒
2025-07-29 20:05:37,189 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\137.mp4 (确认存在: True)
2025-07-29 20:05:37,189 - INFO - 添加场景ID=137，时长=3.40秒，累计时长=6.00秒
2025-07-29 20:05:37,189 - INFO - 准备合并 2 个场景文件，总时长约 6.00秒
2025-07-29 20:05:37,189 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/136.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/137.mp4'

2025-07-29 20:05:37,189 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbv_cmj8y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbv_cmj8y\temp_combined.mp4
2025-07-29 20:05:37,338 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 5.55秒
2025-07-29 20:05:37,340 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbv_cmj8y\temp_combined.mp4 -ss 0 -to 5.551 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 20:05:37,638 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:37,638 - INFO - 目标音频时长: 5.55秒
2025-07-29 20:05:37,638 - INFO - 实际视频时长: 5.58秒
2025-07-29 20:05:37,638 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:37,638 - INFO - ==========================================
2025-07-29 20:05:37,638 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:37,638 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 20:05:37,640 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbv_cmj8y
2025-07-29 20:05:37,684 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:37,684 - INFO -   - 音频时长: 5.55秒
2025-07-29 20:05:37,684 - INFO -   - 视频时长: 5.58秒
2025-07-29 20:05:37,684 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:37,684 - INFO - 
----- 处理字幕 #15 的方案 #3 -----
2025-07-29 20:05:37,684 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 20:05:37,684 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkj1itz4w
2025-07-29 20:05:37,685 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\133.mp4 (确认存在: True)
2025-07-29 20:05:37,685 - INFO - 添加场景ID=133，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:05:37,685 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\135.mp4 (确认存在: True)
2025-07-29 20:05:37,685 - INFO - 添加场景ID=135，时长=4.00秒，累计时长=5.72秒
2025-07-29 20:05:37,685 - INFO - 准备合并 2 个场景文件，总时长约 5.72秒
2025-07-29 20:05:37,685 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/133.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/135.mp4'

2025-07-29 20:05:37,685 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkj1itz4w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkj1itz4w\temp_combined.mp4
2025-07-29 20:05:37,803 - INFO - 合并后的视频时长: 5.77秒，目标音频时长: 5.55秒
2025-07-29 20:05:37,803 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkj1itz4w\temp_combined.mp4 -ss 0 -to 5.551 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 20:05:38,122 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:38,122 - INFO - 目标音频时长: 5.55秒
2025-07-29 20:05:38,122 - INFO - 实际视频时长: 5.58秒
2025-07-29 20:05:38,122 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:38,122 - INFO - ==========================================
2025-07-29 20:05:38,122 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:38,122 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 20:05:38,122 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkj1itz4w
2025-07-29 20:05:38,168 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:38,168 - INFO -   - 音频时长: 5.55秒
2025-07-29 20:05:38,168 - INFO -   - 视频时长: 5.58秒
2025-07-29 20:05:38,168 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 20:05:38,168 - INFO - 
字幕 #15 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:38,168 - INFO - 生成的视频文件:
2025-07-29 20:05:38,168 - INFO -   1. F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 20:05:38,168 - INFO -   2. F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 20:05:38,168 - INFO -   3. F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 20:05:38,168 - INFO - ========== 字幕 #15 处理结束 ==========

