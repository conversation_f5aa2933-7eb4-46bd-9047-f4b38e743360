2025-07-29 20:05:50,787 - INFO - ========== 字幕 #25 处理开始 ==========
2025-07-29 20:05:50,787 - INFO - 字幕内容: 女人彻底慌了，她搬出妹妹当挡箭牌，以为男人会因此回心转意，却不知男人早已问过妹妹，妹妹同意了。
2025-07-29 20:05:50,787 - INFO - 字幕序号: [443, 446]
2025-07-29 20:05:50,788 - INFO - 音频文件详情:
2025-07-29 20:05:50,788 - INFO -   - 路径: output\25.wav
2025-07-29 20:05:50,788 - INFO -   - 时长: 5.59秒
2025-07-29 20:05:50,788 - INFO -   - 验证音频时长: 5.59秒
2025-07-29 20:05:50,788 - INFO - 字幕时间戳信息:
2025-07-29 20:05:50,788 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:50,788 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:50,788 - INFO -   - 根据生成的音频时长(5.59秒)已调整字幕时间戳
2025-07-29 20:05:50,788 - INFO - ========== 新模式：为字幕 #25 生成4套场景方案 ==========
2025-07-29 20:05:50,788 - INFO - 字幕序号列表: [443, 446]
2025-07-29 20:05:50,788 - INFO - 
--- 生成方案 #1：基于字幕序号 #443 ---
2025-07-29 20:05:50,788 - INFO - 开始为单个字幕序号 #443 匹配场景，目标时长: 5.59秒
2025-07-29 20:05:50,788 - INFO - 开始查找字幕序号 [443] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:50,788 - INFO - 找到related_overlap场景: scene_id=486, 字幕#443
2025-07-29 20:05:50,788 - INFO - 找到related_overlap场景: scene_id=487, 字幕#443
2025-07-29 20:05:50,789 - INFO - 字幕 #443 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:50,789 - INFO - 字幕序号 #443 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:50,789 - INFO - 选择第一个overlap场景作为起点: scene_id=486
2025-07-29 20:05:50,789 - INFO - 添加起点场景: scene_id=486, 时长=1.84秒, 累计时长=1.84秒
2025-07-29 20:05:50,790 - INFO - 起点场景时长不足，需要延伸填充 3.75秒
2025-07-29 20:05:50,790 - INFO - 起点场景在原始列表中的索引: 485
2025-07-29 20:05:50,790 - INFO - 延伸添加场景: scene_id=487 (完整时长 2.76秒)
2025-07-29 20:05:50,790 - INFO - 累计时长: 4.60秒
2025-07-29 20:05:50,790 - INFO - 延伸添加场景: scene_id=488 (裁剪至 0.99秒)
2025-07-29 20:05:50,790 - INFO - 累计时长: 5.59秒
2025-07-29 20:05:50,790 - INFO - 字幕序号 #443 场景匹配完成，共选择 3 个场景，总时长: 5.59秒
2025-07-29 20:05:50,790 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:50,790 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:50,790 - INFO - 
--- 生成方案 #2：基于字幕序号 #446 ---
2025-07-29 20:05:50,790 - INFO - 开始为单个字幕序号 #446 匹配场景，目标时长: 5.59秒
2025-07-29 20:05:50,790 - INFO - 开始查找字幕序号 [446] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:50,790 - INFO - 找到related_overlap场景: scene_id=490, 字幕#446
2025-07-29 20:05:50,791 - INFO - 找到related_between场景: scene_id=489, 字幕#446
2025-07-29 20:05:50,791 - INFO - 找到related_between场景: scene_id=491, 字幕#446
2025-07-29 20:05:50,791 - INFO - 找到related_between场景: scene_id=492, 字幕#446
2025-07-29 20:05:50,791 - INFO - 字幕 #446 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:05:50,791 - INFO - 字幕序号 #446 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 20:05:50,791 - INFO - 选择第一个overlap场景作为起点: scene_id=490
2025-07-29 20:05:50,791 - INFO - 添加起点场景: scene_id=490, 时长=0.84秒, 累计时长=0.84秒
2025-07-29 20:05:50,791 - INFO - 起点场景时长不足，需要延伸填充 4.75秒
2025-07-29 20:05:50,791 - INFO - 起点场景在原始列表中的索引: 489
2025-07-29 20:05:50,791 - INFO - 延伸添加场景: scene_id=491 (完整时长 0.88秒)
2025-07-29 20:05:50,791 - INFO - 累计时长: 1.72秒
2025-07-29 20:05:50,791 - INFO - 延伸添加场景: scene_id=492 (完整时长 0.72秒)
2025-07-29 20:05:50,791 - INFO - 累计时长: 2.44秒
2025-07-29 20:05:50,791 - INFO - 延伸添加场景: scene_id=493 (裁剪至 3.15秒)
2025-07-29 20:05:50,791 - INFO - 累计时长: 5.59秒
2025-07-29 20:05:50,791 - INFO - 字幕序号 #446 场景匹配完成，共选择 4 个场景，总时长: 5.59秒
2025-07-29 20:05:50,791 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:50,791 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:50,791 - INFO - ========== 当前模式：为字幕 #25 生成 1 套场景方案 ==========
2025-07-29 20:05:50,791 - INFO - 开始查找字幕序号 [443, 446] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:50,792 - INFO - 找到related_overlap场景: scene_id=486, 字幕#443
2025-07-29 20:05:50,792 - INFO - 找到related_overlap场景: scene_id=487, 字幕#443
2025-07-29 20:05:50,792 - INFO - 找到related_overlap场景: scene_id=490, 字幕#446
2025-07-29 20:05:50,792 - INFO - 找到related_between场景: scene_id=489, 字幕#446
2025-07-29 20:05:50,792 - INFO - 找到related_between场景: scene_id=491, 字幕#446
2025-07-29 20:05:50,792 - INFO - 找到related_between场景: scene_id=492, 字幕#446
2025-07-29 20:05:50,793 - INFO - 字幕 #443 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:50,793 - INFO - 字幕 #446 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:05:50,793 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:05:50,793 - INFO - 开始生成方案 #1
2025-07-29 20:05:50,793 - INFO - 方案 #1: 为字幕#443选择初始化overlap场景id=487
2025-07-29 20:05:50,793 - INFO - 方案 #1: 为字幕#446选择初始化overlap场景id=490
2025-07-29 20:05:50,793 - INFO - 方案 #1: 初始选择后，当前总时长=3.60秒
2025-07-29 20:05:50,793 - INFO - 方案 #1: 额外添加overlap场景id=486, 当前总时长=5.44秒
2025-07-29 20:05:50,793 - INFO - 方案 #1: 额外between选择后，当前总时长=5.44秒
2025-07-29 20:05:50,793 - INFO - 方案 #1: 额外添加between场景id=489, 当前总时长=6.40秒
2025-07-29 20:05:50,793 - INFO - 方案 #1: 场景总时长(6.40秒)大于音频时长(5.59秒)，需要裁剪
2025-07-29 20:05:50,793 - INFO - 调整前总时长: 6.40秒, 目标时长: 5.59秒
2025-07-29 20:05:50,793 - INFO - 需要裁剪 0.80秒
2025-07-29 20:05:50,793 - INFO - 裁剪最长场景ID=487：从2.76秒裁剪至1.95秒
2025-07-29 20:05:50,793 - INFO - 调整后总时长: 5.59秒，与目标时长差异: 0.00秒
2025-07-29 20:05:50,793 - INFO - 方案 #1 调整/填充后最终总时长: 5.59秒
2025-07-29 20:05:50,793 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:50,793 - INFO - ========== 当前模式：字幕 #25 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:50,793 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:50,793 - INFO - ========== 新模式：字幕 #25 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:50,793 - INFO - 
----- 处理字幕 #25 的方案 #1 -----
2025-07-29 20:05:50,793 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 20:05:50,793 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4fpw1bmk
2025-07-29 20:05:50,794 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\486.mp4 (确认存在: True)
2025-07-29 20:05:50,794 - INFO - 添加场景ID=486，时长=1.84秒，累计时长=1.84秒
2025-07-29 20:05:50,794 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\487.mp4 (确认存在: True)
2025-07-29 20:05:50,794 - INFO - 添加场景ID=487，时长=2.76秒，累计时长=4.60秒
2025-07-29 20:05:50,794 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\488.mp4 (确认存在: True)
2025-07-29 20:05:50,794 - INFO - 添加场景ID=488，时长=1.64秒，累计时长=6.24秒
2025-07-29 20:05:50,794 - INFO - 准备合并 3 个场景文件，总时长约 6.24秒
2025-07-29 20:05:50,794 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/486.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/487.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/488.mp4'

2025-07-29 20:05:50,794 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4fpw1bmk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4fpw1bmk\temp_combined.mp4
2025-07-29 20:05:50,912 - INFO - 合并后的视频时长: 6.31秒，目标音频时长: 5.59秒
2025-07-29 20:05:50,912 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4fpw1bmk\temp_combined.mp4 -ss 0 -to 5.592 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 20:05:51,240 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:51,240 - INFO - 目标音频时长: 5.59秒
2025-07-29 20:05:51,240 - INFO - 实际视频时长: 5.62秒
2025-07-29 20:05:51,240 - INFO - 时长差异: 0.03秒 (0.55%)
2025-07-29 20:05:51,240 - INFO - ==========================================
2025-07-29 20:05:51,240 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:51,240 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 20:05:51,240 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4fpw1bmk
2025-07-29 20:05:51,285 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:51,285 - INFO -   - 音频时长: 5.59秒
2025-07-29 20:05:51,285 - INFO -   - 视频时长: 5.62秒
2025-07-29 20:05:51,285 - INFO -   - 时长差异: 0.03秒 (0.55%)
2025-07-29 20:05:51,285 - INFO - 
----- 处理字幕 #25 的方案 #2 -----
2025-07-29 20:05:51,285 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 20:05:51,285 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj9h2pilu
2025-07-29 20:05:51,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\490.mp4 (确认存在: True)
2025-07-29 20:05:51,285 - INFO - 添加场景ID=490，时长=0.84秒，累计时长=0.84秒
2025-07-29 20:05:51,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\491.mp4 (确认存在: True)
2025-07-29 20:05:51,285 - INFO - 添加场景ID=491，时长=0.88秒，累计时长=1.72秒
2025-07-29 20:05:51,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\492.mp4 (确认存在: True)
2025-07-29 20:05:51,285 - INFO - 添加场景ID=492，时长=0.72秒，累计时长=2.44秒
2025-07-29 20:05:51,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\493.mp4 (确认存在: True)
2025-07-29 20:05:51,285 - INFO - 添加场景ID=493，时长=3.52秒，累计时长=5.96秒
2025-07-29 20:05:51,285 - INFO - 准备合并 4 个场景文件，总时长约 5.96秒
2025-07-29 20:05:51,287 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/490.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/491.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/492.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/493.mp4'

2025-07-29 20:05:51,287 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpj9h2pilu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpj9h2pilu\temp_combined.mp4
2025-07-29 20:05:51,445 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 5.59秒
2025-07-29 20:05:51,446 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpj9h2pilu\temp_combined.mp4 -ss 0 -to 5.592 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 20:05:51,771 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:51,772 - INFO - 目标音频时长: 5.59秒
2025-07-29 20:05:51,772 - INFO - 实际视频时长: 5.62秒
2025-07-29 20:05:51,772 - INFO - 时长差异: 0.03秒 (0.55%)
2025-07-29 20:05:51,772 - INFO - ==========================================
2025-07-29 20:05:51,772 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:51,772 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 20:05:51,772 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj9h2pilu
2025-07-29 20:05:51,818 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:51,818 - INFO -   - 音频时长: 5.59秒
2025-07-29 20:05:51,818 - INFO -   - 视频时长: 5.62秒
2025-07-29 20:05:51,818 - INFO -   - 时长差异: 0.03秒 (0.55%)
2025-07-29 20:05:51,818 - INFO - 
----- 处理字幕 #25 的方案 #3 -----
2025-07-29 20:05:51,818 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 20:05:51,818 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpakxrsnzl
2025-07-29 20:05:51,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\487.mp4 (确认存在: True)
2025-07-29 20:05:51,818 - INFO - 添加场景ID=487，时长=2.76秒，累计时长=2.76秒
2025-07-29 20:05:51,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\490.mp4 (确认存在: True)
2025-07-29 20:05:51,818 - INFO - 添加场景ID=490，时长=0.84秒，累计时长=3.60秒
2025-07-29 20:05:51,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\486.mp4 (确认存在: True)
2025-07-29 20:05:51,818 - INFO - 添加场景ID=486，时长=1.84秒，累计时长=5.44秒
2025-07-29 20:05:51,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\489.mp4 (确认存在: True)
2025-07-29 20:05:51,818 - INFO - 添加场景ID=489，时长=0.96秒，累计时长=6.40秒
2025-07-29 20:05:51,818 - INFO - 准备合并 4 个场景文件，总时长约 6.40秒
2025-07-29 20:05:51,819 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/487.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/490.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/486.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/489.mp4'

2025-07-29 20:05:51,819 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpakxrsnzl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpakxrsnzl\temp_combined.mp4
2025-07-29 20:05:51,965 - INFO - 合并后的视频时长: 6.49秒，目标音频时长: 5.59秒
2025-07-29 20:05:51,965 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpakxrsnzl\temp_combined.mp4 -ss 0 -to 5.592 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 20:05:52,287 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:52,287 - INFO - 目标音频时长: 5.59秒
2025-07-29 20:05:52,287 - INFO - 实际视频时长: 5.62秒
2025-07-29 20:05:52,287 - INFO - 时长差异: 0.03秒 (0.55%)
2025-07-29 20:05:52,287 - INFO - ==========================================
2025-07-29 20:05:52,288 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:52,288 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 20:05:52,288 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpakxrsnzl
2025-07-29 20:05:52,331 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:52,331 - INFO -   - 音频时长: 5.59秒
2025-07-29 20:05:52,332 - INFO -   - 视频时长: 5.62秒
2025-07-29 20:05:52,332 - INFO -   - 时长差异: 0.03秒 (0.55%)
2025-07-29 20:05:52,332 - INFO - 
字幕 #25 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:52,332 - INFO - 生成的视频文件:
2025-07-29 20:05:52,332 - INFO -   1. F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 20:05:52,332 - INFO -   2. F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 20:05:52,332 - INFO -   3. F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 20:05:52,332 - INFO - ========== 字幕 #25 处理结束 ==========

