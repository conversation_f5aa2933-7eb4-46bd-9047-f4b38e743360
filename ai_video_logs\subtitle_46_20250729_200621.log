2025-07-29 20:06:21,707 - INFO - ========== 字幕 #46 处理开始 ==========
2025-07-29 20:06:21,707 - INFO - 字幕内容: 机场里，女人远远望见一个女孩亲密地扑向一个熟悉的背影，她脱口而出：“安安！”
2025-07-29 20:06:21,707 - INFO - 字幕序号: [1260, 1262]
2025-07-29 20:06:21,707 - INFO - 音频文件详情:
2025-07-29 20:06:21,707 - INFO -   - 路径: output\46.wav
2025-07-29 20:06:21,707 - INFO -   - 时长: 4.53秒
2025-07-29 20:06:21,707 - INFO -   - 验证音频时长: 4.53秒
2025-07-29 20:06:21,707 - INFO - 字幕时间戳信息:
2025-07-29 20:06:21,709 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:21,709 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:21,709 - INFO -   - 根据生成的音频时长(4.53秒)已调整字幕时间戳
2025-07-29 20:06:21,709 - INFO - ========== 新模式：为字幕 #46 生成4套场景方案 ==========
2025-07-29 20:06:21,709 - INFO - 字幕序号列表: [1260, 1262]
2025-07-29 20:06:21,709 - INFO - 
--- 生成方案 #1：基于字幕序号 #1260 ---
2025-07-29 20:06:21,709 - INFO - 开始为单个字幕序号 #1260 匹配场景，目标时长: 4.53秒
2025-07-29 20:06:21,709 - INFO - 开始查找字幕序号 [1260] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:21,709 - INFO - 找到related_overlap场景: scene_id=1332, 字幕#1260
2025-07-29 20:06:21,709 - INFO - 找到related_overlap场景: scene_id=1333, 字幕#1260
2025-07-29 20:06:21,710 - INFO - 字幕 #1260 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:21,710 - INFO - 字幕序号 #1260 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:21,710 - INFO - 选择第一个overlap场景作为起点: scene_id=1332
2025-07-29 20:06:21,710 - INFO - 添加起点场景: scene_id=1332, 时长=1.52秒, 累计时长=1.52秒
2025-07-29 20:06:21,710 - INFO - 起点场景时长不足，需要延伸填充 3.01秒
2025-07-29 20:06:21,710 - INFO - 起点场景在原始列表中的索引: 1331
2025-07-29 20:06:21,710 - INFO - 延伸添加场景: scene_id=1333 (完整时长 1.08秒)
2025-07-29 20:06:21,710 - INFO - 累计时长: 2.60秒
2025-07-29 20:06:21,710 - INFO - 延伸添加场景: scene_id=1334 (完整时长 1.40秒)
2025-07-29 20:06:21,710 - INFO - 累计时长: 4.00秒
2025-07-29 20:06:21,710 - INFO - 延伸添加场景: scene_id=1335 (裁剪至 0.53秒)
2025-07-29 20:06:21,710 - INFO - 累计时长: 4.53秒
2025-07-29 20:06:21,710 - INFO - 字幕序号 #1260 场景匹配完成，共选择 4 个场景，总时长: 4.53秒
2025-07-29 20:06:21,710 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:21,710 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:21,710 - INFO - 
--- 生成方案 #2：基于字幕序号 #1262 ---
2025-07-29 20:06:21,710 - INFO - 开始为单个字幕序号 #1262 匹配场景，目标时长: 4.53秒
2025-07-29 20:06:21,710 - INFO - 开始查找字幕序号 [1262] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:21,711 - INFO - 找到related_overlap场景: scene_id=1335, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 找到related_overlap场景: scene_id=1336, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 找到related_between场景: scene_id=1337, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 找到related_between场景: scene_id=1338, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 找到related_between场景: scene_id=1339, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 找到related_between场景: scene_id=1340, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 找到related_between场景: scene_id=1341, 字幕#1262
2025-07-29 20:06:21,711 - INFO - 字幕 #1262 找到 2 个overlap场景, 5 个between场景
2025-07-29 20:06:21,711 - INFO - 字幕序号 #1262 找到 1 个可用overlap场景, 5 个可用between场景
2025-07-29 20:06:21,711 - INFO - 选择第一个overlap场景作为起点: scene_id=1336
2025-07-29 20:06:21,711 - INFO - 添加起点场景: scene_id=1336, 时长=0.96秒, 累计时长=0.96秒
2025-07-29 20:06:21,711 - INFO - 起点场景时长不足，需要延伸填充 3.57秒
2025-07-29 20:06:21,711 - INFO - 起点场景在原始列表中的索引: 1335
2025-07-29 20:06:21,711 - INFO - 延伸添加场景: scene_id=1337 (裁剪至 3.57秒)
2025-07-29 20:06:21,711 - INFO - 累计时长: 4.53秒
2025-07-29 20:06:21,711 - INFO - 字幕序号 #1262 场景匹配完成，共选择 2 个场景，总时长: 4.53秒
2025-07-29 20:06:21,711 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:06:21,711 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:21,711 - INFO - ========== 当前模式：为字幕 #46 生成 1 套场景方案 ==========
2025-07-29 20:06:21,711 - INFO - 开始查找字幕序号 [1260, 1262] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:21,712 - INFO - 找到related_overlap场景: scene_id=1332, 字幕#1260
2025-07-29 20:06:21,712 - INFO - 找到related_overlap场景: scene_id=1333, 字幕#1260
2025-07-29 20:06:21,712 - INFO - 找到related_overlap场景: scene_id=1335, 字幕#1262
2025-07-29 20:06:21,712 - INFO - 找到related_overlap场景: scene_id=1336, 字幕#1262
2025-07-29 20:06:21,712 - INFO - 找到related_between场景: scene_id=1337, 字幕#1262
2025-07-29 20:06:21,712 - INFO - 找到related_between场景: scene_id=1338, 字幕#1262
2025-07-29 20:06:21,712 - INFO - 找到related_between场景: scene_id=1339, 字幕#1262
2025-07-29 20:06:21,712 - INFO - 找到related_between场景: scene_id=1340, 字幕#1262
2025-07-29 20:06:21,712 - INFO - 找到related_between场景: scene_id=1341, 字幕#1262
2025-07-29 20:06:21,713 - INFO - 字幕 #1260 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:21,713 - INFO - 字幕 #1262 找到 2 个overlap场景, 5 个between场景
2025-07-29 20:06:21,713 - INFO - 共收集 4 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 20:06:21,713 - INFO - 开始生成方案 #1
2025-07-29 20:06:21,713 - INFO - 方案 #1: 为字幕#1260选择初始化overlap场景id=1332
2025-07-29 20:06:21,713 - INFO - 方案 #1: 为字幕#1262选择初始化overlap场景id=1336
2025-07-29 20:06:21,713 - INFO - 方案 #1: 初始选择后，当前总时长=2.48秒
2025-07-29 20:06:21,713 - INFO - 方案 #1: 额外添加overlap场景id=1333, 当前总时长=3.56秒
2025-07-29 20:06:21,713 - INFO - 方案 #1: 额外添加overlap场景id=1335, 当前总时长=4.56秒
2025-07-29 20:06:21,713 - INFO - 方案 #1: 额外between选择后，当前总时长=4.56秒
2025-07-29 20:06:21,713 - INFO - 方案 #1: 场景总时长(4.56秒)大于音频时长(4.53秒)，需要裁剪
2025-07-29 20:06:21,713 - INFO - 调整前总时长: 4.56秒, 目标时长: 4.53秒
2025-07-29 20:06:21,713 - INFO - 需要裁剪 0.03秒
2025-07-29 20:06:21,713 - INFO - 裁剪最长场景ID=1332：从1.52秒裁剪至1.49秒
2025-07-29 20:06:21,713 - INFO - 调整后总时长: 4.53秒，与目标时长差异: 0.00秒
2025-07-29 20:06:21,713 - INFO - 方案 #1 调整/填充后最终总时长: 4.53秒
2025-07-29 20:06:21,713 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:21,713 - INFO - ========== 当前模式：字幕 #46 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:21,713 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:21,713 - INFO - ========== 新模式：字幕 #46 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:21,713 - INFO - 
----- 处理字幕 #46 的方案 #1 -----
2025-07-29 20:06:21,713 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 20:06:21,713 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpegaryejg
2025-07-29 20:06:21,714 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1332.mp4 (确认存在: True)
2025-07-29 20:06:21,714 - INFO - 添加场景ID=1332，时长=1.52秒，累计时长=1.52秒
2025-07-29 20:06:21,714 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1333.mp4 (确认存在: True)
2025-07-29 20:06:21,714 - INFO - 添加场景ID=1333，时长=1.08秒，累计时长=2.60秒
2025-07-29 20:06:21,714 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1334.mp4 (确认存在: True)
2025-07-29 20:06:21,714 - INFO - 添加场景ID=1334，时长=1.40秒，累计时长=4.00秒
2025-07-29 20:06:21,714 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1335.mp4 (确认存在: True)
2025-07-29 20:06:21,714 - INFO - 添加场景ID=1335，时长=1.00秒，累计时长=5.00秒
2025-07-29 20:06:21,714 - INFO - 准备合并 4 个场景文件，总时长约 5.00秒
2025-07-29 20:06:21,714 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1332.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1333.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1334.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1335.mp4'

2025-07-29 20:06:21,714 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpegaryejg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpegaryejg\temp_combined.mp4
2025-07-29 20:06:21,872 - INFO - 合并后的视频时长: 5.09秒，目标音频时长: 4.53秒
2025-07-29 20:06:21,872 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpegaryejg\temp_combined.mp4 -ss 0 -to 4.529 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 20:06:22,185 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:22,185 - INFO - 目标音频时长: 4.53秒
2025-07-29 20:06:22,185 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:06:22,185 - INFO - 时长差异: 0.05秒 (1.19%)
2025-07-29 20:06:22,185 - INFO - ==========================================
2025-07-29 20:06:22,185 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:22,185 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 20:06:22,185 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpegaryejg
2025-07-29 20:06:22,232 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:22,232 - INFO -   - 音频时长: 4.53秒
2025-07-29 20:06:22,232 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:06:22,232 - INFO -   - 时长差异: 0.05秒 (1.19%)
2025-07-29 20:06:22,232 - INFO - 
----- 处理字幕 #46 的方案 #2 -----
2025-07-29 20:06:22,232 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 20:06:22,245 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcbt8g_kf
2025-07-29 20:06:22,245 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1336.mp4 (确认存在: True)
2025-07-29 20:06:22,246 - INFO - 添加场景ID=1336，时长=0.96秒，累计时长=0.96秒
2025-07-29 20:06:22,246 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1337.mp4 (确认存在: True)
2025-07-29 20:06:22,246 - INFO - 添加场景ID=1337，时长=4.00秒，累计时长=4.96秒
2025-07-29 20:06:22,246 - INFO - 准备合并 2 个场景文件，总时长约 4.96秒
2025-07-29 20:06:22,246 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1336.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1337.mp4'

2025-07-29 20:06:22,246 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcbt8g_kf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcbt8g_kf\temp_combined.mp4
2025-07-29 20:06:22,380 - INFO - 合并后的视频时长: 5.01秒，目标音频时长: 4.53秒
2025-07-29 20:06:22,380 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcbt8g_kf\temp_combined.mp4 -ss 0 -to 4.529 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 20:06:22,677 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:22,677 - INFO - 目标音频时长: 4.53秒
2025-07-29 20:06:22,677 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:06:22,677 - INFO - 时长差异: 0.05秒 (1.19%)
2025-07-29 20:06:22,677 - INFO - ==========================================
2025-07-29 20:06:22,677 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:22,677 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 20:06:22,678 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcbt8g_kf
2025-07-29 20:06:22,726 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:22,726 - INFO -   - 音频时长: 4.53秒
2025-07-29 20:06:22,726 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:06:22,726 - INFO -   - 时长差异: 0.05秒 (1.19%)
2025-07-29 20:06:22,726 - INFO - 
----- 处理字幕 #46 的方案 #3 -----
2025-07-29 20:06:22,726 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 20:06:22,727 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnqwukqjn
2025-07-29 20:06:22,727 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1332.mp4 (确认存在: True)
2025-07-29 20:06:22,727 - INFO - 添加场景ID=1332，时长=1.52秒，累计时长=1.52秒
2025-07-29 20:06:22,727 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1336.mp4 (确认存在: True)
2025-07-29 20:06:22,727 - INFO - 添加场景ID=1336，时长=0.96秒，累计时长=2.48秒
2025-07-29 20:06:22,727 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1333.mp4 (确认存在: True)
2025-07-29 20:06:22,727 - INFO - 添加场景ID=1333，时长=1.08秒，累计时长=3.56秒
2025-07-29 20:06:22,727 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1335.mp4 (确认存在: True)
2025-07-29 20:06:22,727 - INFO - 添加场景ID=1335，时长=1.00秒，累计时长=4.56秒
2025-07-29 20:06:22,727 - INFO - 准备合并 4 个场景文件，总时长约 4.56秒
2025-07-29 20:06:22,727 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1332.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1336.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1333.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1335.mp4'

2025-07-29 20:06:22,727 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnqwukqjn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnqwukqjn\temp_combined.mp4
2025-07-29 20:06:22,871 - INFO - 合并后的视频时长: 4.65秒，目标音频时长: 4.53秒
2025-07-29 20:06:22,871 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnqwukqjn\temp_combined.mp4 -ss 0 -to 4.529 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 20:06:23,176 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:23,176 - INFO - 目标音频时长: 4.53秒
2025-07-29 20:06:23,176 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:06:23,176 - INFO - 时长差异: 0.05秒 (1.19%)
2025-07-29 20:06:23,176 - INFO - ==========================================
2025-07-29 20:06:23,176 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:23,176 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 20:06:23,177 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnqwukqjn
2025-07-29 20:06:23,222 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:23,223 - INFO -   - 音频时长: 4.53秒
2025-07-29 20:06:23,223 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:06:23,223 - INFO -   - 时长差异: 0.05秒 (1.19%)
2025-07-29 20:06:23,223 - INFO - 
字幕 #46 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:23,223 - INFO - 生成的视频文件:
2025-07-29 20:06:23,223 - INFO -   1. F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 20:06:23,223 - INFO -   2. F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 20:06:23,223 - INFO -   3. F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 20:06:23,223 - INFO - ========== 字幕 #46 处理结束 ==========

