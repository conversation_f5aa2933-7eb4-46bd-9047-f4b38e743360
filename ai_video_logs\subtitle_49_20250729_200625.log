2025-07-29 20:06:25,806 - INFO - ========== 字幕 #49 处理开始 ==========
2025-07-29 20:06:25,806 - INFO - 字幕内容: AI大会当天，女人和母亲、新欢一同出席，却发现他们的关系早已名存实亡，全靠演戏维持。
2025-07-29 20:06:25,806 - INFO - 字幕序号: [1963, 1970]
2025-07-29 20:06:25,807 - INFO - 音频文件详情:
2025-07-29 20:06:25,807 - INFO -   - 路径: output\49.wav
2025-07-29 20:06:25,807 - INFO -   - 时长: 7.45秒
2025-07-29 20:06:25,807 - INFO -   - 验证音频时长: 7.45秒
2025-07-29 20:06:25,807 - INFO - 字幕时间戳信息:
2025-07-29 20:06:25,816 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:25,816 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:25,816 - INFO -   - 根据生成的音频时长(7.45秒)已调整字幕时间戳
2025-07-29 20:06:25,816 - INFO - ========== 新模式：为字幕 #49 生成4套场景方案 ==========
2025-07-29 20:06:25,816 - INFO - 字幕序号列表: [1963, 1970]
2025-07-29 20:06:25,816 - INFO - 
--- 生成方案 #1：基于字幕序号 #1963 ---
2025-07-29 20:06:25,817 - INFO - 开始为单个字幕序号 #1963 匹配场景，目标时长: 7.45秒
2025-07-29 20:06:25,817 - INFO - 开始查找字幕序号 [1963] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:25,817 - INFO - 找到related_overlap场景: scene_id=1950, 字幕#1963
2025-07-29 20:06:25,817 - INFO - 找到related_overlap场景: scene_id=1951, 字幕#1963
2025-07-29 20:06:25,818 - INFO - 找到related_between场景: scene_id=1947, 字幕#1963
2025-07-29 20:06:25,819 - INFO - 找到related_between场景: scene_id=1948, 字幕#1963
2025-07-29 20:06:25,819 - INFO - 找到related_between场景: scene_id=1949, 字幕#1963
2025-07-29 20:06:25,819 - INFO - 找到related_between场景: scene_id=1952, 字幕#1963
2025-07-29 20:06:25,820 - INFO - 字幕 #1963 找到 2 个overlap场景, 4 个between场景
2025-07-29 20:06:25,820 - INFO - 字幕序号 #1963 找到 2 个可用overlap场景, 4 个可用between场景
2025-07-29 20:06:25,820 - INFO - 选择第一个overlap场景作为起点: scene_id=1950
2025-07-29 20:06:25,820 - INFO - 添加起点场景: scene_id=1950, 时长=0.96秒, 累计时长=0.96秒
2025-07-29 20:06:25,820 - INFO - 起点场景时长不足，需要延伸填充 6.49秒
2025-07-29 20:06:25,820 - INFO - 起点场景在原始列表中的索引: 1949
2025-07-29 20:06:25,820 - INFO - 延伸添加场景: scene_id=1951 (完整时长 0.80秒)
2025-07-29 20:06:25,820 - INFO - 累计时长: 1.76秒
2025-07-29 20:06:25,820 - INFO - 延伸添加场景: scene_id=1952 (完整时长 1.44秒)
2025-07-29 20:06:25,820 - INFO - 累计时长: 3.20秒
2025-07-29 20:06:25,820 - INFO - 延伸添加场景: scene_id=1953 (完整时长 2.56秒)
2025-07-29 20:06:25,820 - INFO - 累计时长: 5.76秒
2025-07-29 20:06:25,820 - INFO - 延伸添加场景: scene_id=1954 (完整时长 1.08秒)
2025-07-29 20:06:25,820 - INFO - 累计时长: 6.84秒
2025-07-29 20:06:25,820 - INFO - 延伸添加场景: scene_id=1955 (裁剪至 0.62秒)
2025-07-29 20:06:25,820 - INFO - 累计时长: 7.45秒
2025-07-29 20:06:25,820 - INFO - 字幕序号 #1963 场景匹配完成，共选择 6 个场景，总时长: 7.45秒
2025-07-29 20:06:25,821 - INFO - 方案 #1 生成成功，包含 6 个场景
2025-07-29 20:06:25,821 - INFO - 新模式：第1套方案的 6 个场景已加入全局已使用集合
2025-07-29 20:06:25,821 - INFO - 
--- 生成方案 #2：基于字幕序号 #1970 ---
2025-07-29 20:06:25,821 - INFO - 开始为单个字幕序号 #1970 匹配场景，目标时长: 7.45秒
2025-07-29 20:06:25,821 - INFO - 开始查找字幕序号 [1970] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:25,821 - INFO - 找到related_overlap场景: scene_id=1959, 字幕#1970
2025-07-29 20:06:25,822 - INFO - 找到related_overlap场景: scene_id=1960, 字幕#1970
2025-07-29 20:06:25,823 - INFO - 字幕 #1970 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:25,824 - INFO - 字幕序号 #1970 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:25,824 - INFO - 选择第一个overlap场景作为起点: scene_id=1959
2025-07-29 20:06:25,824 - INFO - 添加起点场景: scene_id=1959, 时长=1.28秒, 累计时长=1.28秒
2025-07-29 20:06:25,824 - INFO - 起点场景时长不足，需要延伸填充 6.17秒
2025-07-29 20:06:25,824 - INFO - 起点场景在原始列表中的索引: 1958
2025-07-29 20:06:25,824 - INFO - 延伸添加场景: scene_id=1960 (完整时长 0.60秒)
2025-07-29 20:06:25,824 - INFO - 累计时长: 1.88秒
2025-07-29 20:06:25,824 - INFO - 延伸添加场景: scene_id=1961 (完整时长 0.76秒)
2025-07-29 20:06:25,824 - INFO - 累计时长: 2.64秒
2025-07-29 20:06:25,824 - INFO - 延伸添加场景: scene_id=1962 (完整时长 1.20秒)
2025-07-29 20:06:25,824 - INFO - 累计时长: 3.84秒
2025-07-29 20:06:25,824 - INFO - 延伸添加场景: scene_id=1963 (完整时长 2.28秒)
2025-07-29 20:06:25,824 - INFO - 累计时长: 6.12秒
2025-07-29 20:06:25,824 - INFO - 延伸添加场景: scene_id=1964 (裁剪至 1.33秒)
2025-07-29 20:06:25,824 - INFO - 累计时长: 7.45秒
2025-07-29 20:06:25,824 - INFO - 字幕序号 #1970 场景匹配完成，共选择 6 个场景，总时长: 7.45秒
2025-07-29 20:06:25,824 - INFO - 方案 #2 生成成功，包含 6 个场景
2025-07-29 20:06:25,824 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:25,824 - INFO - ========== 当前模式：为字幕 #49 生成 1 套场景方案 ==========
2025-07-29 20:06:25,824 - INFO - 开始查找字幕序号 [1963, 1970] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:25,825 - INFO - 找到related_overlap场景: scene_id=1950, 字幕#1963
2025-07-29 20:06:25,825 - INFO - 找到related_overlap场景: scene_id=1951, 字幕#1963
2025-07-29 20:06:25,825 - INFO - 找到related_overlap场景: scene_id=1959, 字幕#1970
2025-07-29 20:06:25,825 - INFO - 找到related_overlap场景: scene_id=1960, 字幕#1970
2025-07-29 20:06:25,826 - INFO - 找到related_between场景: scene_id=1947, 字幕#1963
2025-07-29 20:06:25,826 - INFO - 找到related_between场景: scene_id=1948, 字幕#1963
2025-07-29 20:06:25,826 - INFO - 找到related_between场景: scene_id=1949, 字幕#1963
2025-07-29 20:06:25,826 - INFO - 找到related_between场景: scene_id=1952, 字幕#1963
2025-07-29 20:06:25,827 - INFO - 字幕 #1963 找到 2 个overlap场景, 4 个between场景
2025-07-29 20:06:25,827 - INFO - 字幕 #1970 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:25,827 - INFO - 共收集 4 个未使用的overlap场景和 4 个未使用的between场景
2025-07-29 20:06:25,827 - INFO - 开始生成方案 #1
2025-07-29 20:06:25,827 - INFO - 方案 #1: 为字幕#1963选择初始化overlap场景id=1951
2025-07-29 20:06:25,827 - INFO - 方案 #1: 为字幕#1970选择初始化overlap场景id=1960
2025-07-29 20:06:25,827 - INFO - 方案 #1: 初始选择后，当前总时长=1.40秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 额外添加overlap场景id=1959, 当前总时长=2.68秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 额外添加overlap场景id=1950, 当前总时长=3.64秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 额外between选择后，当前总时长=3.64秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 额外添加between场景id=1948, 当前总时长=4.96秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 额外添加between场景id=1949, 当前总时长=6.08秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 额外添加between场景id=1947, 当前总时长=10.84秒
2025-07-29 20:06:25,827 - INFO - 方案 #1: 场景总时长(10.84秒)大于音频时长(7.45秒)，需要裁剪
2025-07-29 20:06:25,827 - INFO - 调整前总时长: 10.84秒, 目标时长: 7.45秒
2025-07-29 20:06:25,827 - INFO - 需要裁剪 3.38秒
2025-07-29 20:06:25,827 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:25,827 - INFO - 裁剪场景ID=1947：从4.76秒裁剪至1.43秒
2025-07-29 20:06:25,827 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.05秒
2025-07-29 20:06:25,827 - INFO - 移除场景ID=1960，时长=0.60秒
2025-07-29 20:06:25,827 - INFO - 调整后总时长: 6.91秒，与目标时长差异: 0.55秒
2025-07-29 20:06:25,827 - INFO - 方案 #1 调整/填充后最终总时长: 6.91秒
2025-07-29 20:06:25,827 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:25,827 - INFO - ========== 当前模式：字幕 #49 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:25,827 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:25,827 - INFO - ========== 新模式：字幕 #49 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:25,827 - INFO - 
----- 处理字幕 #49 的方案 #1 -----
2025-07-29 20:06:25,827 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 20:06:25,828 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcnt3f8fd
2025-07-29 20:06:25,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1950.mp4 (确认存在: True)
2025-07-29 20:06:25,830 - INFO - 添加场景ID=1950，时长=0.96秒，累计时长=0.96秒
2025-07-29 20:06:25,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1951.mp4 (确认存在: True)
2025-07-29 20:06:25,830 - INFO - 添加场景ID=1951，时长=0.80秒，累计时长=1.76秒
2025-07-29 20:06:25,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1952.mp4 (确认存在: True)
2025-07-29 20:06:25,830 - INFO - 添加场景ID=1952，时长=1.44秒，累计时长=3.20秒
2025-07-29 20:06:25,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1953.mp4 (确认存在: True)
2025-07-29 20:06:25,830 - INFO - 添加场景ID=1953，时长=2.56秒，累计时长=5.76秒
2025-07-29 20:06:25,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1954.mp4 (确认存在: True)
2025-07-29 20:06:25,830 - INFO - 添加场景ID=1954，时长=1.08秒，累计时长=6.84秒
2025-07-29 20:06:25,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1955.mp4 (确认存在: True)
2025-07-29 20:06:25,830 - INFO - 添加场景ID=1955，时长=1.48秒，累计时长=8.32秒
2025-07-29 20:06:25,830 - INFO - 准备合并 6 个场景文件，总时长约 8.32秒
2025-07-29 20:06:25,830 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1950.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1951.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1952.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1953.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1954.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1955.mp4'

2025-07-29 20:06:25,830 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcnt3f8fd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcnt3f8fd\temp_combined.mp4
2025-07-29 20:06:26,030 - INFO - 合并后的视频时长: 8.46秒，目标音频时长: 7.45秒
2025-07-29 20:06:26,030 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcnt3f8fd\temp_combined.mp4 -ss 0 -to 7.452 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 20:06:26,440 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:26,440 - INFO - 目标音频时长: 7.45秒
2025-07-29 20:06:26,440 - INFO - 实际视频时长: 7.50秒
2025-07-29 20:06:26,440 - INFO - 时长差异: 0.05秒 (0.68%)
2025-07-29 20:06:26,440 - INFO - ==========================================
2025-07-29 20:06:26,440 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:26,440 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 20:06:26,441 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcnt3f8fd
2025-07-29 20:06:26,489 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:26,489 - INFO -   - 音频时长: 7.45秒
2025-07-29 20:06:26,489 - INFO -   - 视频时长: 7.50秒
2025-07-29 20:06:26,489 - INFO -   - 时长差异: 0.05秒 (0.68%)
2025-07-29 20:06:26,489 - INFO - 
----- 处理字幕 #49 的方案 #2 -----
2025-07-29 20:06:26,489 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 20:06:26,490 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcgyq1yqe
2025-07-29 20:06:26,490 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1959.mp4 (确认存在: True)
2025-07-29 20:06:26,490 - INFO - 添加场景ID=1959，时长=1.28秒，累计时长=1.28秒
2025-07-29 20:06:26,490 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1960.mp4 (确认存在: True)
2025-07-29 20:06:26,490 - INFO - 添加场景ID=1960，时长=0.60秒，累计时长=1.88秒
2025-07-29 20:06:26,490 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1961.mp4 (确认存在: True)
2025-07-29 20:06:26,490 - INFO - 添加场景ID=1961，时长=0.76秒，累计时长=2.64秒
2025-07-29 20:06:26,490 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1962.mp4 (确认存在: True)
2025-07-29 20:06:26,490 - INFO - 添加场景ID=1962，时长=1.20秒，累计时长=3.84秒
2025-07-29 20:06:26,490 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1963.mp4 (确认存在: True)
2025-07-29 20:06:26,491 - INFO - 添加场景ID=1963，时长=2.28秒，累计时长=6.12秒
2025-07-29 20:06:26,491 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1964.mp4 (确认存在: True)
2025-07-29 20:06:26,491 - INFO - 添加场景ID=1964，时长=2.60秒，累计时长=8.72秒
2025-07-29 20:06:26,491 - INFO - 准备合并 6 个场景文件，总时长约 8.72秒
2025-07-29 20:06:26,491 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1959.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1960.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1961.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1962.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1963.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1964.mp4'

2025-07-29 20:06:26,491 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcgyq1yqe\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcgyq1yqe\temp_combined.mp4
2025-07-29 20:06:26,703 - INFO - 合并后的视频时长: 8.86秒，目标音频时长: 7.45秒
2025-07-29 20:06:26,703 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcgyq1yqe\temp_combined.mp4 -ss 0 -to 7.452 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 20:06:27,168 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:27,168 - INFO - 目标音频时长: 7.45秒
2025-07-29 20:06:27,168 - INFO - 实际视频时长: 7.50秒
2025-07-29 20:06:27,168 - INFO - 时长差异: 0.05秒 (0.68%)
2025-07-29 20:06:27,168 - INFO - ==========================================
2025-07-29 20:06:27,168 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:27,168 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 20:06:27,169 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcgyq1yqe
2025-07-29 20:06:27,216 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:27,216 - INFO -   - 音频时长: 7.45秒
2025-07-29 20:06:27,216 - INFO -   - 视频时长: 7.50秒
2025-07-29 20:06:27,216 - INFO -   - 时长差异: 0.05秒 (0.68%)
2025-07-29 20:06:27,216 - INFO - 
----- 处理字幕 #49 的方案 #3 -----
2025-07-29 20:06:27,217 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 20:06:27,217 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9a31us2c
2025-07-29 20:06:27,217 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1951.mp4 (确认存在: True)
2025-07-29 20:06:27,217 - INFO - 添加场景ID=1951，时长=0.80秒，累计时长=0.80秒
2025-07-29 20:06:27,217 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1959.mp4 (确认存在: True)
2025-07-29 20:06:27,217 - INFO - 添加场景ID=1959，时长=1.28秒，累计时长=2.08秒
2025-07-29 20:06:27,217 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1950.mp4 (确认存在: True)
2025-07-29 20:06:27,217 - INFO - 添加场景ID=1950，时长=0.96秒，累计时长=3.04秒
2025-07-29 20:06:27,217 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1948.mp4 (确认存在: True)
2025-07-29 20:06:27,217 - INFO - 添加场景ID=1948，时长=1.32秒，累计时长=4.36秒
2025-07-29 20:06:27,217 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1949.mp4 (确认存在: True)
2025-07-29 20:06:27,217 - INFO - 添加场景ID=1949，时长=1.12秒，累计时长=5.48秒
2025-07-29 20:06:27,217 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1947.mp4 (确认存在: True)
2025-07-29 20:06:27,217 - INFO - 添加场景ID=1947，时长=4.76秒，累计时长=10.24秒
2025-07-29 20:06:27,219 - INFO - 准备合并 6 个场景文件，总时长约 10.24秒
2025-07-29 20:06:27,219 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1951.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1959.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1950.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1948.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1949.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1947.mp4'

2025-07-29 20:06:27,219 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9a31us2c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9a31us2c\temp_combined.mp4
2025-07-29 20:06:27,425 - INFO - 合并后的视频时长: 10.38秒，目标音频时长: 7.45秒
2025-07-29 20:06:27,425 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9a31us2c\temp_combined.mp4 -ss 0 -to 7.452 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 20:06:27,877 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:27,877 - INFO - 目标音频时长: 7.45秒
2025-07-29 20:06:27,877 - INFO - 实际视频时长: 7.50秒
2025-07-29 20:06:27,877 - INFO - 时长差异: 0.05秒 (0.68%)
2025-07-29 20:06:27,877 - INFO - ==========================================
2025-07-29 20:06:27,877 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:27,877 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 20:06:27,878 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9a31us2c
2025-07-29 20:06:27,930 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:27,930 - INFO -   - 音频时长: 7.45秒
2025-07-29 20:06:27,930 - INFO -   - 视频时长: 7.50秒
2025-07-29 20:06:27,930 - INFO -   - 时长差异: 0.05秒 (0.68%)
2025-07-29 20:06:27,930 - INFO - 
字幕 #49 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:27,930 - INFO - 生成的视频文件:
2025-07-29 20:06:27,930 - INFO -   1. F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 20:06:27,930 - INFO -   2. F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 20:06:27,930 - INFO -   3. F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 20:06:27,930 - INFO - ========== 字幕 #49 处理结束 ==========

