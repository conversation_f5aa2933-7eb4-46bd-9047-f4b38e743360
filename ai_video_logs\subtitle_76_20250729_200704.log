2025-07-29 20:07:04,828 - INFO - ========== 字幕 #76 处理开始 ==========
2025-07-29 20:07:04,828 - INFO - 字幕内容: 男人赶到后，不分青红皂白地指责女人，说她害死安安，现在又想害死念念，是个毒如蛇蝎的女人。
2025-07-29 20:07:04,828 - INFO - 字幕序号: [4112, 4116]
2025-07-29 20:07:04,828 - INFO - 音频文件详情:
2025-07-29 20:07:04,828 - INFO -   - 路径: output\76.wav
2025-07-29 20:07:04,828 - INFO -   - 时长: 5.79秒
2025-07-29 20:07:04,828 - INFO -   - 验证音频时长: 5.79秒
2025-07-29 20:07:04,829 - INFO - 字幕时间戳信息:
2025-07-29 20:07:04,829 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:04,829 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:04,829 - INFO -   - 根据生成的音频时长(5.79秒)已调整字幕时间戳
2025-07-29 20:07:04,829 - INFO - ========== 新模式：为字幕 #76 生成4套场景方案 ==========
2025-07-29 20:07:04,829 - INFO - 字幕序号列表: [4112, 4116]
2025-07-29 20:07:04,829 - INFO - 
--- 生成方案 #1：基于字幕序号 #4112 ---
2025-07-29 20:07:04,829 - INFO - 开始为单个字幕序号 #4112 匹配场景，目标时长: 5.79秒
2025-07-29 20:07:04,829 - INFO - 开始查找字幕序号 [4112] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:04,830 - INFO - 找到related_overlap场景: scene_id=3658, 字幕#4112
2025-07-29 20:07:04,830 - INFO - 字幕 #4112 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:04,830 - INFO - 字幕序号 #4112 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:04,830 - INFO - 选择第一个overlap场景作为起点: scene_id=3658
2025-07-29 20:07:04,830 - INFO - 添加起点场景: scene_id=3658, 时长=2.96秒, 累计时长=2.96秒
2025-07-29 20:07:04,830 - INFO - 起点场景时长不足，需要延伸填充 2.83秒
2025-07-29 20:07:04,830 - INFO - 起点场景在原始列表中的索引: 3657
2025-07-29 20:07:04,831 - INFO - 延伸添加场景: scene_id=3659 (完整时长 1.84秒)
2025-07-29 20:07:04,831 - INFO - 累计时长: 4.80秒
2025-07-29 20:07:04,831 - INFO - 延伸添加场景: scene_id=3660 (裁剪至 0.99秒)
2025-07-29 20:07:04,831 - INFO - 累计时长: 5.79秒
2025-07-29 20:07:04,831 - INFO - 字幕序号 #4112 场景匹配完成，共选择 3 个场景，总时长: 5.79秒
2025-07-29 20:07:04,831 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:07:04,831 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:07:04,831 - INFO - 
--- 生成方案 #2：基于字幕序号 #4116 ---
2025-07-29 20:07:04,831 - INFO - 开始为单个字幕序号 #4116 匹配场景，目标时长: 5.79秒
2025-07-29 20:07:04,831 - INFO - 开始查找字幕序号 [4116] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:04,831 - INFO - 找到related_overlap场景: scene_id=3660, 字幕#4116
2025-07-29 20:07:04,832 - INFO - 字幕 #4116 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:04,832 - INFO - 字幕序号 #4116 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:04,832 - ERROR - 字幕序号 #4116 没有找到任何可用的匹配场景
2025-07-29 20:07:04,832 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:07:04,832 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:07:04,832 - INFO - ========== 当前模式：为字幕 #76 生成 1 套场景方案 ==========
2025-07-29 20:07:04,832 - INFO - 开始查找字幕序号 [4112, 4116] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:04,833 - INFO - 找到related_overlap场景: scene_id=3658, 字幕#4112
2025-07-29 20:07:04,833 - INFO - 找到related_overlap场景: scene_id=3660, 字幕#4116
2025-07-29 20:07:04,833 - INFO - 字幕 #4112 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:04,833 - INFO - 字幕 #4116 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:04,833 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:07:04,833 - INFO - 开始生成方案 #1
2025-07-29 20:07:04,833 - INFO - 方案 #1: 为字幕#4112选择初始化overlap场景id=3658
2025-07-29 20:07:04,833 - INFO - 方案 #1: 为字幕#4116选择初始化overlap场景id=3660
2025-07-29 20:07:04,833 - INFO - 方案 #1: 初始选择后，当前总时长=5.20秒
2025-07-29 20:07:04,833 - INFO - 方案 #1: 额外between选择后，当前总时长=5.20秒
2025-07-29 20:07:04,833 - INFO - 方案 #1: 场景总时长(5.20秒)小于音频时长(5.79秒)，需要延伸填充
2025-07-29 20:07:04,833 - INFO - 方案 #1: 最后一个场景ID: 3660
2025-07-29 20:07:04,834 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 3659
2025-07-29 20:07:04,834 - INFO - 方案 #1: 需要填充时长: 0.59秒
2025-07-29 20:07:04,834 - INFO - 方案 #1: 追加场景 scene_id=3661 (裁剪至 0.59秒)
2025-07-29 20:07:04,834 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:07:04,834 - INFO - 方案 #1 调整/填充后最终总时长: 5.79秒
2025-07-29 20:07:04,834 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:04,834 - INFO - ========== 当前模式：字幕 #76 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:04,834 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:07:04,834 - INFO - ========== 新模式：字幕 #76 共生成 2 套有效场景方案 ==========
2025-07-29 20:07:04,834 - INFO - 
----- 处理字幕 #76 的方案 #1 -----
2025-07-29 20:07:04,834 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\76_1.mp4
2025-07-29 20:07:04,834 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjka42h6d
2025-07-29 20:07:04,835 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3658.mp4 (确认存在: True)
2025-07-29 20:07:04,835 - INFO - 添加场景ID=3658，时长=2.96秒，累计时长=2.96秒
2025-07-29 20:07:04,835 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3659.mp4 (确认存在: True)
2025-07-29 20:07:04,835 - INFO - 添加场景ID=3659，时长=1.84秒，累计时长=4.80秒
2025-07-29 20:07:04,835 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3660.mp4 (确认存在: True)
2025-07-29 20:07:04,835 - INFO - 添加场景ID=3660，时长=2.24秒，累计时长=7.04秒
2025-07-29 20:07:04,835 - INFO - 准备合并 3 个场景文件，总时长约 7.04秒
2025-07-29 20:07:04,835 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3658.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3659.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3660.mp4'

2025-07-29 20:07:04,835 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjka42h6d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjka42h6d\temp_combined.mp4
2025-07-29 20:07:04,960 - INFO - 合并后的视频时长: 7.11秒，目标音频时长: 5.79秒
2025-07-29 20:07:04,960 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjka42h6d\temp_combined.mp4 -ss 0 -to 5.792 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\76_1.mp4
2025-07-29 20:07:05,273 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:05,274 - INFO - 目标音频时长: 5.79秒
2025-07-29 20:07:05,274 - INFO - 实际视频时长: 5.82秒
2025-07-29 20:07:05,274 - INFO - 时长差异: 0.03秒 (0.54%)
2025-07-29 20:07:05,274 - INFO - ==========================================
2025-07-29 20:07:05,274 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:05,274 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\76_1.mp4
2025-07-29 20:07:05,274 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjka42h6d
2025-07-29 20:07:05,316 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:05,316 - INFO -   - 音频时长: 5.79秒
2025-07-29 20:07:05,316 - INFO -   - 视频时长: 5.82秒
2025-07-29 20:07:05,316 - INFO -   - 时长差异: 0.03秒 (0.54%)
2025-07-29 20:07:05,316 - INFO - 
----- 处理字幕 #76 的方案 #2 -----
2025-07-29 20:07:05,316 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\76_2.mp4
2025-07-29 20:07:05,316 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpn7xr6u6c
2025-07-29 20:07:05,318 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3658.mp4 (确认存在: True)
2025-07-29 20:07:05,318 - INFO - 添加场景ID=3658，时长=2.96秒，累计时长=2.96秒
2025-07-29 20:07:05,318 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3660.mp4 (确认存在: True)
2025-07-29 20:07:05,318 - INFO - 添加场景ID=3660，时长=2.24秒，累计时长=5.20秒
2025-07-29 20:07:05,318 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3661.mp4 (确认存在: True)
2025-07-29 20:07:05,318 - INFO - 添加场景ID=3661，时长=4.96秒，累计时长=10.16秒
2025-07-29 20:07:05,318 - INFO - 场景总时长(10.16秒)已达到音频时长(5.79秒)的1.5倍，停止添加场景
2025-07-29 20:07:05,318 - INFO - 准备合并 3 个场景文件，总时长约 10.16秒
2025-07-29 20:07:05,318 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3658.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3660.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3661.mp4'

2025-07-29 20:07:05,319 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpn7xr6u6c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpn7xr6u6c\temp_combined.mp4
2025-07-29 20:07:05,436 - INFO - 合并后的视频时长: 10.23秒，目标音频时长: 5.79秒
2025-07-29 20:07:05,436 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpn7xr6u6c\temp_combined.mp4 -ss 0 -to 5.792 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\76_2.mp4
2025-07-29 20:07:05,758 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:05,758 - INFO - 目标音频时长: 5.79秒
2025-07-29 20:07:05,758 - INFO - 实际视频时长: 5.82秒
2025-07-29 20:07:05,758 - INFO - 时长差异: 0.03秒 (0.54%)
2025-07-29 20:07:05,758 - INFO - ==========================================
2025-07-29 20:07:05,758 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:05,758 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\76_2.mp4
2025-07-29 20:07:05,759 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpn7xr6u6c
2025-07-29 20:07:05,802 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:05,802 - INFO -   - 音频时长: 5.79秒
2025-07-29 20:07:05,802 - INFO -   - 视频时长: 5.82秒
2025-07-29 20:07:05,802 - INFO -   - 时长差异: 0.03秒 (0.54%)
2025-07-29 20:07:05,802 - INFO - 
字幕 #76 处理完成，成功生成 2/2 套方案
2025-07-29 20:07:05,802 - INFO - 生成的视频文件:
2025-07-29 20:07:05,802 - INFO -   1. F:/github/aicut_auto/newcut_ai\76_1.mp4
2025-07-29 20:07:05,802 - INFO -   2. F:/github/aicut_auto/newcut_ai\76_2.mp4
2025-07-29 20:07:05,802 - INFO - ========== 字幕 #76 处理结束 ==========

