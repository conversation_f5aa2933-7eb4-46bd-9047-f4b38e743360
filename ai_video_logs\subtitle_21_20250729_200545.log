2025-07-29 20:05:45,413 - INFO - ========== 字幕 #21 处理开始 ==========
2025-07-29 20:05:45,413 - INFO - 字幕内容: 几天后，女人才姗姗来迟，看到男人竟想当然地以为他还在生气，甚至不需要任何解释。
2025-07-29 20:05:45,413 - INFO - 字幕序号: [330, 336]
2025-07-29 20:05:45,413 - INFO - 音频文件详情:
2025-07-29 20:05:45,413 - INFO -   - 路径: output\21.wav
2025-07-29 20:05:45,413 - INFO -   - 时长: 4.12秒
2025-07-29 20:05:45,413 - INFO -   - 验证音频时长: 4.12秒
2025-07-29 20:05:45,413 - INFO - 字幕时间戳信息:
2025-07-29 20:05:45,413 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:45,413 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:45,413 - INFO -   - 根据生成的音频时长(4.12秒)已调整字幕时间戳
2025-07-29 20:05:45,413 - INFO - ========== 新模式：为字幕 #21 生成4套场景方案 ==========
2025-07-29 20:05:45,413 - INFO - 字幕序号列表: [330, 336]
2025-07-29 20:05:45,413 - INFO - 
--- 生成方案 #1：基于字幕序号 #330 ---
2025-07-29 20:05:45,413 - INFO - 开始为单个字幕序号 #330 匹配场景，目标时长: 4.12秒
2025-07-29 20:05:45,414 - INFO - 开始查找字幕序号 [330] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:45,414 - INFO - 找到related_overlap场景: scene_id=392, 字幕#330
2025-07-29 20:05:45,414 - INFO - 找到related_between场景: scene_id=387, 字幕#330
2025-07-29 20:05:45,414 - INFO - 找到related_between场景: scene_id=388, 字幕#330
2025-07-29 20:05:45,414 - INFO - 找到related_between场景: scene_id=389, 字幕#330
2025-07-29 20:05:45,414 - INFO - 找到related_between场景: scene_id=390, 字幕#330
2025-07-29 20:05:45,414 - INFO - 找到related_between场景: scene_id=391, 字幕#330
2025-07-29 20:05:45,415 - INFO - 字幕 #330 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:05:45,415 - INFO - 字幕序号 #330 找到 1 个可用overlap场景, 5 个可用between场景
2025-07-29 20:05:45,415 - INFO - 选择第一个overlap场景作为起点: scene_id=392
2025-07-29 20:05:45,415 - INFO - 添加起点场景: scene_id=392, 时长=1.64秒, 累计时长=1.64秒
2025-07-29 20:05:45,415 - INFO - 起点场景时长不足，需要延伸填充 2.48秒
2025-07-29 20:05:45,415 - INFO - 起点场景在原始列表中的索引: 391
2025-07-29 20:05:45,415 - INFO - 延伸添加场景: scene_id=393 (裁剪至 2.48秒)
2025-07-29 20:05:45,415 - INFO - 累计时长: 4.12秒
2025-07-29 20:05:45,415 - INFO - 字幕序号 #330 场景匹配完成，共选择 2 个场景，总时长: 4.12秒
2025-07-29 20:05:45,415 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:05:45,415 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:05:45,415 - INFO - 
--- 生成方案 #2：基于字幕序号 #336 ---
2025-07-29 20:05:45,415 - INFO - 开始为单个字幕序号 #336 匹配场景，目标时长: 4.12秒
2025-07-29 20:05:45,415 - INFO - 开始查找字幕序号 [336] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:45,415 - INFO - 找到related_overlap场景: scene_id=396, 字幕#336
2025-07-29 20:05:45,416 - INFO - 找到related_between场景: scene_id=397, 字幕#336
2025-07-29 20:05:45,416 - INFO - 找到related_between场景: scene_id=398, 字幕#336
2025-07-29 20:05:45,417 - INFO - 字幕 #336 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:45,417 - INFO - 字幕序号 #336 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:05:45,417 - INFO - 选择第一个overlap场景作为起点: scene_id=396
2025-07-29 20:05:45,417 - INFO - 添加起点场景: scene_id=396, 时长=1.20秒, 累计时长=1.20秒
2025-07-29 20:05:45,417 - INFO - 起点场景时长不足，需要延伸填充 2.92秒
2025-07-29 20:05:45,417 - INFO - 起点场景在原始列表中的索引: 395
2025-07-29 20:05:45,417 - INFO - 延伸添加场景: scene_id=397 (完整时长 2.08秒)
2025-07-29 20:05:45,417 - INFO - 累计时长: 3.28秒
2025-07-29 20:05:45,417 - INFO - 延伸添加场景: scene_id=398 (裁剪至 0.84秒)
2025-07-29 20:05:45,417 - INFO - 累计时长: 4.12秒
2025-07-29 20:05:45,417 - INFO - 字幕序号 #336 场景匹配完成，共选择 3 个场景，总时长: 4.12秒
2025-07-29 20:05:45,417 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:45,417 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:45,417 - INFO - ========== 当前模式：为字幕 #21 生成 1 套场景方案 ==========
2025-07-29 20:05:45,417 - INFO - 开始查找字幕序号 [330, 336] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:45,417 - INFO - 找到related_overlap场景: scene_id=392, 字幕#330
2025-07-29 20:05:45,417 - INFO - 找到related_overlap场景: scene_id=396, 字幕#336
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=387, 字幕#330
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=388, 字幕#330
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=389, 字幕#330
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=390, 字幕#330
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=391, 字幕#330
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=397, 字幕#336
2025-07-29 20:05:45,418 - INFO - 找到related_between场景: scene_id=398, 字幕#336
2025-07-29 20:05:45,418 - INFO - 字幕 #330 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:05:45,418 - INFO - 字幕 #336 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:45,418 - INFO - 共收集 2 个未使用的overlap场景和 7 个未使用的between场景
2025-07-29 20:05:45,418 - INFO - 开始生成方案 #1
2025-07-29 20:05:45,418 - INFO - 方案 #1: 为字幕#330选择初始化overlap场景id=392
2025-07-29 20:05:45,418 - INFO - 方案 #1: 为字幕#336选择初始化overlap场景id=396
2025-07-29 20:05:45,418 - INFO - 方案 #1: 初始选择后，当前总时长=2.84秒
2025-07-29 20:05:45,418 - INFO - 方案 #1: 额外between选择后，当前总时长=2.84秒
2025-07-29 20:05:45,418 - INFO - 方案 #1: 额外添加between场景id=390, 当前总时长=4.08秒
2025-07-29 20:05:45,418 - INFO - 方案 #1: 额外添加between场景id=389, 当前总时长=5.56秒
2025-07-29 20:05:45,418 - INFO - 方案 #1: 场景总时长(5.56秒)大于音频时长(4.12秒)，需要裁剪
2025-07-29 20:05:45,418 - INFO - 调整前总时长: 5.56秒, 目标时长: 4.12秒
2025-07-29 20:05:45,418 - INFO - 需要裁剪 1.44秒
2025-07-29 20:05:45,418 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:05:45,418 - INFO - 裁剪场景ID=392：从1.64秒裁剪至1.00秒
2025-07-29 20:05:45,418 - INFO - 裁剪场景ID=389：从1.48秒裁剪至1.00秒
2025-07-29 20:05:45,418 - INFO - 裁剪场景ID=390：从1.24秒裁剪至1.00秒
2025-07-29 20:05:45,418 - INFO - 裁剪场景ID=396：从1.20秒裁剪至1.12秒
2025-07-29 20:05:45,418 - INFO - 调整后总时长: 4.12秒，与目标时长差异: 0.00秒
2025-07-29 20:05:45,418 - INFO - 方案 #1 调整/填充后最终总时长: 4.12秒
2025-07-29 20:05:45,418 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:45,418 - INFO - ========== 当前模式：字幕 #21 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:45,418 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:45,418 - INFO - ========== 新模式：字幕 #21 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:45,418 - INFO - 
----- 处理字幕 #21 的方案 #1 -----
2025-07-29 20:05:45,419 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 20:05:45,419 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpruqs6o3c
2025-07-29 20:05:45,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\392.mp4 (确认存在: True)
2025-07-29 20:05:45,419 - INFO - 添加场景ID=392，时长=1.64秒，累计时长=1.64秒
2025-07-29 20:05:45,420 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\393.mp4 (确认存在: True)
2025-07-29 20:05:45,420 - INFO - 添加场景ID=393，时长=2.64秒，累计时长=4.28秒
2025-07-29 20:05:45,420 - INFO - 准备合并 2 个场景文件，总时长约 4.28秒
2025-07-29 20:05:45,420 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/392.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/393.mp4'

2025-07-29 20:05:45,420 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpruqs6o3c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpruqs6o3c\temp_combined.mp4
2025-07-29 20:05:45,546 - INFO - 合并后的视频时长: 4.33秒，目标音频时长: 4.12秒
2025-07-29 20:05:45,546 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpruqs6o3c\temp_combined.mp4 -ss 0 -to 4.124 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 20:05:45,835 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:45,836 - INFO - 目标音频时长: 4.12秒
2025-07-29 20:05:45,836 - INFO - 实际视频时长: 4.18秒
2025-07-29 20:05:45,836 - INFO - 时长差异: 0.06秒 (1.43%)
2025-07-29 20:05:45,836 - INFO - ==========================================
2025-07-29 20:05:45,836 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:45,836 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 20:05:45,836 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpruqs6o3c
2025-07-29 20:05:45,890 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:45,890 - INFO -   - 音频时长: 4.12秒
2025-07-29 20:05:45,890 - INFO -   - 视频时长: 4.18秒
2025-07-29 20:05:45,890 - INFO -   - 时长差异: 0.06秒 (1.43%)
2025-07-29 20:05:45,890 - INFO - 
----- 处理字幕 #21 的方案 #2 -----
2025-07-29 20:05:45,890 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 20:05:45,891 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmel2w5eg
2025-07-29 20:05:45,891 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\396.mp4 (确认存在: True)
2025-07-29 20:05:45,891 - INFO - 添加场景ID=396，时长=1.20秒，累计时长=1.20秒
2025-07-29 20:05:45,891 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\397.mp4 (确认存在: True)
2025-07-29 20:05:45,891 - INFO - 添加场景ID=397，时长=2.08秒，累计时长=3.28秒
2025-07-29 20:05:45,891 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\398.mp4 (确认存在: True)
2025-07-29 20:05:45,891 - INFO - 添加场景ID=398，时长=2.08秒，累计时长=5.36秒
2025-07-29 20:05:45,892 - INFO - 准备合并 3 个场景文件，总时长约 5.36秒
2025-07-29 20:05:45,892 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/396.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/397.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/398.mp4'

2025-07-29 20:05:45,892 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmel2w5eg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmel2w5eg\temp_combined.mp4
2025-07-29 20:05:46,053 - INFO - 合并后的视频时长: 5.43秒，目标音频时长: 4.12秒
2025-07-29 20:05:46,053 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmel2w5eg\temp_combined.mp4 -ss 0 -to 4.124 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 20:05:46,357 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:46,357 - INFO - 目标音频时长: 4.12秒
2025-07-29 20:05:46,357 - INFO - 实际视频时长: 4.18秒
2025-07-29 20:05:46,357 - INFO - 时长差异: 0.06秒 (1.43%)
2025-07-29 20:05:46,357 - INFO - ==========================================
2025-07-29 20:05:46,357 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:46,357 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 20:05:46,358 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmel2w5eg
2025-07-29 20:05:46,403 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:46,403 - INFO -   - 音频时长: 4.12秒
2025-07-29 20:05:46,403 - INFO -   - 视频时长: 4.18秒
2025-07-29 20:05:46,403 - INFO -   - 时长差异: 0.06秒 (1.43%)
2025-07-29 20:05:46,403 - INFO - 
----- 处理字幕 #21 的方案 #3 -----
2025-07-29 20:05:46,403 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-07-29 20:05:46,403 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxq_r4byd
2025-07-29 20:05:46,404 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\392.mp4 (确认存在: True)
2025-07-29 20:05:46,404 - INFO - 添加场景ID=392，时长=1.64秒，累计时长=1.64秒
2025-07-29 20:05:46,404 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\396.mp4 (确认存在: True)
2025-07-29 20:05:46,404 - INFO - 添加场景ID=396，时长=1.20秒，累计时长=2.84秒
2025-07-29 20:05:46,404 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\390.mp4 (确认存在: True)
2025-07-29 20:05:46,404 - INFO - 添加场景ID=390，时长=1.24秒，累计时长=4.08秒
2025-07-29 20:05:46,404 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\389.mp4 (确认存在: True)
2025-07-29 20:05:46,404 - INFO - 添加场景ID=389，时长=1.48秒，累计时长=5.56秒
2025-07-29 20:05:46,404 - INFO - 准备合并 4 个场景文件，总时长约 5.56秒
2025-07-29 20:05:46,404 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/392.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/396.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/390.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/389.mp4'

2025-07-29 20:05:46,404 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxq_r4byd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxq_r4byd\temp_combined.mp4
2025-07-29 20:05:46,573 - INFO - 合并后的视频时长: 5.63秒，目标音频时长: 4.12秒
2025-07-29 20:05:46,573 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxq_r4byd\temp_combined.mp4 -ss 0 -to 4.124 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-07-29 20:05:46,864 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:46,864 - INFO - 目标音频时长: 4.12秒
2025-07-29 20:05:46,864 - INFO - 实际视频时长: 4.18秒
2025-07-29 20:05:46,864 - INFO - 时长差异: 0.06秒 (1.43%)
2025-07-29 20:05:46,864 - INFO - ==========================================
2025-07-29 20:05:46,864 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:46,864 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-07-29 20:05:46,865 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxq_r4byd
2025-07-29 20:05:46,908 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:46,908 - INFO -   - 音频时长: 4.12秒
2025-07-29 20:05:46,908 - INFO -   - 视频时长: 4.18秒
2025-07-29 20:05:46,908 - INFO -   - 时长差异: 0.06秒 (1.43%)
2025-07-29 20:05:46,908 - INFO - 
字幕 #21 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:46,908 - INFO - 生成的视频文件:
2025-07-29 20:05:46,908 - INFO -   1. F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 20:05:46,908 - INFO -   2. F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 20:05:46,908 - INFO -   3. F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-07-29 20:05:46,908 - INFO - ========== 字幕 #21 处理结束 ==========

