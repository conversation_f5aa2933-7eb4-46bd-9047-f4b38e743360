2025-07-29 20:06:03,713 - INFO - ========== 字幕 #33 处理开始 ==========
2025-07-29 20:06:03,713 - INFO - 字幕内容: 他将工资卡狠狠摔在地上，宣告两人彻底两清，从此再不相见！
2025-07-29 20:06:03,713 - INFO - 字幕序号: [577, 579]
2025-07-29 20:06:03,713 - INFO - 音频文件详情:
2025-07-29 20:06:03,713 - INFO -   - 路径: output\33.wav
2025-07-29 20:06:03,713 - INFO -   - 时长: 4.57秒
2025-07-29 20:06:03,713 - INFO -   - 验证音频时长: 4.57秒
2025-07-29 20:06:03,713 - INFO - 字幕时间戳信息:
2025-07-29 20:06:03,713 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:03,713 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:03,713 - INFO -   - 根据生成的音频时长(4.57秒)已调整字幕时间戳
2025-07-29 20:06:03,714 - INFO - ========== 新模式：为字幕 #33 生成4套场景方案 ==========
2025-07-29 20:06:03,714 - INFO - 字幕序号列表: [577, 579]
2025-07-29 20:06:03,714 - INFO - 
--- 生成方案 #1：基于字幕序号 #577 ---
2025-07-29 20:06:03,714 - INFO - 开始为单个字幕序号 #577 匹配场景，目标时长: 4.57秒
2025-07-29 20:06:03,714 - INFO - 开始查找字幕序号 [577] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:03,714 - INFO - 找到related_overlap场景: scene_id=609, 字幕#577
2025-07-29 20:06:03,714 - INFO - 找到related_between场景: scene_id=605, 字幕#577
2025-07-29 20:06:03,715 - INFO - 找到related_between场景: scene_id=606, 字幕#577
2025-07-29 20:06:03,715 - INFO - 找到related_between场景: scene_id=607, 字幕#577
2025-07-29 20:06:03,715 - INFO - 找到related_between场景: scene_id=608, 字幕#577
2025-07-29 20:06:03,716 - INFO - 字幕 #577 找到 1 个overlap场景, 4 个between场景
2025-07-29 20:06:03,716 - INFO - 字幕序号 #577 找到 1 个可用overlap场景, 4 个可用between场景
2025-07-29 20:06:03,716 - INFO - 选择第一个overlap场景作为起点: scene_id=609
2025-07-29 20:06:03,716 - INFO - 添加起点场景: scene_id=609, 时长=0.92秒, 累计时长=0.92秒
2025-07-29 20:06:03,716 - INFO - 起点场景时长不足，需要延伸填充 3.65秒
2025-07-29 20:06:03,716 - INFO - 起点场景在原始列表中的索引: 608
2025-07-29 20:06:03,716 - INFO - 延伸添加场景: scene_id=610 (完整时长 1.24秒)
2025-07-29 20:06:03,716 - INFO - 累计时长: 2.16秒
2025-07-29 20:06:03,716 - INFO - 延伸添加场景: scene_id=611 (完整时长 1.20秒)
2025-07-29 20:06:03,716 - INFO - 累计时长: 3.36秒
2025-07-29 20:06:03,716 - INFO - 延伸添加场景: scene_id=612 (完整时长 1.16秒)
2025-07-29 20:06:03,716 - INFO - 累计时长: 4.52秒
2025-07-29 20:06:03,716 - INFO - 延伸添加场景: scene_id=613 (裁剪至 0.05秒)
2025-07-29 20:06:03,716 - INFO - 累计时长: 4.57秒
2025-07-29 20:06:03,716 - INFO - 字幕序号 #577 场景匹配完成，共选择 5 个场景，总时长: 4.57秒
2025-07-29 20:06:03,716 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:06:03,716 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:06:03,716 - INFO - 
--- 生成方案 #2：基于字幕序号 #579 ---
2025-07-29 20:06:03,716 - INFO - 开始为单个字幕序号 #579 匹配场景，目标时长: 4.57秒
2025-07-29 20:06:03,716 - INFO - 开始查找字幕序号 [579] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:03,716 - INFO - 找到related_overlap场景: scene_id=611, 字幕#579
2025-07-29 20:06:03,717 - INFO - 找到related_between场景: scene_id=612, 字幕#579
2025-07-29 20:06:03,717 - INFO - 找到related_between场景: scene_id=613, 字幕#579
2025-07-29 20:06:03,717 - INFO - 字幕 #579 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:03,717 - INFO - 字幕序号 #579 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:03,717 - ERROR - 字幕序号 #579 没有找到任何可用的匹配场景
2025-07-29 20:06:03,717 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:03,717 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:03,717 - INFO - ========== 当前模式：为字幕 #33 生成 1 套场景方案 ==========
2025-07-29 20:06:03,717 - INFO - 开始查找字幕序号 [577, 579] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:03,717 - INFO - 找到related_overlap场景: scene_id=609, 字幕#577
2025-07-29 20:06:03,717 - INFO - 找到related_overlap场景: scene_id=611, 字幕#579
2025-07-29 20:06:03,718 - INFO - 找到related_between场景: scene_id=605, 字幕#577
2025-07-29 20:06:03,718 - INFO - 找到related_between场景: scene_id=606, 字幕#577
2025-07-29 20:06:03,718 - INFO - 找到related_between场景: scene_id=607, 字幕#577
2025-07-29 20:06:03,718 - INFO - 找到related_between场景: scene_id=608, 字幕#577
2025-07-29 20:06:03,718 - INFO - 找到related_between场景: scene_id=612, 字幕#579
2025-07-29 20:06:03,718 - INFO - 找到related_between场景: scene_id=613, 字幕#579
2025-07-29 20:06:03,718 - INFO - 字幕 #577 找到 1 个overlap场景, 4 个between场景
2025-07-29 20:06:03,718 - INFO - 字幕 #579 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:03,718 - INFO - 共收集 2 个未使用的overlap场景和 6 个未使用的between场景
2025-07-29 20:06:03,718 - INFO - 开始生成方案 #1
2025-07-29 20:06:03,718 - INFO - 方案 #1: 为字幕#577选择初始化overlap场景id=609
2025-07-29 20:06:03,718 - INFO - 方案 #1: 为字幕#579选择初始化overlap场景id=611
2025-07-29 20:06:03,718 - INFO - 方案 #1: 初始选择后，当前总时长=2.12秒
2025-07-29 20:06:03,718 - INFO - 方案 #1: 额外between选择后，当前总时长=2.12秒
2025-07-29 20:06:03,718 - INFO - 方案 #1: 额外添加between场景id=608, 当前总时长=3.20秒
2025-07-29 20:06:03,718 - INFO - 方案 #1: 额外添加between场景id=605, 当前总时长=4.08秒
2025-07-29 20:06:03,718 - INFO - 方案 #1: 额外添加between场景id=612, 当前总时长=5.24秒
2025-07-29 20:06:03,718 - INFO - 方案 #1: 场景总时长(5.24秒)大于音频时长(4.57秒)，需要裁剪
2025-07-29 20:06:03,718 - INFO - 调整前总时长: 5.24秒, 目标时长: 4.57秒
2025-07-29 20:06:03,718 - INFO - 需要裁剪 0.67秒
2025-07-29 20:06:03,718 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:03,718 - INFO - 裁剪场景ID=611：从1.20秒裁剪至1.00秒
2025-07-29 20:06:03,718 - INFO - 裁剪场景ID=612：从1.16秒裁剪至1.00秒
2025-07-29 20:06:03,718 - INFO - 裁剪场景ID=608：从1.08秒裁剪至1.00秒
2025-07-29 20:06:03,718 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.23秒
2025-07-29 20:06:03,718 - INFO - 移除场景ID=605，时长=0.88秒
2025-07-29 20:06:03,718 - INFO - 调整后总时长: 3.92秒，与目标时长差异: 0.65秒
2025-07-29 20:06:03,718 - INFO - 方案 #1 调整/填充后最终总时长: 3.92秒
2025-07-29 20:06:03,718 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:03,718 - INFO - ========== 当前模式：字幕 #33 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:03,718 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:03,718 - INFO - ========== 新模式：字幕 #33 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:03,718 - INFO - 
----- 处理字幕 #33 的方案 #1 -----
2025-07-29 20:06:03,718 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 20:06:03,719 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0ixe1_4d
2025-07-29 20:06:03,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\609.mp4 (确认存在: True)
2025-07-29 20:06:03,719 - INFO - 添加场景ID=609，时长=0.92秒，累计时长=0.92秒
2025-07-29 20:06:03,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\610.mp4 (确认存在: True)
2025-07-29 20:06:03,719 - INFO - 添加场景ID=610，时长=1.24秒，累计时长=2.16秒
2025-07-29 20:06:03,720 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\611.mp4 (确认存在: True)
2025-07-29 20:06:03,720 - INFO - 添加场景ID=611，时长=1.20秒，累计时长=3.36秒
2025-07-29 20:06:03,720 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\612.mp4 (确认存在: True)
2025-07-29 20:06:03,720 - INFO - 添加场景ID=612，时长=1.16秒，累计时长=4.52秒
2025-07-29 20:06:03,720 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\613.mp4 (确认存在: True)
2025-07-29 20:06:03,720 - INFO - 添加场景ID=613，时长=1.04秒，累计时长=5.56秒
2025-07-29 20:06:03,720 - INFO - 准备合并 5 个场景文件，总时长约 5.56秒
2025-07-29 20:06:03,720 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/609.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/610.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/611.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/612.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/613.mp4'

2025-07-29 20:06:03,720 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0ixe1_4d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0ixe1_4d\temp_combined.mp4
2025-07-29 20:06:03,862 - INFO - 合并后的视频时长: 5.68秒，目标音频时长: 4.57秒
2025-07-29 20:06:03,862 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0ixe1_4d\temp_combined.mp4 -ss 0 -to 4.571 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 20:06:04,163 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:04,163 - INFO - 目标音频时长: 4.57秒
2025-07-29 20:06:04,163 - INFO - 实际视频时长: 4.62秒
2025-07-29 20:06:04,163 - INFO - 时长差异: 0.05秒 (1.14%)
2025-07-29 20:06:04,164 - INFO - ==========================================
2025-07-29 20:06:04,164 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:04,164 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 20:06:04,164 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0ixe1_4d
2025-07-29 20:06:04,208 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:04,208 - INFO -   - 音频时长: 4.57秒
2025-07-29 20:06:04,208 - INFO -   - 视频时长: 4.62秒
2025-07-29 20:06:04,208 - INFO -   - 时长差异: 0.05秒 (1.14%)
2025-07-29 20:06:04,208 - INFO - 
----- 处理字幕 #33 的方案 #2 -----
2025-07-29 20:06:04,208 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 20:06:04,209 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprei5hpc1
2025-07-29 20:06:04,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\609.mp4 (确认存在: True)
2025-07-29 20:06:04,209 - INFO - 添加场景ID=609，时长=0.92秒，累计时长=0.92秒
2025-07-29 20:06:04,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\611.mp4 (确认存在: True)
2025-07-29 20:06:04,209 - INFO - 添加场景ID=611，时长=1.20秒，累计时长=2.12秒
2025-07-29 20:06:04,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\608.mp4 (确认存在: True)
2025-07-29 20:06:04,209 - INFO - 添加场景ID=608，时长=1.08秒，累计时长=3.20秒
2025-07-29 20:06:04,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\612.mp4 (确认存在: True)
2025-07-29 20:06:04,209 - INFO - 添加场景ID=612，时长=1.16秒，累计时长=4.36秒
2025-07-29 20:06:04,209 - INFO - 准备合并 4 个场景文件，总时长约 4.36秒
2025-07-29 20:06:04,210 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/609.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/611.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/608.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/612.mp4'

2025-07-29 20:06:04,210 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprei5hpc1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprei5hpc1\temp_combined.mp4
2025-07-29 20:06:04,344 - INFO - 合并后的视频时长: 4.45秒，目标音频时长: 4.57秒
2025-07-29 20:06:04,344 - ERROR - 合并后的视频时长(4.45秒)小于音频时长(4.57秒)，无法进行精确裁剪
2025-07-29 20:06:04,344 - WARNING - 将直接使用合并后的视频，可能导致音视频不同步
2025-07-29 20:06:04,406 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:04,406 - INFO - 目标音频时长: 4.57秒
2025-07-29 20:06:04,406 - INFO - 实际视频时长: 4.45秒
2025-07-29 20:06:04,406 - INFO - 时长差异: 0.12秒 (2.58%)
2025-07-29 20:06:04,406 - INFO - ==========================================
2025-07-29 20:06:04,406 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:04,406 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 20:06:04,407 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprei5hpc1
2025-07-29 20:06:04,470 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:04,470 - INFO -   - 音频时长: 4.57秒
2025-07-29 20:06:04,470 - INFO -   - 视频时长: 4.45秒
2025-07-29 20:06:04,470 - INFO -   - 时长差异: 0.12秒 (2.58%)
2025-07-29 20:06:04,470 - INFO - 
字幕 #33 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:04,470 - INFO - 生成的视频文件:
2025-07-29 20:06:04,470 - INFO -   1. F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 20:06:04,470 - INFO -   2. F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 20:06:04,470 - INFO - ========== 字幕 #33 处理结束 ==========

