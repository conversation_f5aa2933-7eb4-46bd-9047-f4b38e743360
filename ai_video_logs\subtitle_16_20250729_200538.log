2025-07-29 20:05:38,178 - INFO - ========== 字幕 #16 处理开始 ==========
2025-07-29 20:05:38,178 - INFO - 字幕内容: 男人如遭雷击，瘫倒在地，妹妹在病房里留着最后一口气，只为等他回来。
2025-07-29 20:05:38,178 - INFO - 字幕序号: [117, 122]
2025-07-29 20:05:38,178 - INFO - 音频文件详情:
2025-07-29 20:05:38,178 - INFO -   - 路径: output\16.wav
2025-07-29 20:05:38,178 - INFO -   - 时长: 4.67秒
2025-07-29 20:05:38,180 - INFO -   - 验证音频时长: 4.67秒
2025-07-29 20:05:38,180 - INFO - 字幕时间戳信息:
2025-07-29 20:05:38,180 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:38,180 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:38,180 - INFO -   - 根据生成的音频时长(4.67秒)已调整字幕时间戳
2025-07-29 20:05:38,180 - INFO - ========== 新模式：为字幕 #16 生成4套场景方案 ==========
2025-07-29 20:05:38,180 - INFO - 字幕序号列表: [117, 122]
2025-07-29 20:05:38,180 - INFO - 
--- 生成方案 #1：基于字幕序号 #117 ---
2025-07-29 20:05:38,180 - INFO - 开始为单个字幕序号 #117 匹配场景，目标时长: 4.67秒
2025-07-29 20:05:38,180 - INFO - 开始查找字幕序号 [117] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:38,180 - INFO - 找到related_overlap场景: scene_id=137, 字幕#117
2025-07-29 20:05:38,181 - INFO - 找到related_between场景: scene_id=136, 字幕#117
2025-07-29 20:05:38,182 - INFO - 字幕 #117 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:38,182 - INFO - 字幕序号 #117 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:38,182 - INFO - 选择第一个overlap场景作为起点: scene_id=137
2025-07-29 20:05:38,182 - INFO - 添加起点场景: scene_id=137, 时长=3.40秒, 累计时长=3.40秒
2025-07-29 20:05:38,182 - INFO - 起点场景时长不足，需要延伸填充 1.27秒
2025-07-29 20:05:38,182 - INFO - 起点场景在原始列表中的索引: 136
2025-07-29 20:05:38,182 - INFO - 延伸添加场景: scene_id=138 (完整时长 1.24秒)
2025-07-29 20:05:38,182 - INFO - 累计时长: 4.64秒
2025-07-29 20:05:38,182 - INFO - 延伸添加场景: scene_id=139 (裁剪至 0.03秒)
2025-07-29 20:05:38,182 - INFO - 累计时长: 4.67秒
2025-07-29 20:05:38,182 - INFO - 字幕序号 #117 场景匹配完成，共选择 3 个场景，总时长: 4.67秒
2025-07-29 20:05:38,182 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:38,182 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:38,182 - INFO - 
--- 生成方案 #2：基于字幕序号 #122 ---
2025-07-29 20:05:38,182 - INFO - 开始为单个字幕序号 #122 匹配场景，目标时长: 4.67秒
2025-07-29 20:05:38,182 - INFO - 开始查找字幕序号 [122] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:38,182 - INFO - 找到related_overlap场景: scene_id=146, 字幕#122
2025-07-29 20:05:38,183 - INFO - 找到related_between场景: scene_id=141, 字幕#122
2025-07-29 20:05:38,183 - INFO - 找到related_between场景: scene_id=142, 字幕#122
2025-07-29 20:05:38,183 - INFO - 找到related_between场景: scene_id=143, 字幕#122
2025-07-29 20:05:38,183 - INFO - 找到related_between场景: scene_id=144, 字幕#122
2025-07-29 20:05:38,183 - INFO - 找到related_between场景: scene_id=145, 字幕#122
2025-07-29 20:05:38,184 - INFO - 字幕 #122 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:05:38,184 - INFO - 字幕序号 #122 找到 1 个可用overlap场景, 5 个可用between场景
2025-07-29 20:05:38,184 - INFO - 选择第一个overlap场景作为起点: scene_id=146
2025-07-29 20:05:38,184 - INFO - 添加起点场景: scene_id=146, 时长=2.00秒, 累计时长=2.00秒
2025-07-29 20:05:38,184 - INFO - 起点场景时长不足，需要延伸填充 2.67秒
2025-07-29 20:05:38,184 - INFO - 起点场景在原始列表中的索引: 145
2025-07-29 20:05:38,184 - INFO - 延伸添加场景: scene_id=147 (完整时长 1.40秒)
2025-07-29 20:05:38,184 - INFO - 累计时长: 3.40秒
2025-07-29 20:05:38,184 - INFO - 延伸添加场景: scene_id=148 (裁剪至 1.27秒)
2025-07-29 20:05:38,184 - INFO - 累计时长: 4.67秒
2025-07-29 20:05:38,184 - INFO - 字幕序号 #122 场景匹配完成，共选择 3 个场景，总时长: 4.67秒
2025-07-29 20:05:38,184 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:38,184 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:38,184 - INFO - ========== 当前模式：为字幕 #16 生成 1 套场景方案 ==========
2025-07-29 20:05:38,184 - INFO - 开始查找字幕序号 [117, 122] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:38,184 - INFO - 找到related_overlap场景: scene_id=137, 字幕#117
2025-07-29 20:05:38,184 - INFO - 找到related_overlap场景: scene_id=146, 字幕#122
2025-07-29 20:05:38,185 - INFO - 找到related_between场景: scene_id=136, 字幕#117
2025-07-29 20:05:38,185 - INFO - 找到related_between场景: scene_id=141, 字幕#122
2025-07-29 20:05:38,185 - INFO - 找到related_between场景: scene_id=142, 字幕#122
2025-07-29 20:05:38,185 - INFO - 找到related_between场景: scene_id=143, 字幕#122
2025-07-29 20:05:38,185 - INFO - 找到related_between场景: scene_id=144, 字幕#122
2025-07-29 20:05:38,185 - INFO - 找到related_between场景: scene_id=145, 字幕#122
2025-07-29 20:05:38,185 - INFO - 字幕 #117 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:38,185 - INFO - 字幕 #122 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:05:38,185 - INFO - 共收集 2 个未使用的overlap场景和 6 个未使用的between场景
2025-07-29 20:05:38,185 - INFO - 开始生成方案 #1
2025-07-29 20:05:38,185 - INFO - 方案 #1: 为字幕#117选择初始化overlap场景id=137
2025-07-29 20:05:38,185 - INFO - 方案 #1: 为字幕#122选择初始化overlap场景id=146
2025-07-29 20:05:38,185 - INFO - 方案 #1: 初始选择后，当前总时长=5.40秒
2025-07-29 20:05:38,185 - INFO - 方案 #1: 额外between选择后，当前总时长=5.40秒
2025-07-29 20:05:38,185 - INFO - 方案 #1: 场景总时长(5.40秒)大于音频时长(4.67秒)，需要裁剪
2025-07-29 20:05:38,185 - INFO - 调整前总时长: 5.40秒, 目标时长: 4.67秒
2025-07-29 20:05:38,185 - INFO - 需要裁剪 0.73秒
2025-07-29 20:05:38,185 - INFO - 裁剪最长场景ID=137：从3.40秒裁剪至2.67秒
2025-07-29 20:05:38,185 - INFO - 调整后总时长: 4.67秒，与目标时长差异: 0.00秒
2025-07-29 20:05:38,185 - INFO - 方案 #1 调整/填充后最终总时长: 4.67秒
2025-07-29 20:05:38,185 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:38,185 - INFO - ========== 当前模式：字幕 #16 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:38,185 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:38,185 - INFO - ========== 新模式：字幕 #16 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:38,185 - INFO - 
----- 处理字幕 #16 的方案 #1 -----
2025-07-29 20:05:38,185 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 20:05:38,186 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0tyyhiaz
2025-07-29 20:05:38,186 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\137.mp4 (确认存在: True)
2025-07-29 20:05:38,186 - INFO - 添加场景ID=137，时长=3.40秒，累计时长=3.40秒
2025-07-29 20:05:38,186 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\138.mp4 (确认存在: True)
2025-07-29 20:05:38,187 - INFO - 添加场景ID=138，时长=1.24秒，累计时长=4.64秒
2025-07-29 20:05:38,187 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\139.mp4 (确认存在: True)
2025-07-29 20:05:38,187 - INFO - 添加场景ID=139，时长=1.08秒，累计时长=5.72秒
2025-07-29 20:05:38,187 - INFO - 准备合并 3 个场景文件，总时长约 5.72秒
2025-07-29 20:05:38,187 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/137.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/138.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/139.mp4'

2025-07-29 20:05:38,187 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0tyyhiaz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0tyyhiaz\temp_combined.mp4
2025-07-29 20:05:38,306 - INFO - 合并后的视频时长: 5.79秒，目标音频时长: 4.67秒
2025-07-29 20:05:38,306 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0tyyhiaz\temp_combined.mp4 -ss 0 -to 4.668 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 20:05:38,604 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:38,604 - INFO - 目标音频时长: 4.67秒
2025-07-29 20:05:38,604 - INFO - 实际视频时长: 4.70秒
2025-07-29 20:05:38,604 - INFO - 时长差异: 0.04秒 (0.75%)
2025-07-29 20:05:38,604 - INFO - ==========================================
2025-07-29 20:05:38,604 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:38,604 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 20:05:38,605 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0tyyhiaz
2025-07-29 20:05:38,649 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:38,649 - INFO -   - 音频时长: 4.67秒
2025-07-29 20:05:38,649 - INFO -   - 视频时长: 4.70秒
2025-07-29 20:05:38,649 - INFO -   - 时长差异: 0.04秒 (0.75%)
2025-07-29 20:05:38,649 - INFO - 
----- 处理字幕 #16 的方案 #2 -----
2025-07-29 20:05:38,649 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 20:05:38,649 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5tjfgi4z
2025-07-29 20:05:38,650 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\146.mp4 (确认存在: True)
2025-07-29 20:05:38,650 - INFO - 添加场景ID=146，时长=2.00秒，累计时长=2.00秒
2025-07-29 20:05:38,650 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\147.mp4 (确认存在: True)
2025-07-29 20:05:38,650 - INFO - 添加场景ID=147，时长=1.40秒，累计时长=3.40秒
2025-07-29 20:05:38,650 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\148.mp4 (确认存在: True)
2025-07-29 20:05:38,650 - INFO - 添加场景ID=148，时长=4.72秒，累计时长=8.12秒
2025-07-29 20:05:38,650 - INFO - 场景总时长(8.12秒)已达到音频时长(4.67秒)的1.5倍，停止添加场景
2025-07-29 20:05:38,650 - INFO - 准备合并 3 个场景文件，总时长约 8.12秒
2025-07-29 20:05:38,650 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/146.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/147.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/148.mp4'

2025-07-29 20:05:38,650 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5tjfgi4z\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5tjfgi4z\temp_combined.mp4
2025-07-29 20:05:38,809 - INFO - 合并后的视频时长: 8.19秒，目标音频时长: 4.67秒
2025-07-29 20:05:38,810 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5tjfgi4z\temp_combined.mp4 -ss 0 -to 4.668 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 20:05:39,130 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:39,130 - INFO - 目标音频时长: 4.67秒
2025-07-29 20:05:39,130 - INFO - 实际视频时长: 4.70秒
2025-07-29 20:05:39,130 - INFO - 时长差异: 0.04秒 (0.75%)
2025-07-29 20:05:39,130 - INFO - ==========================================
2025-07-29 20:05:39,130 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:39,130 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 20:05:39,131 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5tjfgi4z
2025-07-29 20:05:39,174 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:39,174 - INFO -   - 音频时长: 4.67秒
2025-07-29 20:05:39,174 - INFO -   - 视频时长: 4.70秒
2025-07-29 20:05:39,174 - INFO -   - 时长差异: 0.04秒 (0.75%)
2025-07-29 20:05:39,174 - INFO - 
----- 处理字幕 #16 的方案 #3 -----
2025-07-29 20:05:39,174 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 20:05:39,174 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgjorob5g
2025-07-29 20:05:39,175 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\137.mp4 (确认存在: True)
2025-07-29 20:05:39,175 - INFO - 添加场景ID=137，时长=3.40秒，累计时长=3.40秒
2025-07-29 20:05:39,175 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\146.mp4 (确认存在: True)
2025-07-29 20:05:39,175 - INFO - 添加场景ID=146，时长=2.00秒，累计时长=5.40秒
2025-07-29 20:05:39,175 - INFO - 准备合并 2 个场景文件，总时长约 5.40秒
2025-07-29 20:05:39,175 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/137.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/146.mp4'

2025-07-29 20:05:39,175 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgjorob5g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgjorob5g\temp_combined.mp4
2025-07-29 20:05:39,283 - INFO - 合并后的视频时长: 5.45秒，目标音频时长: 4.67秒
2025-07-29 20:05:39,283 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgjorob5g\temp_combined.mp4 -ss 0 -to 4.668 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 20:05:39,575 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:39,575 - INFO - 目标音频时长: 4.67秒
2025-07-29 20:05:39,575 - INFO - 实际视频时长: 4.70秒
2025-07-29 20:05:39,575 - INFO - 时长差异: 0.04秒 (0.75%)
2025-07-29 20:05:39,575 - INFO - ==========================================
2025-07-29 20:05:39,575 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:39,575 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 20:05:39,575 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgjorob5g
2025-07-29 20:05:39,618 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:39,618 - INFO -   - 音频时长: 4.67秒
2025-07-29 20:05:39,618 - INFO -   - 视频时长: 4.70秒
2025-07-29 20:05:39,618 - INFO -   - 时长差异: 0.04秒 (0.75%)
2025-07-29 20:05:39,618 - INFO - 
字幕 #16 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:39,618 - INFO - 生成的视频文件:
2025-07-29 20:05:39,618 - INFO -   1. F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 20:05:39,618 - INFO -   2. F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 20:05:39,618 - INFO -   3. F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 20:05:39,618 - INFO - ========== 字幕 #16 处理结束 ==========

