2025-07-29 20:05:57,094 - INFO - ========== 字幕 #29 处理开始 ==========
2025-07-29 20:05:57,094 - INFO - 字幕内容: 他一字一句地清算，妹妹的治疗费三十万，加上欠条的两万，再减去他上交的百万工资，女人还欠他六十八万！
2025-07-29 20:05:57,094 - INFO - 字幕序号: [510, 518]
2025-07-29 20:05:57,094 - INFO - 音频文件详情:
2025-07-29 20:05:57,094 - INFO -   - 路径: output\29.wav
2025-07-29 20:05:57,094 - INFO -   - 时长: 7.82秒
2025-07-29 20:05:57,094 - INFO -   - 验证音频时长: 7.82秒
2025-07-29 20:05:57,105 - INFO - 字幕时间戳信息:
2025-07-29 20:05:57,105 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:57,105 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:57,105 - INFO -   - 根据生成的音频时长(7.82秒)已调整字幕时间戳
2025-07-29 20:05:57,105 - INFO - ========== 新模式：为字幕 #29 生成4套场景方案 ==========
2025-07-29 20:05:57,105 - INFO - 字幕序号列表: [510, 518]
2025-07-29 20:05:57,105 - INFO - 
--- 生成方案 #1：基于字幕序号 #510 ---
2025-07-29 20:05:57,105 - INFO - 开始为单个字幕序号 #510 匹配场景，目标时长: 7.82秒
2025-07-29 20:05:57,105 - INFO - 开始查找字幕序号 [510] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:57,105 - INFO - 找到related_overlap场景: scene_id=537, 字幕#510
2025-07-29 20:05:57,107 - INFO - 字幕 #510 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:57,107 - INFO - 字幕序号 #510 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:57,107 - INFO - 选择第一个overlap场景作为起点: scene_id=537
2025-07-29 20:05:57,107 - INFO - 添加起点场景: scene_id=537, 时长=2.76秒, 累计时长=2.76秒
2025-07-29 20:05:57,107 - INFO - 起点场景时长不足，需要延伸填充 5.06秒
2025-07-29 20:05:57,107 - INFO - 起点场景在原始列表中的索引: 536
2025-07-29 20:05:57,107 - INFO - 延伸添加场景: scene_id=538 (完整时长 1.76秒)
2025-07-29 20:05:57,107 - INFO - 累计时长: 4.52秒
2025-07-29 20:05:57,107 - INFO - 延伸添加场景: scene_id=539 (裁剪至 3.31秒)
2025-07-29 20:05:57,107 - INFO - 累计时长: 7.82秒
2025-07-29 20:05:57,107 - INFO - 字幕序号 #510 场景匹配完成，共选择 3 个场景，总时长: 7.82秒
2025-07-29 20:05:57,107 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:57,107 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:57,107 - INFO - 
--- 生成方案 #2：基于字幕序号 #518 ---
2025-07-29 20:05:57,107 - INFO - 开始为单个字幕序号 #518 匹配场景，目标时长: 7.82秒
2025-07-29 20:05:57,107 - INFO - 开始查找字幕序号 [518] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:57,107 - INFO - 找到related_overlap场景: scene_id=540, 字幕#518
2025-07-29 20:05:57,108 - INFO - 字幕 #518 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:57,108 - INFO - 字幕序号 #518 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:57,108 - INFO - 选择第一个overlap场景作为起点: scene_id=540
2025-07-29 20:05:57,108 - INFO - 添加起点场景: scene_id=540, 时长=4.40秒, 累计时长=4.40秒
2025-07-29 20:05:57,108 - INFO - 起点场景时长不足，需要延伸填充 3.42秒
2025-07-29 20:05:57,108 - INFO - 起点场景在原始列表中的索引: 539
2025-07-29 20:05:57,108 - INFO - 延伸添加场景: scene_id=541 (完整时长 0.84秒)
2025-07-29 20:05:57,108 - INFO - 累计时长: 5.24秒
2025-07-29 20:05:57,108 - INFO - 延伸添加场景: scene_id=542 (完整时长 1.60秒)
2025-07-29 20:05:57,108 - INFO - 累计时长: 6.84秒
2025-07-29 20:05:57,108 - INFO - 延伸添加场景: scene_id=543 (裁剪至 0.99秒)
2025-07-29 20:05:57,108 - INFO - 累计时长: 7.82秒
2025-07-29 20:05:57,108 - INFO - 字幕序号 #518 场景匹配完成，共选择 4 个场景，总时长: 7.82秒
2025-07-29 20:05:57,108 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:57,108 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:57,108 - INFO - ========== 当前模式：为字幕 #29 生成 1 套场景方案 ==========
2025-07-29 20:05:57,108 - INFO - 开始查找字幕序号 [510, 518] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:57,108 - INFO - 找到related_overlap场景: scene_id=537, 字幕#510
2025-07-29 20:05:57,108 - INFO - 找到related_overlap场景: scene_id=540, 字幕#518
2025-07-29 20:05:57,109 - INFO - 字幕 #510 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:57,109 - INFO - 字幕 #518 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:57,109 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:57,109 - INFO - 开始生成方案 #1
2025-07-29 20:05:57,109 - INFO - 方案 #1: 为字幕#510选择初始化overlap场景id=537
2025-07-29 20:05:57,109 - INFO - 方案 #1: 为字幕#518选择初始化overlap场景id=540
2025-07-29 20:05:57,109 - INFO - 方案 #1: 初始选择后，当前总时长=7.16秒
2025-07-29 20:05:57,109 - INFO - 方案 #1: 额外between选择后，当前总时长=7.16秒
2025-07-29 20:05:57,109 - INFO - 方案 #1: 场景总时长(7.16秒)小于音频时长(7.82秒)，需要延伸填充
2025-07-29 20:05:57,109 - INFO - 方案 #1: 最后一个场景ID: 540
2025-07-29 20:05:57,109 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 539
2025-07-29 20:05:57,109 - INFO - 方案 #1: 需要填充时长: 0.67秒
2025-07-29 20:05:57,109 - INFO - 方案 #1: 追加场景 scene_id=541 (裁剪至 0.67秒)
2025-07-29 20:05:57,109 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:57,109 - INFO - 方案 #1 调整/填充后最终总时长: 7.82秒
2025-07-29 20:05:57,109 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:57,109 - INFO - ========== 当前模式：字幕 #29 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:57,109 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:57,109 - INFO - ========== 新模式：字幕 #29 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:57,109 - INFO - 
----- 处理字幕 #29 的方案 #1 -----
2025-07-29 20:05:57,109 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 20:05:57,110 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpilocfx0b
2025-07-29 20:05:57,110 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\537.mp4 (确认存在: True)
2025-07-29 20:05:57,110 - INFO - 添加场景ID=537，时长=2.76秒，累计时长=2.76秒
2025-07-29 20:05:57,110 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\538.mp4 (确认存在: True)
2025-07-29 20:05:57,110 - INFO - 添加场景ID=538，时长=1.76秒，累计时长=4.52秒
2025-07-29 20:05:57,110 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\539.mp4 (确认存在: True)
2025-07-29 20:05:57,110 - INFO - 添加场景ID=539，时长=3.92秒，累计时长=8.44秒
2025-07-29 20:05:57,110 - INFO - 准备合并 3 个场景文件，总时长约 8.44秒
2025-07-29 20:05:57,110 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/537.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/538.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/539.mp4'

2025-07-29 20:05:57,110 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpilocfx0b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpilocfx0b\temp_combined.mp4
2025-07-29 20:05:57,239 - INFO - 合并后的视频时长: 8.51秒，目标音频时长: 7.82秒
2025-07-29 20:05:57,239 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpilocfx0b\temp_combined.mp4 -ss 0 -to 7.823 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 20:05:57,629 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:57,629 - INFO - 目标音频时长: 7.82秒
2025-07-29 20:05:57,629 - INFO - 实际视频时长: 7.86秒
2025-07-29 20:05:57,630 - INFO - 时长差异: 0.04秒 (0.51%)
2025-07-29 20:05:57,630 - INFO - ==========================================
2025-07-29 20:05:57,630 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:57,630 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 20:05:57,630 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpilocfx0b
2025-07-29 20:05:57,674 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:57,674 - INFO -   - 音频时长: 7.82秒
2025-07-29 20:05:57,674 - INFO -   - 视频时长: 7.86秒
2025-07-29 20:05:57,674 - INFO -   - 时长差异: 0.04秒 (0.51%)
2025-07-29 20:05:57,675 - INFO - 
----- 处理字幕 #29 的方案 #2 -----
2025-07-29 20:05:57,675 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-29 20:05:57,675 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq7fztm9l
2025-07-29 20:05:57,675 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\540.mp4 (确认存在: True)
2025-07-29 20:05:57,675 - INFO - 添加场景ID=540，时长=4.40秒，累计时长=4.40秒
2025-07-29 20:05:57,675 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\541.mp4 (确认存在: True)
2025-07-29 20:05:57,675 - INFO - 添加场景ID=541，时长=0.84秒，累计时长=5.24秒
2025-07-29 20:05:57,675 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\542.mp4 (确认存在: True)
2025-07-29 20:05:57,675 - INFO - 添加场景ID=542，时长=1.60秒，累计时长=6.84秒
2025-07-29 20:05:57,675 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\543.mp4 (确认存在: True)
2025-07-29 20:05:57,675 - INFO - 添加场景ID=543，时长=3.72秒，累计时长=10.56秒
2025-07-29 20:05:57,675 - INFO - 准备合并 4 个场景文件，总时长约 10.56秒
2025-07-29 20:05:57,675 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/540.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/541.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/542.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/543.mp4'

2025-07-29 20:05:57,676 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq7fztm9l\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq7fztm9l\temp_combined.mp4
2025-07-29 20:05:57,815 - INFO - 合并后的视频时长: 10.65秒，目标音频时长: 7.82秒
2025-07-29 20:05:57,815 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq7fztm9l\temp_combined.mp4 -ss 0 -to 7.823 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-29 20:05:58,204 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:58,204 - INFO - 目标音频时长: 7.82秒
2025-07-29 20:05:58,204 - INFO - 实际视频时长: 7.86秒
2025-07-29 20:05:58,204 - INFO - 时长差异: 0.04秒 (0.51%)
2025-07-29 20:05:58,204 - INFO - ==========================================
2025-07-29 20:05:58,204 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:58,204 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-29 20:05:58,205 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq7fztm9l
2025-07-29 20:05:58,251 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:58,251 - INFO -   - 音频时长: 7.82秒
2025-07-29 20:05:58,251 - INFO -   - 视频时长: 7.86秒
2025-07-29 20:05:58,251 - INFO -   - 时长差异: 0.04秒 (0.51%)
2025-07-29 20:05:58,251 - INFO - 
----- 处理字幕 #29 的方案 #3 -----
2025-07-29 20:05:58,251 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-07-29 20:05:58,252 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphd7ziuyr
2025-07-29 20:05:58,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\537.mp4 (确认存在: True)
2025-07-29 20:05:58,252 - INFO - 添加场景ID=537，时长=2.76秒，累计时长=2.76秒
2025-07-29 20:05:58,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\540.mp4 (确认存在: True)
2025-07-29 20:05:58,252 - INFO - 添加场景ID=540，时长=4.40秒，累计时长=7.16秒
2025-07-29 20:05:58,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\541.mp4 (确认存在: True)
2025-07-29 20:05:58,252 - INFO - 添加场景ID=541，时长=0.84秒，累计时长=8.00秒
2025-07-29 20:05:58,252 - INFO - 准备合并 3 个场景文件，总时长约 8.00秒
2025-07-29 20:05:58,253 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/537.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/540.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/541.mp4'

2025-07-29 20:05:58,253 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphd7ziuyr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphd7ziuyr\temp_combined.mp4
2025-07-29 20:05:58,382 - INFO - 合并后的视频时长: 8.07秒，目标音频时长: 7.82秒
2025-07-29 20:05:58,382 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphd7ziuyr\temp_combined.mp4 -ss 0 -to 7.823 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-07-29 20:05:58,771 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:58,771 - INFO - 目标音频时长: 7.82秒
2025-07-29 20:05:58,771 - INFO - 实际视频时长: 7.86秒
2025-07-29 20:05:58,771 - INFO - 时长差异: 0.04秒 (0.51%)
2025-07-29 20:05:58,771 - INFO - ==========================================
2025-07-29 20:05:58,771 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:58,771 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-07-29 20:05:58,772 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphd7ziuyr
2025-07-29 20:05:58,817 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:58,817 - INFO -   - 音频时长: 7.82秒
2025-07-29 20:05:58,817 - INFO -   - 视频时长: 7.86秒
2025-07-29 20:05:58,817 - INFO -   - 时长差异: 0.04秒 (0.51%)
2025-07-29 20:05:58,817 - INFO - 
字幕 #29 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:58,817 - INFO - 生成的视频文件:
2025-07-29 20:05:58,817 - INFO -   1. F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 20:05:58,817 - INFO -   2. F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-29 20:05:58,817 - INFO -   3. F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-07-29 20:05:58,817 - INFO - ========== 字幕 #29 处理结束 ==========

