2025-07-29 20:05:31,621 - INFO - ========== 字幕 #12 处理开始 ==========
2025-07-29 20:05:31,621 - INFO - 字幕内容: 女人冷酷地拒绝，声称自己没空陪他开这种玩笑，甚至当众撇清关系，说不认识他。
2025-07-29 20:05:31,621 - INFO - 字幕序号: [177, 180]
2025-07-29 20:05:31,621 - INFO - 音频文件详情:
2025-07-29 20:05:31,621 - INFO -   - 路径: output\12.wav
2025-07-29 20:05:31,621 - INFO -   - 时长: 5.55秒
2025-07-29 20:05:31,621 - INFO -   - 验证音频时长: 5.55秒
2025-07-29 20:05:31,621 - INFO - 字幕时间戳信息:
2025-07-29 20:05:31,622 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:31,622 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:31,622 - INFO -   - 根据生成的音频时长(5.55秒)已调整字幕时间戳
2025-07-29 20:05:31,622 - INFO - ========== 新模式：为字幕 #12 生成4套场景方案 ==========
2025-07-29 20:05:31,622 - INFO - 字幕序号列表: [177, 180]
2025-07-29 20:05:31,622 - INFO - 
--- 生成方案 #1：基于字幕序号 #177 ---
2025-07-29 20:05:31,622 - INFO - 开始为单个字幕序号 #177 匹配场景，目标时长: 5.55秒
2025-07-29 20:05:31,622 - INFO - 开始查找字幕序号 [177] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:31,622 - INFO - 找到related_overlap场景: scene_id=196, 字幕#177
2025-07-29 20:05:31,623 - INFO - 找到related_between场景: scene_id=195, 字幕#177
2025-07-29 20:05:31,623 - INFO - 字幕 #177 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:31,623 - INFO - 字幕序号 #177 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:31,623 - INFO - 选择第一个overlap场景作为起点: scene_id=196
2025-07-29 20:05:31,623 - INFO - 添加起点场景: scene_id=196, 时长=1.56秒, 累计时长=1.56秒
2025-07-29 20:05:31,623 - INFO - 起点场景时长不足，需要延伸填充 3.99秒
2025-07-29 20:05:31,623 - INFO - 起点场景在原始列表中的索引: 195
2025-07-29 20:05:31,623 - INFO - 延伸添加场景: scene_id=197 (完整时长 0.84秒)
2025-07-29 20:05:31,623 - INFO - 累计时长: 2.40秒
2025-07-29 20:05:31,623 - INFO - 延伸添加场景: scene_id=198 (完整时长 0.96秒)
2025-07-29 20:05:31,623 - INFO - 累计时长: 3.36秒
2025-07-29 20:05:31,623 - INFO - 延伸添加场景: scene_id=199 (完整时长 1.12秒)
2025-07-29 20:05:31,623 - INFO - 累计时长: 4.48秒
2025-07-29 20:05:31,623 - INFO - 延伸添加场景: scene_id=200 (裁剪至 1.07秒)
2025-07-29 20:05:31,623 - INFO - 累计时长: 5.55秒
2025-07-29 20:05:31,623 - INFO - 字幕序号 #177 场景匹配完成，共选择 5 个场景，总时长: 5.55秒
2025-07-29 20:05:31,623 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:31,623 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:31,623 - INFO - 
--- 生成方案 #2：基于字幕序号 #180 ---
2025-07-29 20:05:31,623 - INFO - 开始为单个字幕序号 #180 匹配场景，目标时长: 5.55秒
2025-07-29 20:05:31,623 - INFO - 开始查找字幕序号 [180] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:31,624 - INFO - 找到related_overlap场景: scene_id=202, 字幕#180
2025-07-29 20:05:31,624 - INFO - 找到related_between场景: scene_id=200, 字幕#180
2025-07-29 20:05:31,624 - INFO - 找到related_between场景: scene_id=201, 字幕#180
2025-07-29 20:05:31,625 - INFO - 字幕 #180 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:31,625 - INFO - 字幕序号 #180 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:31,625 - INFO - 选择第一个overlap场景作为起点: scene_id=202
2025-07-29 20:05:31,625 - INFO - 添加起点场景: scene_id=202, 时长=2.28秒, 累计时长=2.28秒
2025-07-29 20:05:31,625 - INFO - 起点场景时长不足，需要延伸填充 3.27秒
2025-07-29 20:05:31,625 - INFO - 起点场景在原始列表中的索引: 201
2025-07-29 20:05:31,625 - INFO - 延伸添加场景: scene_id=203 (完整时长 2.36秒)
2025-07-29 20:05:31,625 - INFO - 累计时长: 4.64秒
2025-07-29 20:05:31,625 - INFO - 延伸添加场景: scene_id=204 (裁剪至 0.91秒)
2025-07-29 20:05:31,625 - INFO - 累计时长: 5.55秒
2025-07-29 20:05:31,625 - INFO - 字幕序号 #180 场景匹配完成，共选择 3 个场景，总时长: 5.55秒
2025-07-29 20:05:31,625 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:31,625 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:31,625 - INFO - ========== 当前模式：为字幕 #12 生成 1 套场景方案 ==========
2025-07-29 20:05:31,625 - INFO - 开始查找字幕序号 [177, 180] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:31,625 - INFO - 找到related_overlap场景: scene_id=196, 字幕#177
2025-07-29 20:05:31,625 - INFO - 找到related_overlap场景: scene_id=202, 字幕#180
2025-07-29 20:05:31,626 - INFO - 找到related_between场景: scene_id=195, 字幕#177
2025-07-29 20:05:31,626 - INFO - 找到related_between场景: scene_id=200, 字幕#180
2025-07-29 20:05:31,626 - INFO - 找到related_between场景: scene_id=201, 字幕#180
2025-07-29 20:05:31,626 - INFO - 字幕 #177 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:31,626 - INFO - 字幕 #180 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:31,626 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:05:31,626 - INFO - 开始生成方案 #1
2025-07-29 20:05:31,626 - INFO - 方案 #1: 为字幕#177选择初始化overlap场景id=196
2025-07-29 20:05:31,626 - INFO - 方案 #1: 为字幕#180选择初始化overlap场景id=202
2025-07-29 20:05:31,626 - INFO - 方案 #1: 初始选择后，当前总时长=3.84秒
2025-07-29 20:05:31,626 - INFO - 方案 #1: 额外between选择后，当前总时长=3.84秒
2025-07-29 20:05:31,626 - INFO - 方案 #1: 额外添加between场景id=200, 当前总时长=5.36秒
2025-07-29 20:05:31,626 - INFO - 方案 #1: 额外添加between场景id=201, 当前总时长=6.32秒
2025-07-29 20:05:31,626 - INFO - 方案 #1: 场景总时长(6.32秒)大于音频时长(5.55秒)，需要裁剪
2025-07-29 20:05:31,626 - INFO - 调整前总时长: 6.32秒, 目标时长: 5.55秒
2025-07-29 20:05:31,626 - INFO - 需要裁剪 0.77秒
2025-07-29 20:05:31,627 - INFO - 裁剪最长场景ID=202：从2.28秒裁剪至1.51秒
2025-07-29 20:05:31,627 - INFO - 调整后总时长: 5.55秒，与目标时长差异: 0.00秒
2025-07-29 20:05:31,627 - INFO - 方案 #1 调整/填充后最终总时长: 5.55秒
2025-07-29 20:05:31,627 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:31,627 - INFO - ========== 当前模式：字幕 #12 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:31,627 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:31,627 - INFO - ========== 新模式：字幕 #12 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:31,627 - INFO - 
----- 处理字幕 #12 的方案 #1 -----
2025-07-29 20:05:31,627 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 20:05:31,627 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzhbym_hp
2025-07-29 20:05:31,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\196.mp4 (确认存在: True)
2025-07-29 20:05:31,628 - INFO - 添加场景ID=196，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:05:31,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\197.mp4 (确认存在: True)
2025-07-29 20:05:31,628 - INFO - 添加场景ID=197，时长=0.84秒，累计时长=2.40秒
2025-07-29 20:05:31,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\198.mp4 (确认存在: True)
2025-07-29 20:05:31,628 - INFO - 添加场景ID=198，时长=0.96秒，累计时长=3.36秒
2025-07-29 20:05:31,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\199.mp4 (确认存在: True)
2025-07-29 20:05:31,628 - INFO - 添加场景ID=199，时长=1.12秒，累计时长=4.48秒
2025-07-29 20:05:31,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\200.mp4 (确认存在: True)
2025-07-29 20:05:31,628 - INFO - 添加场景ID=200，时长=1.52秒，累计时长=6.00秒
2025-07-29 20:05:31,628 - INFO - 准备合并 5 个场景文件，总时长约 6.00秒
2025-07-29 20:05:31,628 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/196.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/197.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/198.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/199.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/200.mp4'

2025-07-29 20:05:31,628 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzhbym_hp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzhbym_hp\temp_combined.mp4
2025-07-29 20:05:31,791 - INFO - 合并后的视频时长: 6.12秒，目标音频时长: 5.55秒
2025-07-29 20:05:31,791 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzhbym_hp\temp_combined.mp4 -ss 0 -to 5.552 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 20:05:32,137 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:32,137 - INFO - 目标音频时长: 5.55秒
2025-07-29 20:05:32,137 - INFO - 实际视频时长: 5.58秒
2025-07-29 20:05:32,137 - INFO - 时长差异: 0.03秒 (0.56%)
2025-07-29 20:05:32,137 - INFO - ==========================================
2025-07-29 20:05:32,137 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:32,137 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 20:05:32,138 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzhbym_hp
2025-07-29 20:05:32,182 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:32,182 - INFO -   - 音频时长: 5.55秒
2025-07-29 20:05:32,182 - INFO -   - 视频时长: 5.58秒
2025-07-29 20:05:32,182 - INFO -   - 时长差异: 0.03秒 (0.56%)
2025-07-29 20:05:32,182 - INFO - 
----- 处理字幕 #12 的方案 #2 -----
2025-07-29 20:05:32,182 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 20:05:32,182 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsm0v092b
2025-07-29 20:05:32,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\202.mp4 (确认存在: True)
2025-07-29 20:05:32,183 - INFO - 添加场景ID=202，时长=2.28秒，累计时长=2.28秒
2025-07-29 20:05:32,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\203.mp4 (确认存在: True)
2025-07-29 20:05:32,183 - INFO - 添加场景ID=203，时长=2.36秒，累计时长=4.64秒
2025-07-29 20:05:32,183 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 20:05:32,183 - INFO - 添加场景ID=204，时长=1.36秒，累计时长=6.00秒
2025-07-29 20:05:32,183 - INFO - 准备合并 3 个场景文件，总时长约 6.00秒
2025-07-29 20:05:32,183 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/202.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/203.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'

2025-07-29 20:05:32,183 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsm0v092b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsm0v092b\temp_combined.mp4
2025-07-29 20:05:32,323 - INFO - 合并后的视频时长: 6.07秒，目标音频时长: 5.55秒
2025-07-29 20:05:32,323 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsm0v092b\temp_combined.mp4 -ss 0 -to 5.552 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 20:05:32,649 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:32,649 - INFO - 目标音频时长: 5.55秒
2025-07-29 20:05:32,649 - INFO - 实际视频时长: 5.58秒
2025-07-29 20:05:32,649 - INFO - 时长差异: 0.03秒 (0.56%)
2025-07-29 20:05:32,649 - INFO - ==========================================
2025-07-29 20:05:32,650 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:32,650 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 20:05:32,650 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsm0v092b
2025-07-29 20:05:32,696 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:32,696 - INFO -   - 音频时长: 5.55秒
2025-07-29 20:05:32,696 - INFO -   - 视频时长: 5.58秒
2025-07-29 20:05:32,696 - INFO -   - 时长差异: 0.03秒 (0.56%)
2025-07-29 20:05:32,696 - INFO - 
----- 处理字幕 #12 的方案 #3 -----
2025-07-29 20:05:32,696 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 20:05:32,696 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbaxes5fp
2025-07-29 20:05:32,697 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\196.mp4 (确认存在: True)
2025-07-29 20:05:32,697 - INFO - 添加场景ID=196，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:05:32,697 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\202.mp4 (确认存在: True)
2025-07-29 20:05:32,697 - INFO - 添加场景ID=202，时长=2.28秒，累计时长=3.84秒
2025-07-29 20:05:32,697 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\200.mp4 (确认存在: True)
2025-07-29 20:05:32,697 - INFO - 添加场景ID=200，时长=1.52秒，累计时长=5.36秒
2025-07-29 20:05:32,697 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\201.mp4 (确认存在: True)
2025-07-29 20:05:32,697 - INFO - 添加场景ID=201，时长=0.96秒，累计时长=6.32秒
2025-07-29 20:05:32,697 - INFO - 准备合并 4 个场景文件，总时长约 6.32秒
2025-07-29 20:05:32,697 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/196.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/202.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/200.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/201.mp4'

2025-07-29 20:05:32,697 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbaxes5fp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbaxes5fp\temp_combined.mp4
2025-07-29 20:05:32,834 - INFO - 合并后的视频时长: 6.41秒，目标音频时长: 5.55秒
2025-07-29 20:05:32,834 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbaxes5fp\temp_combined.mp4 -ss 0 -to 5.552 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 20:05:33,157 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:33,157 - INFO - 目标音频时长: 5.55秒
2025-07-29 20:05:33,157 - INFO - 实际视频时长: 5.58秒
2025-07-29 20:05:33,157 - INFO - 时长差异: 0.03秒 (0.56%)
2025-07-29 20:05:33,157 - INFO - ==========================================
2025-07-29 20:05:33,157 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:33,157 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 20:05:33,158 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbaxes5fp
2025-07-29 20:05:33,202 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:33,202 - INFO -   - 音频时长: 5.55秒
2025-07-29 20:05:33,202 - INFO -   - 视频时长: 5.58秒
2025-07-29 20:05:33,202 - INFO -   - 时长差异: 0.03秒 (0.56%)
2025-07-29 20:05:33,202 - INFO - 
字幕 #12 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:33,202 - INFO - 生成的视频文件:
2025-07-29 20:05:33,202 - INFO -   1. F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 20:05:33,202 - INFO -   2. F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 20:05:33,202 - INFO -   3. F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 20:05:33,202 - INFO - ========== 字幕 #12 处理结束 ==========

