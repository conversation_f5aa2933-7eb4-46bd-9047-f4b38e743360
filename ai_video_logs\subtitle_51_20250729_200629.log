2025-07-29 20:06:29,477 - INFO - ========== 字幕 #51 处理开始 ==========
2025-07-29 20:06:29,477 - INFO - 字幕内容: 新欢拿出VIP邀请函炫耀，却被众人质疑是伪造的，因为以许氏的体量，根本没资格获得。
2025-07-29 20:06:29,477 - INFO - 字幕序号: [2058, 2063]
2025-07-29 20:06:29,477 - INFO - 音频文件详情:
2025-07-29 20:06:29,477 - INFO -   - 路径: output\51.wav
2025-07-29 20:06:29,477 - INFO -   - 时长: 4.89秒
2025-07-29 20:06:29,477 - INFO -   - 验证音频时长: 4.89秒
2025-07-29 20:06:29,477 - INFO - 字幕时间戳信息:
2025-07-29 20:06:29,477 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:29,477 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:29,477 - INFO -   - 根据生成的音频时长(4.89秒)已调整字幕时间戳
2025-07-29 20:06:29,477 - INFO - ========== 新模式：为字幕 #51 生成4套场景方案 ==========
2025-07-29 20:06:29,477 - INFO - 字幕序号列表: [2058, 2063]
2025-07-29 20:06:29,477 - INFO - 
--- 生成方案 #1：基于字幕序号 #2058 ---
2025-07-29 20:06:29,477 - INFO - 开始为单个字幕序号 #2058 匹配场景，目标时长: 4.89秒
2025-07-29 20:06:29,477 - INFO - 开始查找字幕序号 [2058] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:29,479 - INFO - 找到related_overlap场景: scene_id=2037, 字幕#2058
2025-07-29 20:06:29,480 - INFO - 字幕 #2058 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:29,480 - INFO - 字幕序号 #2058 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:29,480 - INFO - 选择第一个overlap场景作为起点: scene_id=2037
2025-07-29 20:06:29,480 - INFO - 添加起点场景: scene_id=2037, 时长=0.84秒, 累计时长=0.84秒
2025-07-29 20:06:29,480 - INFO - 起点场景时长不足，需要延伸填充 4.05秒
2025-07-29 20:06:29,480 - INFO - 起点场景在原始列表中的索引: 2036
2025-07-29 20:06:29,480 - INFO - 延伸添加场景: scene_id=2038 (完整时长 0.80秒)
2025-07-29 20:06:29,480 - INFO - 累计时长: 1.64秒
2025-07-29 20:06:29,480 - INFO - 延伸添加场景: scene_id=2039 (完整时长 2.68秒)
2025-07-29 20:06:29,480 - INFO - 累计时长: 4.32秒
2025-07-29 20:06:29,480 - INFO - 延伸添加场景: scene_id=2040 (裁剪至 0.57秒)
2025-07-29 20:06:29,480 - INFO - 累计时长: 4.89秒
2025-07-29 20:06:29,480 - INFO - 字幕序号 #2058 场景匹配完成，共选择 4 个场景，总时长: 4.89秒
2025-07-29 20:06:29,480 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:29,480 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:29,480 - INFO - 
--- 生成方案 #2：基于字幕序号 #2063 ---
2025-07-29 20:06:29,480 - INFO - 开始为单个字幕序号 #2063 匹配场景，目标时长: 4.89秒
2025-07-29 20:06:29,480 - INFO - 开始查找字幕序号 [2063] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:29,481 - INFO - 找到related_overlap场景: scene_id=2041, 字幕#2063
2025-07-29 20:06:29,481 - INFO - 字幕 #2063 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:29,482 - INFO - 字幕序号 #2063 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:29,482 - INFO - 选择第一个overlap场景作为起点: scene_id=2041
2025-07-29 20:06:29,482 - INFO - 添加起点场景: scene_id=2041, 时长=1.76秒, 累计时长=1.76秒
2025-07-29 20:06:29,482 - INFO - 起点场景时长不足，需要延伸填充 3.13秒
2025-07-29 20:06:29,482 - INFO - 起点场景在原始列表中的索引: 2040
2025-07-29 20:06:29,482 - INFO - 延伸添加场景: scene_id=2042 (完整时长 1.56秒)
2025-07-29 20:06:29,482 - INFO - 累计时长: 3.32秒
2025-07-29 20:06:29,482 - INFO - 延伸添加场景: scene_id=2043 (裁剪至 1.57秒)
2025-07-29 20:06:29,482 - INFO - 累计时长: 4.89秒
2025-07-29 20:06:29,482 - INFO - 字幕序号 #2063 场景匹配完成，共选择 3 个场景，总时长: 4.89秒
2025-07-29 20:06:29,482 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:06:29,482 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:29,482 - INFO - ========== 当前模式：为字幕 #51 生成 1 套场景方案 ==========
2025-07-29 20:06:29,482 - INFO - 开始查找字幕序号 [2058, 2063] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:29,482 - INFO - 找到related_overlap场景: scene_id=2037, 字幕#2058
2025-07-29 20:06:29,482 - INFO - 找到related_overlap场景: scene_id=2041, 字幕#2063
2025-07-29 20:06:29,483 - INFO - 字幕 #2058 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:29,483 - INFO - 字幕 #2063 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:29,483 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:29,483 - INFO - 开始生成方案 #1
2025-07-29 20:06:29,483 - INFO - 方案 #1: 为字幕#2058选择初始化overlap场景id=2037
2025-07-29 20:06:29,483 - INFO - 方案 #1: 为字幕#2063选择初始化overlap场景id=2041
2025-07-29 20:06:29,483 - INFO - 方案 #1: 初始选择后，当前总时长=2.60秒
2025-07-29 20:06:29,483 - INFO - 方案 #1: 额外between选择后，当前总时长=2.60秒
2025-07-29 20:06:29,483 - INFO - 方案 #1: 场景总时长(2.60秒)小于音频时长(4.89秒)，需要延伸填充
2025-07-29 20:06:29,483 - INFO - 方案 #1: 最后一个场景ID: 2041
2025-07-29 20:06:29,483 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2040
2025-07-29 20:06:29,483 - INFO - 方案 #1: 需要填充时长: 2.29秒
2025-07-29 20:06:29,483 - INFO - 方案 #1: 追加场景 scene_id=2042 (完整时长 1.56秒)
2025-07-29 20:06:29,483 - INFO - 方案 #1: 追加场景 scene_id=2043 (裁剪至 0.73秒)
2025-07-29 20:06:29,483 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:29,483 - INFO - 方案 #1 调整/填充后最终总时长: 4.89秒
2025-07-29 20:06:29,483 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:29,483 - INFO - ========== 当前模式：字幕 #51 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:29,483 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:29,483 - INFO - ========== 新模式：字幕 #51 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:29,484 - INFO - 
----- 处理字幕 #51 的方案 #1 -----
2025-07-29 20:06:29,484 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 20:06:29,484 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_vh8locz
2025-07-29 20:06:29,485 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2037.mp4 (确认存在: True)
2025-07-29 20:06:29,485 - INFO - 添加场景ID=2037，时长=0.84秒，累计时长=0.84秒
2025-07-29 20:06:29,485 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2038.mp4 (确认存在: True)
2025-07-29 20:06:29,485 - INFO - 添加场景ID=2038，时长=0.80秒，累计时长=1.64秒
2025-07-29 20:06:29,485 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2039.mp4 (确认存在: True)
2025-07-29 20:06:29,485 - INFO - 添加场景ID=2039，时长=2.68秒，累计时长=4.32秒
2025-07-29 20:06:29,485 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2040.mp4 (确认存在: True)
2025-07-29 20:06:29,485 - INFO - 添加场景ID=2040，时长=1.40秒，累计时长=5.72秒
2025-07-29 20:06:29,485 - INFO - 准备合并 4 个场景文件，总时长约 5.72秒
2025-07-29 20:06:29,485 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2037.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2038.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2039.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2040.mp4'

2025-07-29 20:06:29,485 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_vh8locz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_vh8locz\temp_combined.mp4
2025-07-29 20:06:29,665 - INFO - 合并后的视频时长: 5.81秒，目标音频时长: 4.89秒
2025-07-29 20:06:29,665 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_vh8locz\temp_combined.mp4 -ss 0 -to 4.893 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 20:06:30,008 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:30,008 - INFO - 目标音频时长: 4.89秒
2025-07-29 20:06:30,008 - INFO - 实际视频时长: 4.94秒
2025-07-29 20:06:30,008 - INFO - 时长差异: 0.05秒 (1.02%)
2025-07-29 20:06:30,008 - INFO - ==========================================
2025-07-29 20:06:30,008 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:30,008 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 20:06:30,009 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_vh8locz
2025-07-29 20:06:30,057 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:30,057 - INFO -   - 音频时长: 4.89秒
2025-07-29 20:06:30,057 - INFO -   - 视频时长: 4.94秒
2025-07-29 20:06:30,057 - INFO -   - 时长差异: 0.05秒 (1.02%)
2025-07-29 20:06:30,057 - INFO - 
----- 处理字幕 #51 的方案 #2 -----
2025-07-29 20:06:30,057 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 20:06:30,057 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp57wc73w7
2025-07-29 20:06:30,059 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2041.mp4 (确认存在: True)
2025-07-29 20:06:30,059 - INFO - 添加场景ID=2041，时长=1.76秒，累计时长=1.76秒
2025-07-29 20:06:30,059 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2042.mp4 (确认存在: True)
2025-07-29 20:06:30,059 - INFO - 添加场景ID=2042，时长=1.56秒，累计时长=3.32秒
2025-07-29 20:06:30,059 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2043.mp4 (确认存在: True)
2025-07-29 20:06:30,059 - INFO - 添加场景ID=2043，时长=2.24秒，累计时长=5.56秒
2025-07-29 20:06:30,059 - INFO - 准备合并 3 个场景文件，总时长约 5.56秒
2025-07-29 20:06:30,059 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2041.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2042.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2043.mp4'

2025-07-29 20:06:30,059 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp57wc73w7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp57wc73w7\temp_combined.mp4
2025-07-29 20:06:30,225 - INFO - 合并后的视频时长: 5.63秒，目标音频时长: 4.89秒
2025-07-29 20:06:30,225 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp57wc73w7\temp_combined.mp4 -ss 0 -to 4.893 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 20:06:30,566 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:30,566 - INFO - 目标音频时长: 4.89秒
2025-07-29 20:06:30,567 - INFO - 实际视频时长: 4.94秒
2025-07-29 20:06:30,567 - INFO - 时长差异: 0.05秒 (1.02%)
2025-07-29 20:06:30,567 - INFO - ==========================================
2025-07-29 20:06:30,567 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:30,567 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 20:06:30,567 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp57wc73w7
2025-07-29 20:06:30,618 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:30,618 - INFO -   - 音频时长: 4.89秒
2025-07-29 20:06:30,618 - INFO -   - 视频时长: 4.94秒
2025-07-29 20:06:30,618 - INFO -   - 时长差异: 0.05秒 (1.02%)
2025-07-29 20:06:30,618 - INFO - 
----- 处理字幕 #51 的方案 #3 -----
2025-07-29 20:06:30,618 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 20:06:30,618 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjv6xg4k4
2025-07-29 20:06:30,619 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2037.mp4 (确认存在: True)
2025-07-29 20:06:30,619 - INFO - 添加场景ID=2037，时长=0.84秒，累计时长=0.84秒
2025-07-29 20:06:30,619 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2041.mp4 (确认存在: True)
2025-07-29 20:06:30,619 - INFO - 添加场景ID=2041，时长=1.76秒，累计时长=2.60秒
2025-07-29 20:06:30,619 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2042.mp4 (确认存在: True)
2025-07-29 20:06:30,619 - INFO - 添加场景ID=2042，时长=1.56秒，累计时长=4.16秒
2025-07-29 20:06:30,619 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2043.mp4 (确认存在: True)
2025-07-29 20:06:30,619 - INFO - 添加场景ID=2043，时长=2.24秒，累计时长=6.40秒
2025-07-29 20:06:30,619 - INFO - 准备合并 4 个场景文件，总时长约 6.40秒
2025-07-29 20:06:30,619 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2037.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2041.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2042.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2043.mp4'

2025-07-29 20:06:30,619 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjv6xg4k4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjv6xg4k4\temp_combined.mp4
2025-07-29 20:06:30,796 - INFO - 合并后的视频时长: 6.49秒，目标音频时长: 4.89秒
2025-07-29 20:06:30,796 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjv6xg4k4\temp_combined.mp4 -ss 0 -to 4.893 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 20:06:31,143 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:31,143 - INFO - 目标音频时长: 4.89秒
2025-07-29 20:06:31,143 - INFO - 实际视频时长: 4.94秒
2025-07-29 20:06:31,143 - INFO - 时长差异: 0.05秒 (1.02%)
2025-07-29 20:06:31,143 - INFO - ==========================================
2025-07-29 20:06:31,143 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:31,143 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 20:06:31,144 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjv6xg4k4
2025-07-29 20:06:31,196 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:31,196 - INFO -   - 音频时长: 4.89秒
2025-07-29 20:06:31,196 - INFO -   - 视频时长: 4.94秒
2025-07-29 20:06:31,196 - INFO -   - 时长差异: 0.05秒 (1.02%)
2025-07-29 20:06:31,196 - INFO - 
字幕 #51 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:31,196 - INFO - 生成的视频文件:
2025-07-29 20:06:31,196 - INFO -   1. F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 20:06:31,196 - INFO -   2. F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 20:06:31,197 - INFO -   3. F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 20:06:31,197 - INFO - ========== 字幕 #51 处理结束 ==========

