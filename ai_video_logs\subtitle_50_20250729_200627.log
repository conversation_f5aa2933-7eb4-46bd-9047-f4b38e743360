2025-07-29 20:06:27,931 - INFO - ========== 字幕 #50 处理开始 ==========
2025-07-29 20:06:27,931 - INFO - 字幕内容: 他们一进场，就遭到了其他企业家的鄙视，嘲笑他们这种小企业是如何混进来的。
2025-07-29 20:06:27,931 - INFO - 字幕序号: [2030, 2033]
2025-07-29 20:06:27,931 - INFO - 音频文件详情:
2025-07-29 20:06:27,931 - INFO -   - 路径: output\50.wav
2025-07-29 20:06:27,931 - INFO -   - 时长: 4.11秒
2025-07-29 20:06:27,931 - INFO -   - 验证音频时长: 4.11秒
2025-07-29 20:06:27,931 - INFO - 字幕时间戳信息:
2025-07-29 20:06:27,931 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:27,931 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:27,932 - INFO -   - 根据生成的音频时长(4.11秒)已调整字幕时间戳
2025-07-29 20:06:27,932 - INFO - ========== 新模式：为字幕 #50 生成4套场景方案 ==========
2025-07-29 20:06:27,932 - INFO - 字幕序号列表: [2030, 2033]
2025-07-29 20:06:27,932 - INFO - 
--- 生成方案 #1：基于字幕序号 #2030 ---
2025-07-29 20:06:27,932 - INFO - 开始为单个字幕序号 #2030 匹配场景，目标时长: 4.11秒
2025-07-29 20:06:27,932 - INFO - 开始查找字幕序号 [2030] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:27,932 - INFO - 找到related_overlap场景: scene_id=2016, 字幕#2030
2025-07-29 20:06:27,932 - INFO - 找到related_overlap场景: scene_id=2017, 字幕#2030
2025-07-29 20:06:27,933 - INFO - 字幕 #2030 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:27,933 - INFO - 字幕序号 #2030 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:27,933 - INFO - 选择第一个overlap场景作为起点: scene_id=2016
2025-07-29 20:06:27,933 - INFO - 添加起点场景: scene_id=2016, 时长=1.56秒, 累计时长=1.56秒
2025-07-29 20:06:27,933 - INFO - 起点场景时长不足，需要延伸填充 2.55秒
2025-07-29 20:06:27,933 - INFO - 起点场景在原始列表中的索引: 2015
2025-07-29 20:06:27,933 - INFO - 延伸添加场景: scene_id=2017 (完整时长 2.52秒)
2025-07-29 20:06:27,933 - INFO - 累计时长: 4.08秒
2025-07-29 20:06:27,933 - INFO - 延伸添加场景: scene_id=2018 (裁剪至 0.04秒)
2025-07-29 20:06:27,933 - INFO - 累计时长: 4.11秒
2025-07-29 20:06:27,933 - INFO - 字幕序号 #2030 场景匹配完成，共选择 3 个场景，总时长: 4.11秒
2025-07-29 20:06:27,933 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:27,933 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:27,933 - INFO - 
--- 生成方案 #2：基于字幕序号 #2033 ---
2025-07-29 20:06:27,934 - INFO - 开始为单个字幕序号 #2033 匹配场景，目标时长: 4.11秒
2025-07-29 20:06:27,934 - INFO - 开始查找字幕序号 [2033] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:27,934 - INFO - 找到related_overlap场景: scene_id=2018, 字幕#2033
2025-07-29 20:06:27,934 - INFO - 找到related_overlap场景: scene_id=2019, 字幕#2033
2025-07-29 20:06:27,935 - INFO - 字幕 #2033 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:27,935 - INFO - 字幕序号 #2033 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:27,935 - INFO - 选择第一个overlap场景作为起点: scene_id=2019
2025-07-29 20:06:27,935 - INFO - 添加起点场景: scene_id=2019, 时长=1.12秒, 累计时长=1.12秒
2025-07-29 20:06:27,935 - INFO - 起点场景时长不足，需要延伸填充 3.00秒
2025-07-29 20:06:27,935 - INFO - 起点场景在原始列表中的索引: 2018
2025-07-29 20:06:27,935 - INFO - 延伸添加场景: scene_id=2020 (完整时长 2.68秒)
2025-07-29 20:06:27,935 - INFO - 累计时长: 3.80秒
2025-07-29 20:06:27,935 - INFO - 延伸添加场景: scene_id=2021 (裁剪至 0.31秒)
2025-07-29 20:06:27,935 - INFO - 累计时长: 4.11秒
2025-07-29 20:06:27,935 - INFO - 字幕序号 #2033 场景匹配完成，共选择 3 个场景，总时长: 4.11秒
2025-07-29 20:06:27,935 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:06:27,935 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:27,935 - INFO - ========== 当前模式：为字幕 #50 生成 1 套场景方案 ==========
2025-07-29 20:06:27,935 - INFO - 开始查找字幕序号 [2030, 2033] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:27,936 - INFO - 找到related_overlap场景: scene_id=2016, 字幕#2030
2025-07-29 20:06:27,936 - INFO - 找到related_overlap场景: scene_id=2017, 字幕#2030
2025-07-29 20:06:27,936 - INFO - 找到related_overlap场景: scene_id=2018, 字幕#2033
2025-07-29 20:06:27,936 - INFO - 找到related_overlap场景: scene_id=2019, 字幕#2033
2025-07-29 20:06:27,937 - INFO - 字幕 #2030 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:27,937 - INFO - 字幕 #2033 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:27,937 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:27,937 - INFO - 开始生成方案 #1
2025-07-29 20:06:27,937 - INFO - 方案 #1: 为字幕#2030选择初始化overlap场景id=2017
2025-07-29 20:06:27,937 - INFO - 方案 #1: 为字幕#2033选择初始化overlap场景id=2019
2025-07-29 20:06:27,937 - INFO - 方案 #1: 初始选择后，当前总时长=3.64秒
2025-07-29 20:06:27,937 - INFO - 方案 #1: 额外添加overlap场景id=2016, 当前总时长=5.20秒
2025-07-29 20:06:27,937 - INFO - 方案 #1: 额外between选择后，当前总时长=5.20秒
2025-07-29 20:06:27,937 - INFO - 方案 #1: 场景总时长(5.20秒)大于音频时长(4.11秒)，需要裁剪
2025-07-29 20:06:27,937 - INFO - 调整前总时长: 5.20秒, 目标时长: 4.11秒
2025-07-29 20:06:27,937 - INFO - 需要裁剪 1.08秒
2025-07-29 20:06:27,937 - INFO - 裁剪最长场景ID=2017：从2.52秒裁剪至1.44秒
2025-07-29 20:06:27,937 - INFO - 调整后总时长: 4.11秒，与目标时长差异: 0.00秒
2025-07-29 20:06:27,937 - INFO - 方案 #1 调整/填充后最终总时长: 4.11秒
2025-07-29 20:06:27,937 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:27,937 - INFO - ========== 当前模式：字幕 #50 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:27,937 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:27,937 - INFO - ========== 新模式：字幕 #50 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:27,937 - INFO - 
----- 处理字幕 #50 的方案 #1 -----
2025-07-29 20:06:27,937 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 20:06:27,937 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1yepmrxl
2025-07-29 20:06:27,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2016.mp4 (确认存在: True)
2025-07-29 20:06:27,939 - INFO - 添加场景ID=2016，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:06:27,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2017.mp4 (确认存在: True)
2025-07-29 20:06:27,939 - INFO - 添加场景ID=2017，时长=2.52秒，累计时长=4.08秒
2025-07-29 20:06:27,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2018.mp4 (确认存在: True)
2025-07-29 20:06:27,939 - INFO - 添加场景ID=2018，时长=1.72秒，累计时长=5.80秒
2025-07-29 20:06:27,939 - INFO - 准备合并 3 个场景文件，总时长约 5.80秒
2025-07-29 20:06:27,939 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2016.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2017.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2018.mp4'

2025-07-29 20:06:27,939 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1yepmrxl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1yepmrxl\temp_combined.mp4
2025-07-29 20:06:28,084 - INFO - 合并后的视频时长: 5.87秒，目标音频时长: 4.11秒
2025-07-29 20:06:28,084 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1yepmrxl\temp_combined.mp4 -ss 0 -to 4.114 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 20:06:28,367 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:28,367 - INFO - 目标音频时长: 4.11秒
2025-07-29 20:06:28,367 - INFO - 实际视频时长: 4.14秒
2025-07-29 20:06:28,367 - INFO - 时长差异: 0.03秒 (0.70%)
2025-07-29 20:06:28,367 - INFO - ==========================================
2025-07-29 20:06:28,367 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:28,367 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 20:06:28,369 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1yepmrxl
2025-07-29 20:06:28,414 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:28,414 - INFO -   - 音频时长: 4.11秒
2025-07-29 20:06:28,414 - INFO -   - 视频时长: 4.14秒
2025-07-29 20:06:28,414 - INFO -   - 时长差异: 0.03秒 (0.70%)
2025-07-29 20:06:28,414 - INFO - 
----- 处理字幕 #50 的方案 #2 -----
2025-07-29 20:06:28,414 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 20:06:28,415 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmatwgvqz
2025-07-29 20:06:28,415 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2019.mp4 (确认存在: True)
2025-07-29 20:06:28,415 - INFO - 添加场景ID=2019，时长=1.12秒，累计时长=1.12秒
2025-07-29 20:06:28,415 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2020.mp4 (确认存在: True)
2025-07-29 20:06:28,415 - INFO - 添加场景ID=2020，时长=2.68秒，累计时长=3.80秒
2025-07-29 20:06:28,415 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2021.mp4 (确认存在: True)
2025-07-29 20:06:28,415 - INFO - 添加场景ID=2021，时长=1.84秒，累计时长=5.64秒
2025-07-29 20:06:28,416 - INFO - 准备合并 3 个场景文件，总时长约 5.64秒
2025-07-29 20:06:28,416 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2019.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2020.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2021.mp4'

2025-07-29 20:06:28,416 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmatwgvqz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmatwgvqz\temp_combined.mp4
2025-07-29 20:06:28,568 - INFO - 合并后的视频时长: 5.71秒，目标音频时长: 4.11秒
2025-07-29 20:06:28,568 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmatwgvqz\temp_combined.mp4 -ss 0 -to 4.114 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 20:06:28,900 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:28,900 - INFO - 目标音频时长: 4.11秒
2025-07-29 20:06:28,900 - INFO - 实际视频时长: 4.14秒
2025-07-29 20:06:28,900 - INFO - 时长差异: 0.03秒 (0.70%)
2025-07-29 20:06:28,900 - INFO - ==========================================
2025-07-29 20:06:28,900 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:28,900 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 20:06:28,901 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmatwgvqz
2025-07-29 20:06:28,949 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:28,949 - INFO -   - 音频时长: 4.11秒
2025-07-29 20:06:28,949 - INFO -   - 视频时长: 4.14秒
2025-07-29 20:06:28,949 - INFO -   - 时长差异: 0.03秒 (0.70%)
2025-07-29 20:06:28,949 - INFO - 
----- 处理字幕 #50 的方案 #3 -----
2025-07-29 20:06:28,949 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 20:06:28,950 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqfys0oep
2025-07-29 20:06:28,950 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2017.mp4 (确认存在: True)
2025-07-29 20:06:28,950 - INFO - 添加场景ID=2017，时长=2.52秒，累计时长=2.52秒
2025-07-29 20:06:28,950 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2019.mp4 (确认存在: True)
2025-07-29 20:06:28,950 - INFO - 添加场景ID=2019，时长=1.12秒，累计时长=3.64秒
2025-07-29 20:06:28,950 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2016.mp4 (确认存在: True)
2025-07-29 20:06:28,950 - INFO - 添加场景ID=2016，时长=1.56秒，累计时长=5.20秒
2025-07-29 20:06:28,951 - INFO - 准备合并 3 个场景文件，总时长约 5.20秒
2025-07-29 20:06:28,951 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2017.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2019.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2016.mp4'

2025-07-29 20:06:28,951 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqfys0oep\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqfys0oep\temp_combined.mp4
2025-07-29 20:06:29,108 - INFO - 合并后的视频时长: 5.27秒，目标音频时长: 4.11秒
2025-07-29 20:06:29,109 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqfys0oep\temp_combined.mp4 -ss 0 -to 4.114 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 20:06:29,429 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:29,429 - INFO - 目标音频时长: 4.11秒
2025-07-29 20:06:29,429 - INFO - 实际视频时长: 4.14秒
2025-07-29 20:06:29,429 - INFO - 时长差异: 0.03秒 (0.70%)
2025-07-29 20:06:29,429 - INFO - ==========================================
2025-07-29 20:06:29,429 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:29,429 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 20:06:29,430 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqfys0oep
2025-07-29 20:06:29,476 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:29,476 - INFO -   - 音频时长: 4.11秒
2025-07-29 20:06:29,476 - INFO -   - 视频时长: 4.14秒
2025-07-29 20:06:29,476 - INFO -   - 时长差异: 0.03秒 (0.70%)
2025-07-29 20:06:29,476 - INFO - 
字幕 #50 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:29,476 - INFO - 生成的视频文件:
2025-07-29 20:06:29,476 - INFO -   1. F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 20:06:29,476 - INFO -   2. F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 20:06:29,476 - INFO -   3. F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 20:06:29,476 - INFO - ========== 字幕 #50 处理结束 ==========

