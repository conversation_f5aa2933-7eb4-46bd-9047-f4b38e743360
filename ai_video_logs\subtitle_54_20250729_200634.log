2025-07-29 20:06:34,223 - INFO - ========== 字幕 #54 处理开始 ==========
2025-07-29 20:06:34,223 - INFO - 字幕内容: 没想到，这份尘封七年的计划书，竟让林总和另一位老总大加赞赏，称其构思精彩，预测了未来二十年的发展。
2025-07-29 20:06:34,223 - INFO - 字幕序号: [2193, 2200]
2025-07-29 20:06:34,224 - INFO - 音频文件详情:
2025-07-29 20:06:34,224 - INFO -   - 路径: output\54.wav
2025-07-29 20:06:34,224 - INFO -   - 时长: 7.76秒
2025-07-29 20:06:34,224 - INFO -   - 验证音频时长: 7.76秒
2025-07-29 20:06:34,234 - INFO - 字幕时间戳信息:
2025-07-29 20:06:34,234 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:34,234 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:34,234 - INFO -   - 根据生成的音频时长(7.76秒)已调整字幕时间戳
2025-07-29 20:06:34,234 - INFO - ========== 新模式：为字幕 #54 生成4套场景方案 ==========
2025-07-29 20:06:34,234 - INFO - 字幕序号列表: [2193, 2200]
2025-07-29 20:06:34,234 - INFO - 
--- 生成方案 #1：基于字幕序号 #2193 ---
2025-07-29 20:06:34,234 - INFO - 开始为单个字幕序号 #2193 匹配场景，目标时长: 7.76秒
2025-07-29 20:06:34,234 - INFO - 开始查找字幕序号 [2193] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:34,235 - INFO - 找到related_overlap场景: scene_id=2153, 字幕#2193
2025-07-29 20:06:34,235 - INFO - 找到related_between场景: scene_id=2151, 字幕#2193
2025-07-29 20:06:34,235 - INFO - 找到related_between场景: scene_id=2152, 字幕#2193
2025-07-29 20:06:34,235 - INFO - 字幕 #2193 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:34,235 - INFO - 字幕序号 #2193 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:06:34,235 - INFO - 选择第一个overlap场景作为起点: scene_id=2153
2025-07-29 20:06:34,235 - INFO - 添加起点场景: scene_id=2153, 时长=2.04秒, 累计时长=2.04秒
2025-07-29 20:06:34,235 - INFO - 起点场景时长不足，需要延伸填充 5.72秒
2025-07-29 20:06:34,235 - INFO - 起点场景在原始列表中的索引: 2152
2025-07-29 20:06:34,235 - INFO - 延伸添加场景: scene_id=2154 (完整时长 1.32秒)
2025-07-29 20:06:34,235 - INFO - 累计时长: 3.36秒
2025-07-29 20:06:34,235 - INFO - 延伸添加场景: scene_id=2155 (完整时长 1.68秒)
2025-07-29 20:06:34,235 - INFO - 累计时长: 5.04秒
2025-07-29 20:06:34,235 - INFO - 延伸添加场景: scene_id=2156 (裁剪至 2.72秒)
2025-07-29 20:06:34,235 - INFO - 累计时长: 7.76秒
2025-07-29 20:06:34,235 - INFO - 字幕序号 #2193 场景匹配完成，共选择 4 个场景，总时长: 7.76秒
2025-07-29 20:06:34,235 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:34,235 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:34,235 - INFO - 
--- 生成方案 #2：基于字幕序号 #2200 ---
2025-07-29 20:06:34,236 - INFO - 开始为单个字幕序号 #2200 匹配场景，目标时长: 7.76秒
2025-07-29 20:06:34,236 - INFO - 开始查找字幕序号 [2200] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:34,236 - INFO - 找到related_overlap场景: scene_id=2160, 字幕#2200
2025-07-29 20:06:34,236 - INFO - 找到related_between场景: scene_id=2161, 字幕#2200
2025-07-29 20:06:34,236 - INFO - 找到related_between场景: scene_id=2162, 字幕#2200
2025-07-29 20:06:34,236 - INFO - 找到related_between场景: scene_id=2163, 字幕#2200
2025-07-29 20:06:34,237 - INFO - 字幕 #2200 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:06:34,237 - INFO - 字幕序号 #2200 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 20:06:34,237 - INFO - 选择第一个overlap场景作为起点: scene_id=2160
2025-07-29 20:06:34,237 - INFO - 添加起点场景: scene_id=2160, 时长=1.68秒, 累计时长=1.68秒
2025-07-29 20:06:34,237 - INFO - 起点场景时长不足，需要延伸填充 6.08秒
2025-07-29 20:06:34,237 - INFO - 起点场景在原始列表中的索引: 2159
2025-07-29 20:06:34,237 - INFO - 延伸添加场景: scene_id=2161 (完整时长 1.80秒)
2025-07-29 20:06:34,237 - INFO - 累计时长: 3.48秒
2025-07-29 20:06:34,237 - INFO - 延伸添加场景: scene_id=2162 (完整时长 1.64秒)
2025-07-29 20:06:34,237 - INFO - 累计时长: 5.12秒
2025-07-29 20:06:34,237 - INFO - 延伸添加场景: scene_id=2163 (完整时长 1.76秒)
2025-07-29 20:06:34,237 - INFO - 累计时长: 6.88秒
2025-07-29 20:06:34,237 - INFO - 延伸添加场景: scene_id=2164 (裁剪至 0.88秒)
2025-07-29 20:06:34,237 - INFO - 累计时长: 7.76秒
2025-07-29 20:06:34,237 - INFO - 字幕序号 #2200 场景匹配完成，共选择 5 个场景，总时长: 7.76秒
2025-07-29 20:06:34,237 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 20:06:34,237 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:34,237 - INFO - ========== 当前模式：为字幕 #54 生成 1 套场景方案 ==========
2025-07-29 20:06:34,237 - INFO - 开始查找字幕序号 [2193, 2200] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:34,237 - INFO - 找到related_overlap场景: scene_id=2153, 字幕#2193
2025-07-29 20:06:34,237 - INFO - 找到related_overlap场景: scene_id=2160, 字幕#2200
2025-07-29 20:06:34,239 - INFO - 找到related_between场景: scene_id=2151, 字幕#2193
2025-07-29 20:06:34,239 - INFO - 找到related_between场景: scene_id=2152, 字幕#2193
2025-07-29 20:06:34,239 - INFO - 找到related_between场景: scene_id=2161, 字幕#2200
2025-07-29 20:06:34,239 - INFO - 找到related_between场景: scene_id=2162, 字幕#2200
2025-07-29 20:06:34,239 - INFO - 找到related_between场景: scene_id=2163, 字幕#2200
2025-07-29 20:06:34,239 - INFO - 字幕 #2193 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:34,239 - INFO - 字幕 #2200 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:06:34,239 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 20:06:34,239 - INFO - 开始生成方案 #1
2025-07-29 20:06:34,239 - INFO - 方案 #1: 为字幕#2193选择初始化overlap场景id=2153
2025-07-29 20:06:34,239 - INFO - 方案 #1: 为字幕#2200选择初始化overlap场景id=2160
2025-07-29 20:06:34,239 - INFO - 方案 #1: 初始选择后，当前总时长=3.72秒
2025-07-29 20:06:34,239 - INFO - 方案 #1: 额外between选择后，当前总时长=3.72秒
2025-07-29 20:06:34,239 - INFO - 方案 #1: 额外添加between场景id=2151, 当前总时长=4.36秒
2025-07-29 20:06:34,239 - INFO - 方案 #1: 额外添加between场景id=2152, 当前总时长=5.40秒
2025-07-29 20:06:34,239 - INFO - 方案 #1: 额外添加between场景id=2163, 当前总时长=7.16秒
2025-07-29 20:06:34,239 - INFO - 方案 #1: 额外添加between场景id=2162, 当前总时长=8.80秒
2025-07-29 20:06:34,239 - INFO - 方案 #1: 场景总时长(8.80秒)大于音频时长(7.76秒)，需要裁剪
2025-07-29 20:06:34,239 - INFO - 调整前总时长: 8.80秒, 目标时长: 7.76秒
2025-07-29 20:06:34,239 - INFO - 需要裁剪 1.04秒
2025-07-29 20:06:34,239 - INFO - 裁剪最长场景ID=2153：从2.04秒裁剪至1.00秒
2025-07-29 20:06:34,239 - INFO - 调整后总时长: 7.76秒，与目标时长差异: 0.00秒
2025-07-29 20:06:34,239 - INFO - 方案 #1 调整/填充后最终总时长: 7.76秒
2025-07-29 20:06:34,239 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:34,239 - INFO - ========== 当前模式：字幕 #54 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:34,239 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:34,239 - INFO - ========== 新模式：字幕 #54 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:34,239 - INFO - 
----- 处理字幕 #54 的方案 #1 -----
2025-07-29 20:06:34,239 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 20:06:34,240 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1csfi4nk
2025-07-29 20:06:34,240 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2153.mp4 (确认存在: True)
2025-07-29 20:06:34,240 - INFO - 添加场景ID=2153，时长=2.04秒，累计时长=2.04秒
2025-07-29 20:06:34,240 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2154.mp4 (确认存在: True)
2025-07-29 20:06:34,240 - INFO - 添加场景ID=2154，时长=1.32秒，累计时长=3.36秒
2025-07-29 20:06:34,241 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2155.mp4 (确认存在: True)
2025-07-29 20:06:34,241 - INFO - 添加场景ID=2155，时长=1.68秒，累计时长=5.04秒
2025-07-29 20:06:34,241 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2156.mp4 (确认存在: True)
2025-07-29 20:06:34,241 - INFO - 添加场景ID=2156，时长=3.24秒，累计时长=8.28秒
2025-07-29 20:06:34,241 - INFO - 准备合并 4 个场景文件，总时长约 8.28秒
2025-07-29 20:06:34,241 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2153.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2154.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2155.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2156.mp4'

2025-07-29 20:06:34,241 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1csfi4nk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1csfi4nk\temp_combined.mp4
2025-07-29 20:06:34,406 - INFO - 合并后的视频时长: 8.37秒，目标音频时长: 7.76秒
2025-07-29 20:06:34,406 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1csfi4nk\temp_combined.mp4 -ss 0 -to 7.757 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 20:06:34,827 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:34,827 - INFO - 目标音频时长: 7.76秒
2025-07-29 20:06:34,827 - INFO - 实际视频时长: 7.78秒
2025-07-29 20:06:34,827 - INFO - 时长差异: 0.03秒 (0.34%)
2025-07-29 20:06:34,827 - INFO - ==========================================
2025-07-29 20:06:34,827 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:34,827 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 20:06:34,828 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1csfi4nk
2025-07-29 20:06:34,872 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:34,872 - INFO -   - 音频时长: 7.76秒
2025-07-29 20:06:34,872 - INFO -   - 视频时长: 7.78秒
2025-07-29 20:06:34,872 - INFO -   - 时长差异: 0.03秒 (0.34%)
2025-07-29 20:06:34,872 - INFO - 
----- 处理字幕 #54 的方案 #2 -----
2025-07-29 20:06:34,872 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 20:06:34,872 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsiwj7gjx
2025-07-29 20:06:34,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2160.mp4 (确认存在: True)
2025-07-29 20:06:34,873 - INFO - 添加场景ID=2160，时长=1.68秒，累计时长=1.68秒
2025-07-29 20:06:34,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2161.mp4 (确认存在: True)
2025-07-29 20:06:34,873 - INFO - 添加场景ID=2161，时长=1.80秒，累计时长=3.48秒
2025-07-29 20:06:34,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2162.mp4 (确认存在: True)
2025-07-29 20:06:34,873 - INFO - 添加场景ID=2162，时长=1.64秒，累计时长=5.12秒
2025-07-29 20:06:34,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2163.mp4 (确认存在: True)
2025-07-29 20:06:34,873 - INFO - 添加场景ID=2163，时长=1.76秒，累计时长=6.88秒
2025-07-29 20:06:34,873 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2164.mp4 (确认存在: True)
2025-07-29 20:06:34,873 - INFO - 添加场景ID=2164，时长=1.36秒，累计时长=8.24秒
2025-07-29 20:06:34,873 - INFO - 准备合并 5 个场景文件，总时长约 8.24秒
2025-07-29 20:06:34,873 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2160.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2161.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2162.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2163.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2164.mp4'

2025-07-29 20:06:34,874 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsiwj7gjx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsiwj7gjx\temp_combined.mp4
2025-07-29 20:06:35,070 - INFO - 合并后的视频时长: 8.36秒，目标音频时长: 7.76秒
2025-07-29 20:06:35,070 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsiwj7gjx\temp_combined.mp4 -ss 0 -to 7.757 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 20:06:35,483 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:35,483 - INFO - 目标音频时长: 7.76秒
2025-07-29 20:06:35,483 - INFO - 实际视频时长: 7.78秒
2025-07-29 20:06:35,483 - INFO - 时长差异: 0.03秒 (0.34%)
2025-07-29 20:06:35,483 - INFO - ==========================================
2025-07-29 20:06:35,483 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:35,483 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 20:06:35,484 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsiwj7gjx
2025-07-29 20:06:35,529 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:35,529 - INFO -   - 音频时长: 7.76秒
2025-07-29 20:06:35,529 - INFO -   - 视频时长: 7.78秒
2025-07-29 20:06:35,529 - INFO -   - 时长差异: 0.03秒 (0.34%)
2025-07-29 20:06:35,529 - INFO - 
----- 处理字幕 #54 的方案 #3 -----
2025-07-29 20:06:35,529 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 20:06:35,529 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyjl7tqvc
2025-07-29 20:06:35,530 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2153.mp4 (确认存在: True)
2025-07-29 20:06:35,530 - INFO - 添加场景ID=2153，时长=2.04秒，累计时长=2.04秒
2025-07-29 20:06:35,530 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2160.mp4 (确认存在: True)
2025-07-29 20:06:35,530 - INFO - 添加场景ID=2160，时长=1.68秒，累计时长=3.72秒
2025-07-29 20:06:35,530 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2151.mp4 (确认存在: True)
2025-07-29 20:06:35,530 - INFO - 添加场景ID=2151，时长=0.64秒，累计时长=4.36秒
2025-07-29 20:06:35,530 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2152.mp4 (确认存在: True)
2025-07-29 20:06:35,530 - INFO - 添加场景ID=2152，时长=1.04秒，累计时长=5.40秒
2025-07-29 20:06:35,530 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2163.mp4 (确认存在: True)
2025-07-29 20:06:35,530 - INFO - 添加场景ID=2163，时长=1.76秒，累计时长=7.16秒
2025-07-29 20:06:35,530 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2162.mp4 (确认存在: True)
2025-07-29 20:06:35,530 - INFO - 添加场景ID=2162，时长=1.64秒，累计时长=8.80秒
2025-07-29 20:06:35,530 - INFO - 准备合并 6 个场景文件，总时长约 8.80秒
2025-07-29 20:06:35,530 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2153.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2160.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2151.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2152.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2163.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2162.mp4'

2025-07-29 20:06:35,530 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyjl7tqvc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyjl7tqvc\temp_combined.mp4
2025-07-29 20:06:35,690 - INFO - 合并后的视频时长: 8.94秒，目标音频时长: 7.76秒
2025-07-29 20:06:35,691 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyjl7tqvc\temp_combined.mp4 -ss 0 -to 7.757 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 20:06:36,076 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:36,076 - INFO - 目标音频时长: 7.76秒
2025-07-29 20:06:36,076 - INFO - 实际视频时长: 7.78秒
2025-07-29 20:06:36,076 - INFO - 时长差异: 0.03秒 (0.34%)
2025-07-29 20:06:36,076 - INFO - ==========================================
2025-07-29 20:06:36,076 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:36,076 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 20:06:36,077 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyjl7tqvc
2025-07-29 20:06:36,121 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:36,121 - INFO -   - 音频时长: 7.76秒
2025-07-29 20:06:36,121 - INFO -   - 视频时长: 7.78秒
2025-07-29 20:06:36,121 - INFO -   - 时长差异: 0.03秒 (0.34%)
2025-07-29 20:06:36,121 - INFO - 
字幕 #54 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:36,121 - INFO - 生成的视频文件:
2025-07-29 20:06:36,121 - INFO -   1. F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 20:06:36,121 - INFO -   2. F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 20:06:36,121 - INFO -   3. F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 20:06:36,121 - INFO - ========== 字幕 #54 处理结束 ==========

