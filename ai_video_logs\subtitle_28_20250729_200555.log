2025-07-29 20:05:55,624 - INFO - ========== 字幕 #28 处理开始 ==========
2025-07-29 20:05:55,624 - INFO - 字幕内容: 任何额外的开销，都必须写下欠条，七年下来，竟攒了厚厚一沓，共计五百张！
2025-07-29 20:05:55,624 - INFO - 字幕序号: [504, 509]
2025-07-29 20:05:55,625 - INFO - 音频文件详情:
2025-07-29 20:05:55,625 - INFO -   - 路径: output\28.wav
2025-07-29 20:05:55,625 - INFO -   - 时长: 5.19秒
2025-07-29 20:05:55,625 - INFO -   - 验证音频时长: 5.19秒
2025-07-29 20:05:55,625 - INFO - 字幕时间戳信息:
2025-07-29 20:05:55,625 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:55,625 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:55,625 - INFO -   - 根据生成的音频时长(5.19秒)已调整字幕时间戳
2025-07-29 20:05:55,625 - INFO - ========== 新模式：为字幕 #28 生成4套场景方案 ==========
2025-07-29 20:05:55,625 - INFO - 字幕序号列表: [504, 509]
2025-07-29 20:05:55,625 - INFO - 
--- 生成方案 #1：基于字幕序号 #504 ---
2025-07-29 20:05:55,625 - INFO - 开始为单个字幕序号 #504 匹配场景，目标时长: 5.19秒
2025-07-29 20:05:55,625 - INFO - 开始查找字幕序号 [504] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:55,626 - INFO - 找到related_overlap场景: scene_id=533, 字幕#504
2025-07-29 20:05:55,627 - INFO - 字幕 #504 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:55,628 - INFO - 字幕序号 #504 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:55,628 - INFO - 选择第一个overlap场景作为起点: scene_id=533
2025-07-29 20:05:55,628 - INFO - 添加起点场景: scene_id=533, 时长=3.88秒, 累计时长=3.88秒
2025-07-29 20:05:55,628 - INFO - 起点场景时长不足，需要延伸填充 1.31秒
2025-07-29 20:05:55,628 - INFO - 起点场景在原始列表中的索引: 532
2025-07-29 20:05:55,628 - INFO - 延伸添加场景: scene_id=534 (完整时长 0.88秒)
2025-07-29 20:05:55,628 - INFO - 累计时长: 4.76秒
2025-07-29 20:05:55,628 - INFO - 延伸添加场景: scene_id=535 (裁剪至 0.43秒)
2025-07-29 20:05:55,628 - INFO - 累计时长: 5.19秒
2025-07-29 20:05:55,628 - INFO - 字幕序号 #504 场景匹配完成，共选择 3 个场景，总时长: 5.19秒
2025-07-29 20:05:55,628 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:55,628 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:55,628 - INFO - 
--- 生成方案 #2：基于字幕序号 #509 ---
2025-07-29 20:05:55,628 - INFO - 开始为单个字幕序号 #509 匹配场景，目标时长: 5.19秒
2025-07-29 20:05:55,628 - INFO - 开始查找字幕序号 [509] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:55,628 - INFO - 找到related_overlap场景: scene_id=536, 字幕#509
2025-07-29 20:05:55,630 - INFO - 字幕 #509 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:55,630 - INFO - 字幕序号 #509 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:55,630 - INFO - 选择第一个overlap场景作为起点: scene_id=536
2025-07-29 20:05:55,630 - INFO - 添加起点场景: scene_id=536, 时长=1.96秒, 累计时长=1.96秒
2025-07-29 20:05:55,630 - INFO - 起点场景时长不足，需要延伸填充 3.23秒
2025-07-29 20:05:55,630 - INFO - 起点场景在原始列表中的索引: 535
2025-07-29 20:05:55,630 - INFO - 延伸添加场景: scene_id=537 (完整时长 2.76秒)
2025-07-29 20:05:55,630 - INFO - 累计时长: 4.72秒
2025-07-29 20:05:55,630 - INFO - 延伸添加场景: scene_id=538 (裁剪至 0.47秒)
2025-07-29 20:05:55,631 - INFO - 累计时长: 5.19秒
2025-07-29 20:05:55,631 - INFO - 字幕序号 #509 场景匹配完成，共选择 3 个场景，总时长: 5.19秒
2025-07-29 20:05:55,631 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:55,631 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:55,631 - INFO - ========== 当前模式：为字幕 #28 生成 1 套场景方案 ==========
2025-07-29 20:05:55,631 - INFO - 开始查找字幕序号 [504, 509] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:55,631 - INFO - 找到related_overlap场景: scene_id=533, 字幕#504
2025-07-29 20:05:55,631 - INFO - 找到related_overlap场景: scene_id=536, 字幕#509
2025-07-29 20:05:55,632 - INFO - 字幕 #504 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:55,632 - INFO - 字幕 #509 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:55,632 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:55,632 - INFO - 开始生成方案 #1
2025-07-29 20:05:55,632 - INFO - 方案 #1: 为字幕#504选择初始化overlap场景id=533
2025-07-29 20:05:55,632 - INFO - 方案 #1: 为字幕#509选择初始化overlap场景id=536
2025-07-29 20:05:55,632 - INFO - 方案 #1: 初始选择后，当前总时长=5.84秒
2025-07-29 20:05:55,632 - INFO - 方案 #1: 额外between选择后，当前总时长=5.84秒
2025-07-29 20:05:55,632 - INFO - 方案 #1: 场景总时长(5.84秒)大于音频时长(5.19秒)，需要裁剪
2025-07-29 20:05:55,632 - INFO - 调整前总时长: 5.84秒, 目标时长: 5.19秒
2025-07-29 20:05:55,632 - INFO - 需要裁剪 0.65秒
2025-07-29 20:05:55,632 - INFO - 裁剪最长场景ID=533：从3.88秒裁剪至3.23秒
2025-07-29 20:05:55,632 - INFO - 调整后总时长: 5.19秒，与目标时长差异: 0.00秒
2025-07-29 20:05:55,632 - INFO - 方案 #1 调整/填充后最终总时长: 5.19秒
2025-07-29 20:05:55,632 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:55,632 - INFO - ========== 当前模式：字幕 #28 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:55,632 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:55,632 - INFO - ========== 新模式：字幕 #28 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:55,632 - INFO - 
----- 处理字幕 #28 的方案 #1 -----
2025-07-29 20:05:55,632 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 20:05:55,633 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp_lz_g_x
2025-07-29 20:05:55,633 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\533.mp4 (确认存在: True)
2025-07-29 20:05:55,633 - INFO - 添加场景ID=533，时长=3.88秒，累计时长=3.88秒
2025-07-29 20:05:55,633 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\534.mp4 (确认存在: True)
2025-07-29 20:05:55,633 - INFO - 添加场景ID=534，时长=0.88秒，累计时长=4.76秒
2025-07-29 20:05:55,634 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\535.mp4 (确认存在: True)
2025-07-29 20:05:55,634 - INFO - 添加场景ID=535，时长=1.76秒，累计时长=6.52秒
2025-07-29 20:05:55,634 - INFO - 准备合并 3 个场景文件，总时长约 6.52秒
2025-07-29 20:05:55,634 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/533.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/534.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/535.mp4'

2025-07-29 20:05:55,634 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp_lz_g_x\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp_lz_g_x\temp_combined.mp4
2025-07-29 20:05:55,761 - INFO - 合并后的视频时长: 6.59秒，目标音频时长: 5.19秒
2025-07-29 20:05:55,761 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp_lz_g_x\temp_combined.mp4 -ss 0 -to 5.188 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 20:05:56,093 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:56,093 - INFO - 目标音频时长: 5.19秒
2025-07-29 20:05:56,093 - INFO - 实际视频时长: 5.22秒
2025-07-29 20:05:56,094 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:56,094 - INFO - ==========================================
2025-07-29 20:05:56,094 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:56,094 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 20:05:56,094 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp_lz_g_x
2025-07-29 20:05:56,140 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:56,140 - INFO -   - 音频时长: 5.19秒
2025-07-29 20:05:56,140 - INFO -   - 视频时长: 5.22秒
2025-07-29 20:05:56,140 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:56,140 - INFO - 
----- 处理字幕 #28 的方案 #2 -----
2025-07-29 20:05:56,140 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 20:05:56,140 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptj40g52n
2025-07-29 20:05:56,141 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\536.mp4 (确认存在: True)
2025-07-29 20:05:56,141 - INFO - 添加场景ID=536，时长=1.96秒，累计时长=1.96秒
2025-07-29 20:05:56,141 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\537.mp4 (确认存在: True)
2025-07-29 20:05:56,141 - INFO - 添加场景ID=537，时长=2.76秒，累计时长=4.72秒
2025-07-29 20:05:56,141 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\538.mp4 (确认存在: True)
2025-07-29 20:05:56,141 - INFO - 添加场景ID=538，时长=1.76秒，累计时长=6.48秒
2025-07-29 20:05:56,141 - INFO - 准备合并 3 个场景文件，总时长约 6.48秒
2025-07-29 20:05:56,141 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/536.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/537.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/538.mp4'

2025-07-29 20:05:56,141 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptj40g52n\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptj40g52n\temp_combined.mp4
2025-07-29 20:05:56,272 - INFO - 合并后的视频时长: 6.55秒，目标音频时长: 5.19秒
2025-07-29 20:05:56,272 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptj40g52n\temp_combined.mp4 -ss 0 -to 5.188 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 20:05:56,577 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:56,577 - INFO - 目标音频时长: 5.19秒
2025-07-29 20:05:56,577 - INFO - 实际视频时长: 5.22秒
2025-07-29 20:05:56,577 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:56,577 - INFO - ==========================================
2025-07-29 20:05:56,577 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:56,577 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 20:05:56,578 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptj40g52n
2025-07-29 20:05:56,621 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:56,621 - INFO -   - 音频时长: 5.19秒
2025-07-29 20:05:56,621 - INFO -   - 视频时长: 5.22秒
2025-07-29 20:05:56,621 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:56,621 - INFO - 
----- 处理字幕 #28 的方案 #3 -----
2025-07-29 20:05:56,621 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 20:05:56,622 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcv00rkov
2025-07-29 20:05:56,622 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\533.mp4 (确认存在: True)
2025-07-29 20:05:56,622 - INFO - 添加场景ID=533，时长=3.88秒，累计时长=3.88秒
2025-07-29 20:05:56,622 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\536.mp4 (确认存在: True)
2025-07-29 20:05:56,622 - INFO - 添加场景ID=536，时长=1.96秒，累计时长=5.84秒
2025-07-29 20:05:56,623 - INFO - 准备合并 2 个场景文件，总时长约 5.84秒
2025-07-29 20:05:56,623 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/533.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/536.mp4'

2025-07-29 20:05:56,623 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcv00rkov\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcv00rkov\temp_combined.mp4
2025-07-29 20:05:56,733 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 5.19秒
2025-07-29 20:05:56,733 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcv00rkov\temp_combined.mp4 -ss 0 -to 5.188 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 20:05:57,048 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:57,048 - INFO - 目标音频时长: 5.19秒
2025-07-29 20:05:57,048 - INFO - 实际视频时长: 5.22秒
2025-07-29 20:05:57,048 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:57,048 - INFO - ==========================================
2025-07-29 20:05:57,048 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:57,048 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 20:05:57,049 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcv00rkov
2025-07-29 20:05:57,093 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:57,093 - INFO -   - 音频时长: 5.19秒
2025-07-29 20:05:57,093 - INFO -   - 视频时长: 5.22秒
2025-07-29 20:05:57,093 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:57,093 - INFO - 
字幕 #28 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:57,093 - INFO - 生成的视频文件:
2025-07-29 20:05:57,093 - INFO -   1. F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 20:05:57,093 - INFO -   2. F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 20:05:57,093 - INFO -   3. F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 20:05:57,093 - INFO - ========== 字幕 #28 处理结束 ==========

