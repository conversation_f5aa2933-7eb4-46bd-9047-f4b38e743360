2025-07-29 20:07:08,236 - INFO - ========== 字幕 #79 处理开始 ==========
2025-07-29 20:07:08,236 - INFO - 字幕内容: 弥留之际，她对男人说，自己欠他的，欠安安的，这辈子都还不清了。
2025-07-29 20:07:08,236 - INFO - 字幕序号: [4259, 4262]
2025-07-29 20:07:08,236 - INFO - 音频文件详情:
2025-07-29 20:07:08,236 - INFO -   - 路径: output\79.wav
2025-07-29 20:07:08,236 - INFO -   - 时长: 3.99秒
2025-07-29 20:07:08,236 - INFO -   - 验证音频时长: 3.99秒
2025-07-29 20:07:08,237 - INFO - 字幕时间戳信息:
2025-07-29 20:07:08,237 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:08,237 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:08,237 - INFO -   - 根据生成的音频时长(3.99秒)已调整字幕时间戳
2025-07-29 20:07:08,237 - INFO - ========== 新模式：为字幕 #79 生成4套场景方案 ==========
2025-07-29 20:07:08,237 - INFO - 字幕序号列表: [4259, 4262]
2025-07-29 20:07:08,237 - INFO - 
--- 生成方案 #1：基于字幕序号 #4259 ---
2025-07-29 20:07:08,237 - INFO - 开始为单个字幕序号 #4259 匹配场景，目标时长: 3.99秒
2025-07-29 20:07:08,237 - INFO - 开始查找字幕序号 [4259] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:08,238 - INFO - 找到related_overlap场景: scene_id=3767, 字幕#4259
2025-07-29 20:07:08,238 - INFO - 字幕 #4259 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:08,238 - INFO - 字幕序号 #4259 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:08,239 - INFO - 选择第一个overlap场景作为起点: scene_id=3767
2025-07-29 20:07:08,239 - INFO - 添加起点场景: scene_id=3767, 时长=1.40秒, 累计时长=1.40秒
2025-07-29 20:07:08,239 - INFO - 起点场景时长不足，需要延伸填充 2.60秒
2025-07-29 20:07:08,239 - INFO - 起点场景在原始列表中的索引: 3766
2025-07-29 20:07:08,239 - INFO - 延伸添加场景: scene_id=3768 (完整时长 1.40秒)
2025-07-29 20:07:08,239 - INFO - 累计时长: 2.80秒
2025-07-29 20:07:08,239 - INFO - 延伸添加场景: scene_id=3769 (裁剪至 1.20秒)
2025-07-29 20:07:08,239 - INFO - 累计时长: 3.99秒
2025-07-29 20:07:08,239 - INFO - 字幕序号 #4259 场景匹配完成，共选择 3 个场景，总时长: 3.99秒
2025-07-29 20:07:08,239 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:07:08,239 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:07:08,239 - INFO - 
--- 生成方案 #2：基于字幕序号 #4262 ---
2025-07-29 20:07:08,239 - INFO - 开始为单个字幕序号 #4262 匹配场景，目标时长: 3.99秒
2025-07-29 20:07:08,239 - INFO - 开始查找字幕序号 [4262] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:08,240 - INFO - 找到related_overlap场景: scene_id=3770, 字幕#4262
2025-07-29 20:07:08,240 - INFO - 字幕 #4262 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:08,240 - INFO - 字幕序号 #4262 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:08,240 - INFO - 选择第一个overlap场景作为起点: scene_id=3770
2025-07-29 20:07:08,240 - INFO - 添加起点场景: scene_id=3770, 时长=4.44秒, 累计时长=4.44秒
2025-07-29 20:07:08,240 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:07:08,240 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 20:07:08,240 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:07:08,240 - INFO - ========== 当前模式：为字幕 #79 生成 1 套场景方案 ==========
2025-07-29 20:07:08,240 - INFO - 开始查找字幕序号 [4259, 4262] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:08,241 - INFO - 找到related_overlap场景: scene_id=3767, 字幕#4259
2025-07-29 20:07:08,241 - INFO - 找到related_overlap场景: scene_id=3770, 字幕#4262
2025-07-29 20:07:08,241 - INFO - 字幕 #4259 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:08,241 - INFO - 字幕 #4262 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:08,241 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:07:08,241 - INFO - 开始生成方案 #1
2025-07-29 20:07:08,242 - INFO - 方案 #1: 为字幕#4259选择初始化overlap场景id=3767
2025-07-29 20:07:08,242 - INFO - 方案 #1: 为字幕#4262选择初始化overlap场景id=3770
2025-07-29 20:07:08,242 - INFO - 方案 #1: 初始选择后，当前总时长=5.84秒
2025-07-29 20:07:08,242 - INFO - 方案 #1: 额外between选择后，当前总时长=5.84秒
2025-07-29 20:07:08,242 - INFO - 方案 #1: 场景总时长(5.84秒)大于音频时长(3.99秒)，需要裁剪
2025-07-29 20:07:08,242 - INFO - 调整前总时长: 5.84秒, 目标时长: 3.99秒
2025-07-29 20:07:08,242 - INFO - 需要裁剪 1.84秒
2025-07-29 20:07:08,242 - INFO - 裁剪最长场景ID=3770：从4.44秒裁剪至2.60秒
2025-07-29 20:07:08,242 - INFO - 调整后总时长: 3.99秒，与目标时长差异: 0.00秒
2025-07-29 20:07:08,242 - INFO - 方案 #1 调整/填充后最终总时长: 3.99秒
2025-07-29 20:07:08,242 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:08,242 - INFO - ========== 当前模式：字幕 #79 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:08,242 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:07:08,242 - INFO - ========== 新模式：字幕 #79 共生成 3 套有效场景方案 ==========
2025-07-29 20:07:08,242 - INFO - 
----- 处理字幕 #79 的方案 #1 -----
2025-07-29 20:07:08,242 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\79_1.mp4
2025-07-29 20:07:08,242 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuyrnx197
2025-07-29 20:07:08,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3767.mp4 (确认存在: True)
2025-07-29 20:07:08,243 - INFO - 添加场景ID=3767，时长=1.40秒，累计时长=1.40秒
2025-07-29 20:07:08,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3768.mp4 (确认存在: True)
2025-07-29 20:07:08,243 - INFO - 添加场景ID=3768，时长=1.40秒，累计时长=2.80秒
2025-07-29 20:07:08,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3769.mp4 (确认存在: True)
2025-07-29 20:07:08,243 - INFO - 添加场景ID=3769，时长=1.84秒，累计时长=4.64秒
2025-07-29 20:07:08,243 - INFO - 准备合并 3 个场景文件，总时长约 4.64秒
2025-07-29 20:07:08,243 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3767.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3768.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3769.mp4'

2025-07-29 20:07:08,243 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuyrnx197\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuyrnx197\temp_combined.mp4
2025-07-29 20:07:08,374 - INFO - 合并后的视频时长: 4.71秒，目标音频时长: 3.99秒
2025-07-29 20:07:08,374 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuyrnx197\temp_combined.mp4 -ss 0 -to 3.994 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\79_1.mp4
2025-07-29 20:07:08,692 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:08,692 - INFO - 目标音频时长: 3.99秒
2025-07-29 20:07:08,692 - INFO - 实际视频时长: 4.02秒
2025-07-29 20:07:08,692 - INFO - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:07:08,692 - INFO - ==========================================
2025-07-29 20:07:08,692 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:08,692 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\79_1.mp4
2025-07-29 20:07:08,693 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuyrnx197
2025-07-29 20:07:08,739 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:08,739 - INFO -   - 音频时长: 3.99秒
2025-07-29 20:07:08,739 - INFO -   - 视频时长: 4.02秒
2025-07-29 20:07:08,739 - INFO -   - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:07:08,739 - INFO - 
----- 处理字幕 #79 的方案 #2 -----
2025-07-29 20:07:08,739 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\79_2.mp4
2025-07-29 20:07:08,740 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0270v_m0
2025-07-29 20:07:08,740 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3770.mp4 (确认存在: True)
2025-07-29 20:07:08,740 - INFO - 添加场景ID=3770，时长=4.44秒，累计时长=4.44秒
2025-07-29 20:07:08,741 - INFO - 准备合并 1 个场景文件，总时长约 4.44秒
2025-07-29 20:07:08,741 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3770.mp4'

2025-07-29 20:07:08,741 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0270v_m0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0270v_m0\temp_combined.mp4
2025-07-29 20:07:08,860 - INFO - 合并后的视频时长: 4.46秒，目标音频时长: 3.99秒
2025-07-29 20:07:08,860 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0270v_m0\temp_combined.mp4 -ss 0 -to 3.994 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\79_2.mp4
2025-07-29 20:07:09,160 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:09,160 - INFO - 目标音频时长: 3.99秒
2025-07-29 20:07:09,160 - INFO - 实际视频时长: 4.02秒
2025-07-29 20:07:09,160 - INFO - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:07:09,160 - INFO - ==========================================
2025-07-29 20:07:09,160 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:09,160 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\79_2.mp4
2025-07-29 20:07:09,160 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0270v_m0
2025-07-29 20:07:09,208 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:09,208 - INFO -   - 音频时长: 3.99秒
2025-07-29 20:07:09,208 - INFO -   - 视频时长: 4.02秒
2025-07-29 20:07:09,208 - INFO -   - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:07:09,208 - INFO - 
----- 处理字幕 #79 的方案 #3 -----
2025-07-29 20:07:09,208 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\79_3.mp4
2025-07-29 20:07:09,208 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqlvs_his
2025-07-29 20:07:09,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3767.mp4 (确认存在: True)
2025-07-29 20:07:09,209 - INFO - 添加场景ID=3767，时长=1.40秒，累计时长=1.40秒
2025-07-29 20:07:09,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3770.mp4 (确认存在: True)
2025-07-29 20:07:09,209 - INFO - 添加场景ID=3770，时长=4.44秒，累计时长=5.84秒
2025-07-29 20:07:09,209 - INFO - 准备合并 2 个场景文件，总时长约 5.84秒
2025-07-29 20:07:09,209 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3767.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3770.mp4'

2025-07-29 20:07:09,209 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqlvs_his\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqlvs_his\temp_combined.mp4
2025-07-29 20:07:09,338 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 3.99秒
2025-07-29 20:07:09,338 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqlvs_his\temp_combined.mp4 -ss 0 -to 3.994 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\79_3.mp4
2025-07-29 20:07:09,615 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:09,616 - INFO - 目标音频时长: 3.99秒
2025-07-29 20:07:09,616 - INFO - 实际视频时长: 4.02秒
2025-07-29 20:07:09,616 - INFO - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:07:09,616 - INFO - ==========================================
2025-07-29 20:07:09,616 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:09,616 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\79_3.mp4
2025-07-29 20:07:09,616 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqlvs_his
2025-07-29 20:07:09,661 - INFO - 方案 #3 处理完成:
2025-07-29 20:07:09,661 - INFO -   - 音频时长: 3.99秒
2025-07-29 20:07:09,661 - INFO -   - 视频时长: 4.02秒
2025-07-29 20:07:09,661 - INFO -   - 时长差异: 0.03秒 (0.73%)
2025-07-29 20:07:09,661 - INFO - 
字幕 #79 处理完成，成功生成 3/3 套方案
2025-07-29 20:07:09,661 - INFO - 生成的视频文件:
2025-07-29 20:07:09,661 - INFO -   1. F:/github/aicut_auto/newcut_ai\79_1.mp4
2025-07-29 20:07:09,661 - INFO -   2. F:/github/aicut_auto/newcut_ai\79_2.mp4
2025-07-29 20:07:09,662 - INFO -   3. F:/github/aicut_auto/newcut_ai\79_3.mp4
2025-07-29 20:07:09,662 - INFO - ========== 字幕 #79 处理结束 ==========

