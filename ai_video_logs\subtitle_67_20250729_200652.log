2025-07-29 20:06:52,169 - INFO - ========== 字幕 #67 处理开始 ==========
2025-07-29 20:06:52,169 - INFO - 字幕内容: 真相大白，全场哗然。原来男人就是那个凭一己之力将威尔集团推向世界之巅的天才！新欢被当场打脸，吓得魂不附体。
2025-07-29 20:06:52,169 - INFO - 字幕序号: [2622, 2628]
2025-07-29 20:06:52,169 - INFO - 音频文件详情:
2025-07-29 20:06:52,169 - INFO -   - 路径: output\67.wav
2025-07-29 20:06:52,170 - INFO -   - 时长: 8.80秒
2025-07-29 20:06:52,170 - INFO -   - 验证音频时长: 8.80秒
2025-07-29 20:06:52,179 - INFO - 字幕时间戳信息:
2025-07-29 20:06:52,179 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:52,179 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:52,179 - INFO -   - 根据生成的音频时长(8.80秒)已调整字幕时间戳
2025-07-29 20:06:52,179 - INFO - ========== 新模式：为字幕 #67 生成4套场景方案 ==========
2025-07-29 20:06:52,180 - INFO - 字幕序号列表: [2622, 2628]
2025-07-29 20:06:52,180 - INFO - 
--- 生成方案 #1：基于字幕序号 #2622 ---
2025-07-29 20:06:52,180 - INFO - 开始为单个字幕序号 #2622 匹配场景，目标时长: 8.80秒
2025-07-29 20:06:52,180 - INFO - 开始查找字幕序号 [2622] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:52,180 - INFO - 找到related_overlap场景: scene_id=2458, 字幕#2622
2025-07-29 20:06:52,181 - INFO - 字幕 #2622 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:52,181 - INFO - 字幕序号 #2622 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:52,181 - INFO - 选择第一个overlap场景作为起点: scene_id=2458
2025-07-29 20:06:52,181 - INFO - 添加起点场景: scene_id=2458, 时长=4.80秒, 累计时长=4.80秒
2025-07-29 20:06:52,181 - INFO - 起点场景时长不足，需要延伸填充 4.00秒
2025-07-29 20:06:52,181 - INFO - 起点场景在原始列表中的索引: 2457
2025-07-29 20:06:52,181 - INFO - 延伸添加场景: scene_id=2459 (完整时长 2.36秒)
2025-07-29 20:06:52,181 - INFO - 累计时长: 7.16秒
2025-07-29 20:06:52,181 - INFO - 延伸添加场景: scene_id=2460 (裁剪至 1.64秒)
2025-07-29 20:06:52,181 - INFO - 累计时长: 8.80秒
2025-07-29 20:06:52,181 - INFO - 字幕序号 #2622 场景匹配完成，共选择 3 个场景，总时长: 8.80秒
2025-07-29 20:06:52,181 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:52,181 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:52,181 - INFO - 
--- 生成方案 #2：基于字幕序号 #2628 ---
2025-07-29 20:06:52,181 - INFO - 开始为单个字幕序号 #2628 匹配场景，目标时长: 8.80秒
2025-07-29 20:06:52,181 - INFO - 开始查找字幕序号 [2628] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:52,182 - INFO - 找到related_overlap场景: scene_id=2464, 字幕#2628
2025-07-29 20:06:52,182 - INFO - 找到related_overlap场景: scene_id=2465, 字幕#2628
2025-07-29 20:06:52,182 - INFO - 找到related_between场景: scene_id=2463, 字幕#2628
2025-07-29 20:06:52,183 - INFO - 字幕 #2628 找到 2 个overlap场景, 1 个between场景
2025-07-29 20:06:52,183 - INFO - 字幕序号 #2628 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:52,183 - INFO - 选择第一个overlap场景作为起点: scene_id=2464
2025-07-29 20:06:52,183 - INFO - 添加起点场景: scene_id=2464, 时长=1.60秒, 累计时长=1.60秒
2025-07-29 20:06:52,183 - INFO - 起点场景时长不足，需要延伸填充 7.20秒
2025-07-29 20:06:52,183 - INFO - 起点场景在原始列表中的索引: 2463
2025-07-29 20:06:52,183 - INFO - 延伸添加场景: scene_id=2465 (完整时长 1.16秒)
2025-07-29 20:06:52,183 - INFO - 累计时长: 2.76秒
2025-07-29 20:06:52,183 - INFO - 延伸添加场景: scene_id=2466 (完整时长 2.64秒)
2025-07-29 20:06:52,183 - INFO - 累计时长: 5.40秒
2025-07-29 20:06:52,183 - INFO - 延伸添加场景: scene_id=2467 (完整时长 2.76秒)
2025-07-29 20:06:52,183 - INFO - 累计时长: 8.16秒
2025-07-29 20:06:52,183 - INFO - 延伸添加场景: scene_id=2468 (裁剪至 0.64秒)
2025-07-29 20:06:52,183 - INFO - 累计时长: 8.80秒
2025-07-29 20:06:52,183 - INFO - 字幕序号 #2628 场景匹配完成，共选择 5 个场景，总时长: 8.80秒
2025-07-29 20:06:52,183 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 20:06:52,183 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:52,183 - INFO - ========== 当前模式：为字幕 #67 生成 1 套场景方案 ==========
2025-07-29 20:06:52,183 - INFO - 开始查找字幕序号 [2622, 2628] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:52,183 - INFO - 找到related_overlap场景: scene_id=2458, 字幕#2622
2025-07-29 20:06:52,183 - INFO - 找到related_overlap场景: scene_id=2464, 字幕#2628
2025-07-29 20:06:52,183 - INFO - 找到related_overlap场景: scene_id=2465, 字幕#2628
2025-07-29 20:06:52,184 - INFO - 找到related_between场景: scene_id=2463, 字幕#2628
2025-07-29 20:06:52,184 - INFO - 字幕 #2622 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:52,184 - INFO - 字幕 #2628 找到 2 个overlap场景, 1 个between场景
2025-07-29 20:06:52,184 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:06:52,184 - INFO - 开始生成方案 #1
2025-07-29 20:06:52,184 - INFO - 方案 #1: 为字幕#2622选择初始化overlap场景id=2458
2025-07-29 20:06:52,184 - INFO - 方案 #1: 为字幕#2628选择初始化overlap场景id=2465
2025-07-29 20:06:52,184 - INFO - 方案 #1: 初始选择后，当前总时长=5.96秒
2025-07-29 20:06:52,184 - INFO - 方案 #1: 额外添加overlap场景id=2464, 当前总时长=7.56秒
2025-07-29 20:06:52,184 - INFO - 方案 #1: 额外between选择后，当前总时长=7.56秒
2025-07-29 20:06:52,184 - INFO - 方案 #1: 额外添加between场景id=2463, 当前总时长=8.88秒
2025-07-29 20:06:52,184 - INFO - 方案 #1: 场景总时长(8.88秒)大于音频时长(8.80秒)，需要裁剪
2025-07-29 20:06:52,184 - INFO - 调整前总时长: 8.88秒, 目标时长: 8.80秒
2025-07-29 20:06:52,184 - INFO - 需要裁剪 0.08秒
2025-07-29 20:06:52,184 - INFO - 裁剪最长场景ID=2458：从4.80秒裁剪至4.72秒
2025-07-29 20:06:52,184 - INFO - 调整后总时长: 8.80秒，与目标时长差异: 0.00秒
2025-07-29 20:06:52,184 - INFO - 方案 #1 调整/填充后最终总时长: 8.80秒
2025-07-29 20:06:52,184 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:52,184 - INFO - ========== 当前模式：字幕 #67 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:52,184 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:52,184 - INFO - ========== 新模式：字幕 #67 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:52,185 - INFO - 
----- 处理字幕 #67 的方案 #1 -----
2025-07-29 20:06:52,185 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 20:06:52,185 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpktkkqjm5
2025-07-29 20:06:52,185 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2458.mp4 (确认存在: True)
2025-07-29 20:06:52,185 - INFO - 添加场景ID=2458，时长=4.80秒，累计时长=4.80秒
2025-07-29 20:06:52,186 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2459.mp4 (确认存在: True)
2025-07-29 20:06:52,186 - INFO - 添加场景ID=2459，时长=2.36秒，累计时长=7.16秒
2025-07-29 20:06:52,186 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2460.mp4 (确认存在: True)
2025-07-29 20:06:52,186 - INFO - 添加场景ID=2460，时长=1.72秒，累计时长=8.88秒
2025-07-29 20:06:52,186 - INFO - 准备合并 3 个场景文件，总时长约 8.88秒
2025-07-29 20:06:52,186 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2458.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2459.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2460.mp4'

2025-07-29 20:06:52,186 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpktkkqjm5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpktkkqjm5\temp_combined.mp4
2025-07-29 20:06:52,319 - INFO - 合并后的视频时长: 8.95秒，目标音频时长: 8.80秒
2025-07-29 20:06:52,319 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpktkkqjm5\temp_combined.mp4 -ss 0 -to 8.797 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 20:06:52,721 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:52,721 - INFO - 目标音频时长: 8.80秒
2025-07-29 20:06:52,721 - INFO - 实际视频时长: 8.82秒
2025-07-29 20:06:52,721 - INFO - 时长差异: 0.03秒 (0.30%)
2025-07-29 20:06:52,721 - INFO - ==========================================
2025-07-29 20:06:52,721 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:52,721 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 20:06:52,722 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpktkkqjm5
2025-07-29 20:06:52,765 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:52,765 - INFO -   - 音频时长: 8.80秒
2025-07-29 20:06:52,766 - INFO -   - 视频时长: 8.82秒
2025-07-29 20:06:52,766 - INFO -   - 时长差异: 0.03秒 (0.30%)
2025-07-29 20:06:52,766 - INFO - 
----- 处理字幕 #67 的方案 #2 -----
2025-07-29 20:06:52,766 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 20:06:52,766 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd7di_gfb
2025-07-29 20:06:52,766 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2464.mp4 (确认存在: True)
2025-07-29 20:06:52,767 - INFO - 添加场景ID=2464，时长=1.60秒，累计时长=1.60秒
2025-07-29 20:06:52,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2465.mp4 (确认存在: True)
2025-07-29 20:06:52,767 - INFO - 添加场景ID=2465，时长=1.16秒，累计时长=2.76秒
2025-07-29 20:06:52,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2466.mp4 (确认存在: True)
2025-07-29 20:06:52,767 - INFO - 添加场景ID=2466，时长=2.64秒，累计时长=5.40秒
2025-07-29 20:06:52,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2467.mp4 (确认存在: True)
2025-07-29 20:06:52,767 - INFO - 添加场景ID=2467，时长=2.76秒，累计时长=8.16秒
2025-07-29 20:06:52,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2468.mp4 (确认存在: True)
2025-07-29 20:06:52,767 - INFO - 添加场景ID=2468，时长=3.44秒，累计时长=11.60秒
2025-07-29 20:06:52,767 - INFO - 准备合并 5 个场景文件，总时长约 11.60秒
2025-07-29 20:06:52,767 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2464.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2465.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2466.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2467.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2468.mp4'

2025-07-29 20:06:52,767 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpd7di_gfb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpd7di_gfb\temp_combined.mp4
2025-07-29 20:06:52,935 - INFO - 合并后的视频时长: 11.72秒，目标音频时长: 8.80秒
2025-07-29 20:06:52,935 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpd7di_gfb\temp_combined.mp4 -ss 0 -to 8.797 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 20:06:53,397 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:53,397 - INFO - 目标音频时长: 8.80秒
2025-07-29 20:06:53,397 - INFO - 实际视频时长: 8.82秒
2025-07-29 20:06:53,398 - INFO - 时长差异: 0.03秒 (0.30%)
2025-07-29 20:06:53,398 - INFO - ==========================================
2025-07-29 20:06:53,398 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:53,398 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 20:06:53,399 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd7di_gfb
2025-07-29 20:06:53,446 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:53,446 - INFO -   - 音频时长: 8.80秒
2025-07-29 20:06:53,446 - INFO -   - 视频时长: 8.82秒
2025-07-29 20:06:53,446 - INFO -   - 时长差异: 0.03秒 (0.30%)
2025-07-29 20:06:53,446 - INFO - 
----- 处理字幕 #67 的方案 #3 -----
2025-07-29 20:06:53,446 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 20:06:53,446 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5be7ps1g
2025-07-29 20:06:53,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2458.mp4 (确认存在: True)
2025-07-29 20:06:53,447 - INFO - 添加场景ID=2458，时长=4.80秒，累计时长=4.80秒
2025-07-29 20:06:53,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2465.mp4 (确认存在: True)
2025-07-29 20:06:53,447 - INFO - 添加场景ID=2465，时长=1.16秒，累计时长=5.96秒
2025-07-29 20:06:53,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2464.mp4 (确认存在: True)
2025-07-29 20:06:53,447 - INFO - 添加场景ID=2464，时长=1.60秒，累计时长=7.56秒
2025-07-29 20:06:53,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2463.mp4 (确认存在: True)
2025-07-29 20:06:53,447 - INFO - 添加场景ID=2463，时长=1.32秒，累计时长=8.88秒
2025-07-29 20:06:53,447 - INFO - 准备合并 4 个场景文件，总时长约 8.88秒
2025-07-29 20:06:53,447 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2458.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2465.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2464.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2463.mp4'

2025-07-29 20:06:53,447 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5be7ps1g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5be7ps1g\temp_combined.mp4
2025-07-29 20:06:53,605 - INFO - 合并后的视频时长: 8.97秒，目标音频时长: 8.80秒
2025-07-29 20:06:53,605 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5be7ps1g\temp_combined.mp4 -ss 0 -to 8.797 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 20:06:54,006 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:54,007 - INFO - 目标音频时长: 8.80秒
2025-07-29 20:06:54,007 - INFO - 实际视频时长: 8.82秒
2025-07-29 20:06:54,007 - INFO - 时长差异: 0.03秒 (0.30%)
2025-07-29 20:06:54,007 - INFO - ==========================================
2025-07-29 20:06:54,007 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:54,007 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 20:06:54,007 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5be7ps1g
2025-07-29 20:06:54,052 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:54,052 - INFO -   - 音频时长: 8.80秒
2025-07-29 20:06:54,052 - INFO -   - 视频时长: 8.82秒
2025-07-29 20:06:54,052 - INFO -   - 时长差异: 0.03秒 (0.30%)
2025-07-29 20:06:54,052 - INFO - 
字幕 #67 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:54,052 - INFO - 生成的视频文件:
2025-07-29 20:06:54,052 - INFO -   1. F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 20:06:54,052 - INFO -   2. F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 20:06:54,052 - INFO -   3. F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 20:06:54,052 - INFO - ========== 字幕 #67 处理结束 ==========

