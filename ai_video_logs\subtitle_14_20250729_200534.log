2025-07-29 20:05:34,768 - INFO - ========== 字幕 #14 处理开始 ==========
2025-07-29 20:05:34,769 - INFO - 字幕内容: 男人走投无路，拼尽全力凑到钱赶回医院，却只得到了一个让他心碎的消息：他来迟了。
2025-07-29 20:05:34,769 - INFO - 字幕序号: [106, 111]
2025-07-29 20:05:34,769 - INFO - 音频文件详情:
2025-07-29 20:05:34,769 - INFO -   - 路径: output\14.wav
2025-07-29 20:05:34,769 - INFO -   - 时长: 5.88秒
2025-07-29 20:05:34,769 - INFO -   - 验证音频时长: 5.88秒
2025-07-29 20:05:34,769 - INFO - 字幕时间戳信息:
2025-07-29 20:05:34,769 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:34,769 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:34,769 - INFO -   - 根据生成的音频时长(5.88秒)已调整字幕时间戳
2025-07-29 20:05:34,769 - INFO - ========== 新模式：为字幕 #14 生成4套场景方案 ==========
2025-07-29 20:05:34,769 - INFO - 字幕序号列表: [106, 111]
2025-07-29 20:05:34,769 - INFO - 
--- 生成方案 #1：基于字幕序号 #106 ---
2025-07-29 20:05:34,769 - INFO - 开始为单个字幕序号 #106 匹配场景，目标时长: 5.88秒
2025-07-29 20:05:34,769 - INFO - 开始查找字幕序号 [106] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:34,769 - INFO - 找到related_overlap场景: scene_id=125, 字幕#106
2025-07-29 20:05:34,770 - INFO - 字幕 #106 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:34,770 - INFO - 字幕序号 #106 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:34,770 - INFO - 选择第一个overlap场景作为起点: scene_id=125
2025-07-29 20:05:34,770 - INFO - 添加起点场景: scene_id=125, 时长=1.80秒, 累计时长=1.80秒
2025-07-29 20:05:34,771 - INFO - 起点场景时长不足，需要延伸填充 4.08秒
2025-07-29 20:05:34,771 - INFO - 起点场景在原始列表中的索引: 124
2025-07-29 20:05:34,771 - INFO - 延伸添加场景: scene_id=126 (完整时长 0.92秒)
2025-07-29 20:05:34,771 - INFO - 累计时长: 2.72秒
2025-07-29 20:05:34,771 - INFO - 延伸添加场景: scene_id=127 (完整时长 0.76秒)
2025-07-29 20:05:34,771 - INFO - 累计时长: 3.48秒
2025-07-29 20:05:34,771 - INFO - 延伸添加场景: scene_id=128 (完整时长 0.44秒)
2025-07-29 20:05:34,771 - INFO - 累计时长: 3.92秒
2025-07-29 20:05:34,771 - INFO - 延伸添加场景: scene_id=129 (裁剪至 1.96秒)
2025-07-29 20:05:34,771 - INFO - 累计时长: 5.88秒
2025-07-29 20:05:34,771 - INFO - 字幕序号 #106 场景匹配完成，共选择 5 个场景，总时长: 5.88秒
2025-07-29 20:05:34,771 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:34,771 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:34,771 - INFO - 
--- 生成方案 #2：基于字幕序号 #111 ---
2025-07-29 20:05:34,771 - INFO - 开始为单个字幕序号 #111 匹配场景，目标时长: 5.88秒
2025-07-29 20:05:34,771 - INFO - 开始查找字幕序号 [111] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:34,771 - INFO - 找到related_overlap场景: scene_id=130, 字幕#111
2025-07-29 20:05:34,771 - INFO - 找到related_overlap场景: scene_id=131, 字幕#111
2025-07-29 20:05:34,772 - INFO - 字幕 #111 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:34,772 - INFO - 字幕序号 #111 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:34,772 - INFO - 选择第一个overlap场景作为起点: scene_id=130
2025-07-29 20:05:34,772 - INFO - 添加起点场景: scene_id=130, 时长=1.12秒, 累计时长=1.12秒
2025-07-29 20:05:34,772 - INFO - 起点场景时长不足，需要延伸填充 4.75秒
2025-07-29 20:05:34,772 - INFO - 起点场景在原始列表中的索引: 129
2025-07-29 20:05:34,772 - INFO - 延伸添加场景: scene_id=131 (完整时长 0.96秒)
2025-07-29 20:05:34,772 - INFO - 累计时长: 2.08秒
2025-07-29 20:05:34,772 - INFO - 延伸添加场景: scene_id=132 (完整时长 0.84秒)
2025-07-29 20:05:34,772 - INFO - 累计时长: 2.92秒
2025-07-29 20:05:34,772 - INFO - 延伸添加场景: scene_id=133 (完整时长 1.72秒)
2025-07-29 20:05:34,772 - INFO - 累计时长: 4.64秒
2025-07-29 20:05:34,772 - INFO - 延伸添加场景: scene_id=134 (裁剪至 1.24秒)
2025-07-29 20:05:34,772 - INFO - 累计时长: 5.88秒
2025-07-29 20:05:34,772 - INFO - 字幕序号 #111 场景匹配完成，共选择 5 个场景，总时长: 5.88秒
2025-07-29 20:05:34,772 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 20:05:34,772 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:34,772 - INFO - ========== 当前模式：为字幕 #14 生成 1 套场景方案 ==========
2025-07-29 20:05:34,772 - INFO - 开始查找字幕序号 [106, 111] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:34,772 - INFO - 找到related_overlap场景: scene_id=125, 字幕#106
2025-07-29 20:05:34,772 - INFO - 找到related_overlap场景: scene_id=130, 字幕#111
2025-07-29 20:05:34,772 - INFO - 找到related_overlap场景: scene_id=131, 字幕#111
2025-07-29 20:05:34,773 - INFO - 字幕 #106 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:34,773 - INFO - 字幕 #111 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:34,773 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:34,773 - INFO - 开始生成方案 #1
2025-07-29 20:05:34,773 - INFO - 方案 #1: 为字幕#106选择初始化overlap场景id=125
2025-07-29 20:05:34,773 - INFO - 方案 #1: 为字幕#111选择初始化overlap场景id=130
2025-07-29 20:05:34,773 - INFO - 方案 #1: 初始选择后，当前总时长=2.92秒
2025-07-29 20:05:34,774 - INFO - 方案 #1: 额外添加overlap场景id=131, 当前总时长=3.88秒
2025-07-29 20:05:34,774 - INFO - 方案 #1: 额外between选择后，当前总时长=3.88秒
2025-07-29 20:05:34,774 - INFO - 方案 #1: 场景总时长(3.88秒)小于音频时长(5.88秒)，需要延伸填充
2025-07-29 20:05:34,774 - INFO - 方案 #1: 最后一个场景ID: 131
2025-07-29 20:05:34,774 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 130
2025-07-29 20:05:34,774 - INFO - 方案 #1: 需要填充时长: 2.00秒
2025-07-29 20:05:34,774 - INFO - 方案 #1: 追加场景 scene_id=132 (完整时长 0.84秒)
2025-07-29 20:05:34,774 - INFO - 方案 #1: 追加场景 scene_id=133 (裁剪至 1.16秒)
2025-07-29 20:05:34,774 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:34,774 - INFO - 方案 #1 调整/填充后最终总时长: 5.88秒
2025-07-29 20:05:34,774 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:34,774 - INFO - ========== 当前模式：字幕 #14 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:34,774 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:34,774 - INFO - ========== 新模式：字幕 #14 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:34,774 - INFO - 
----- 处理字幕 #14 的方案 #1 -----
2025-07-29 20:05:34,774 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 20:05:34,774 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp13v0_gay
2025-07-29 20:05:34,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\125.mp4 (确认存在: True)
2025-07-29 20:05:34,775 - INFO - 添加场景ID=125，时长=1.80秒，累计时长=1.80秒
2025-07-29 20:05:34,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\126.mp4 (确认存在: True)
2025-07-29 20:05:34,775 - INFO - 添加场景ID=126，时长=0.92秒，累计时长=2.72秒
2025-07-29 20:05:34,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\127.mp4 (确认存在: True)
2025-07-29 20:05:34,775 - INFO - 添加场景ID=127，时长=0.76秒，累计时长=3.48秒
2025-07-29 20:05:34,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\128.mp4 (确认存在: True)
2025-07-29 20:05:34,775 - INFO - 添加场景ID=128，时长=0.44秒，累计时长=3.92秒
2025-07-29 20:05:34,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\129.mp4 (确认存在: True)
2025-07-29 20:05:34,775 - INFO - 添加场景ID=129，时长=2.24秒，累计时长=6.16秒
2025-07-29 20:05:34,775 - INFO - 准备合并 5 个场景文件，总时长约 6.16秒
2025-07-29 20:05:34,775 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/125.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/126.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/127.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/128.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/129.mp4'

2025-07-29 20:05:34,775 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp13v0_gay\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp13v0_gay\temp_combined.mp4
2025-07-29 20:05:34,964 - INFO - 合并后的视频时长: 6.28秒，目标音频时长: 5.88秒
2025-07-29 20:05:34,964 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp13v0_gay\temp_combined.mp4 -ss 0 -to 5.875 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 20:05:35,341 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:35,341 - INFO - 目标音频时长: 5.88秒
2025-07-29 20:05:35,341 - INFO - 实际视频时长: 5.90秒
2025-07-29 20:05:35,341 - INFO - 时长差异: 0.03秒 (0.48%)
2025-07-29 20:05:35,341 - INFO - ==========================================
2025-07-29 20:05:35,341 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:35,341 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 20:05:35,342 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp13v0_gay
2025-07-29 20:05:35,394 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:35,394 - INFO -   - 音频时长: 5.88秒
2025-07-29 20:05:35,394 - INFO -   - 视频时长: 5.90秒
2025-07-29 20:05:35,394 - INFO -   - 时长差异: 0.03秒 (0.48%)
2025-07-29 20:05:35,394 - INFO - 
----- 处理字幕 #14 的方案 #2 -----
2025-07-29 20:05:35,394 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 20:05:35,394 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqvwsybpa
2025-07-29 20:05:35,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\130.mp4 (确认存在: True)
2025-07-29 20:05:35,395 - INFO - 添加场景ID=130，时长=1.12秒，累计时长=1.12秒
2025-07-29 20:05:35,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-07-29 20:05:35,395 - INFO - 添加场景ID=131，时长=0.96秒，累计时长=2.08秒
2025-07-29 20:05:35,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\132.mp4 (确认存在: True)
2025-07-29 20:05:35,395 - INFO - 添加场景ID=132，时长=0.84秒，累计时长=2.92秒
2025-07-29 20:05:35,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\133.mp4 (确认存在: True)
2025-07-29 20:05:35,395 - INFO - 添加场景ID=133，时长=1.72秒，累计时长=4.64秒
2025-07-29 20:05:35,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\134.mp4 (确认存在: True)
2025-07-29 20:05:35,395 - INFO - 添加场景ID=134，时长=1.88秒，累计时长=6.52秒
2025-07-29 20:05:35,396 - INFO - 准备合并 5 个场景文件，总时长约 6.52秒
2025-07-29 20:05:35,396 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/130.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/132.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/133.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/134.mp4'

2025-07-29 20:05:35,396 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqvwsybpa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqvwsybpa\temp_combined.mp4
2025-07-29 20:05:35,567 - INFO - 合并后的视频时长: 6.64秒，目标音频时长: 5.88秒
2025-07-29 20:05:35,567 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqvwsybpa\temp_combined.mp4 -ss 0 -to 5.875 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 20:05:35,930 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:35,930 - INFO - 目标音频时长: 5.88秒
2025-07-29 20:05:35,930 - INFO - 实际视频时长: 5.90秒
2025-07-29 20:05:35,930 - INFO - 时长差异: 0.03秒 (0.48%)
2025-07-29 20:05:35,930 - INFO - ==========================================
2025-07-29 20:05:35,930 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:35,930 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 20:05:35,932 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqvwsybpa
2025-07-29 20:05:35,983 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:35,983 - INFO -   - 音频时长: 5.88秒
2025-07-29 20:05:35,983 - INFO -   - 视频时长: 5.90秒
2025-07-29 20:05:35,983 - INFO -   - 时长差异: 0.03秒 (0.48%)
2025-07-29 20:05:35,983 - INFO - 
----- 处理字幕 #14 的方案 #3 -----
2025-07-29 20:05:35,983 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 20:05:35,984 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpb_7uroum
2025-07-29 20:05:35,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\125.mp4 (确认存在: True)
2025-07-29 20:05:35,984 - INFO - 添加场景ID=125，时长=1.80秒，累计时长=1.80秒
2025-07-29 20:05:35,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\130.mp4 (确认存在: True)
2025-07-29 20:05:35,984 - INFO - 添加场景ID=130，时长=1.12秒，累计时长=2.92秒
2025-07-29 20:05:35,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-07-29 20:05:35,984 - INFO - 添加场景ID=131，时长=0.96秒，累计时长=3.88秒
2025-07-29 20:05:35,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\132.mp4 (确认存在: True)
2025-07-29 20:05:35,984 - INFO - 添加场景ID=132，时长=0.84秒，累计时长=4.72秒
2025-07-29 20:05:35,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\133.mp4 (确认存在: True)
2025-07-29 20:05:35,984 - INFO - 添加场景ID=133，时长=1.72秒，累计时长=6.44秒
2025-07-29 20:05:35,985 - INFO - 准备合并 5 个场景文件，总时长约 6.44秒
2025-07-29 20:05:35,985 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/125.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/130.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/132.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/133.mp4'

2025-07-29 20:05:35,985 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpb_7uroum\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpb_7uroum\temp_combined.mp4
2025-07-29 20:05:36,184 - INFO - 合并后的视频时长: 6.56秒，目标音频时长: 5.88秒
2025-07-29 20:05:36,184 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpb_7uroum\temp_combined.mp4 -ss 0 -to 5.875 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 20:05:36,554 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:36,554 - INFO - 目标音频时长: 5.88秒
2025-07-29 20:05:36,554 - INFO - 实际视频时长: 5.90秒
2025-07-29 20:05:36,554 - INFO - 时长差异: 0.03秒 (0.48%)
2025-07-29 20:05:36,554 - INFO - ==========================================
2025-07-29 20:05:36,554 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:36,554 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 20:05:36,555 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpb_7uroum
2025-07-29 20:05:36,602 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:36,602 - INFO -   - 音频时长: 5.88秒
2025-07-29 20:05:36,602 - INFO -   - 视频时长: 5.90秒
2025-07-29 20:05:36,602 - INFO -   - 时长差异: 0.03秒 (0.48%)
2025-07-29 20:05:36,602 - INFO - 
字幕 #14 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:36,602 - INFO - 生成的视频文件:
2025-07-29 20:05:36,602 - INFO -   1. F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 20:05:36,602 - INFO -   2. F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 20:05:36,602 - INFO -   3. F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 20:05:36,602 - INFO - ========== 字幕 #14 处理结束 ==========

