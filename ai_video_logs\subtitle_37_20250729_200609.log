2025-07-29 20:06:09,573 - INFO - ========== 字幕 #37 处理开始 ==========
2025-07-29 20:06:09,574 - INFO - 字幕内容: 最终，一个知情人告诉了她一个晴天霹雳的消息：那个叫江安安的小姑娘，一个星期前就死了。
2025-07-29 20:06:09,574 - INFO - 字幕序号: [615, 619]
2025-07-29 20:06:09,574 - INFO - 音频文件详情:
2025-07-29 20:06:09,574 - INFO -   - 路径: output\37.wav
2025-07-29 20:06:09,574 - INFO -   - 时长: 4.38秒
2025-07-29 20:06:09,574 - INFO -   - 验证音频时长: 4.38秒
2025-07-29 20:06:09,574 - INFO - 字幕时间戳信息:
2025-07-29 20:06:09,583 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:09,583 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:09,583 - INFO -   - 根据生成的音频时长(4.38秒)已调整字幕时间戳
2025-07-29 20:06:09,583 - INFO - ========== 新模式：为字幕 #37 生成4套场景方案 ==========
2025-07-29 20:06:09,583 - INFO - 字幕序号列表: [615, 619]
2025-07-29 20:06:09,583 - INFO - 
--- 生成方案 #1：基于字幕序号 #615 ---
2025-07-29 20:06:09,583 - INFO - 开始为单个字幕序号 #615 匹配场景，目标时长: 4.38秒
2025-07-29 20:06:09,583 - INFO - 开始查找字幕序号 [615] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:09,584 - INFO - 找到related_overlap场景: scene_id=664, 字幕#615
2025-07-29 20:06:09,584 - INFO - 找到related_overlap场景: scene_id=665, 字幕#615
2025-07-29 20:06:09,585 - INFO - 字幕 #615 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:09,585 - INFO - 字幕序号 #615 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:09,585 - INFO - 选择第一个overlap场景作为起点: scene_id=664
2025-07-29 20:06:09,585 - INFO - 添加起点场景: scene_id=664, 时长=1.32秒, 累计时长=1.32秒
2025-07-29 20:06:09,585 - INFO - 起点场景时长不足，需要延伸填充 3.06秒
2025-07-29 20:06:09,585 - INFO - 起点场景在原始列表中的索引: 663
2025-07-29 20:06:09,585 - INFO - 延伸添加场景: scene_id=665 (完整时长 2.60秒)
2025-07-29 20:06:09,585 - INFO - 累计时长: 3.92秒
2025-07-29 20:06:09,585 - INFO - 延伸添加场景: scene_id=666 (裁剪至 0.46秒)
2025-07-29 20:06:09,585 - INFO - 累计时长: 4.38秒
2025-07-29 20:06:09,585 - INFO - 字幕序号 #615 场景匹配完成，共选择 3 个场景，总时长: 4.38秒
2025-07-29 20:06:09,585 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:09,585 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:09,585 - INFO - 
--- 生成方案 #2：基于字幕序号 #619 ---
2025-07-29 20:06:09,585 - INFO - 开始为单个字幕序号 #619 匹配场景，目标时长: 4.38秒
2025-07-29 20:06:09,585 - INFO - 开始查找字幕序号 [619] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:09,586 - INFO - 找到related_overlap场景: scene_id=668, 字幕#619
2025-07-29 20:06:09,587 - INFO - 字幕 #619 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:09,587 - INFO - 字幕序号 #619 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:09,587 - INFO - 选择第一个overlap场景作为起点: scene_id=668
2025-07-29 20:06:09,587 - INFO - 添加起点场景: scene_id=668, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 20:06:09,587 - INFO - 起点场景时长不足，需要延伸填充 2.66秒
2025-07-29 20:06:09,587 - INFO - 起点场景在原始列表中的索引: 667
2025-07-29 20:06:09,587 - INFO - 延伸添加场景: scene_id=669 (完整时长 2.20秒)
2025-07-29 20:06:09,587 - INFO - 累计时长: 3.92秒
2025-07-29 20:06:09,587 - INFO - 延伸添加场景: scene_id=670 (裁剪至 0.46秒)
2025-07-29 20:06:09,587 - INFO - 累计时长: 4.38秒
2025-07-29 20:06:09,587 - INFO - 字幕序号 #619 场景匹配完成，共选择 3 个场景，总时长: 4.38秒
2025-07-29 20:06:09,587 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:06:09,587 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:09,587 - INFO - ========== 当前模式：为字幕 #37 生成 1 套场景方案 ==========
2025-07-29 20:06:09,587 - INFO - 开始查找字幕序号 [615, 619] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:09,587 - INFO - 找到related_overlap场景: scene_id=664, 字幕#615
2025-07-29 20:06:09,587 - INFO - 找到related_overlap场景: scene_id=665, 字幕#615
2025-07-29 20:06:09,587 - INFO - 找到related_overlap场景: scene_id=668, 字幕#619
2025-07-29 20:06:09,589 - INFO - 字幕 #615 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:09,589 - INFO - 字幕 #619 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:09,589 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:09,589 - INFO - 开始生成方案 #1
2025-07-29 20:06:09,589 - INFO - 方案 #1: 为字幕#615选择初始化overlap场景id=665
2025-07-29 20:06:09,589 - INFO - 方案 #1: 为字幕#619选择初始化overlap场景id=668
2025-07-29 20:06:09,589 - INFO - 方案 #1: 初始选择后，当前总时长=4.32秒
2025-07-29 20:06:09,589 - INFO - 方案 #1: 额外添加overlap场景id=664, 当前总时长=5.64秒
2025-07-29 20:06:09,589 - INFO - 方案 #1: 额外between选择后，当前总时长=5.64秒
2025-07-29 20:06:09,589 - INFO - 方案 #1: 场景总时长(5.64秒)大于音频时长(4.38秒)，需要裁剪
2025-07-29 20:06:09,589 - INFO - 调整前总时长: 5.64秒, 目标时长: 4.38秒
2025-07-29 20:06:09,589 - INFO - 需要裁剪 1.26秒
2025-07-29 20:06:09,589 - INFO - 裁剪最长场景ID=665：从2.60秒裁剪至1.34秒
2025-07-29 20:06:09,589 - INFO - 调整后总时长: 4.38秒，与目标时长差异: 0.00秒
2025-07-29 20:06:09,589 - INFO - 方案 #1 调整/填充后最终总时长: 4.38秒
2025-07-29 20:06:09,589 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:09,589 - INFO - ========== 当前模式：字幕 #37 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:09,589 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:09,589 - INFO - ========== 新模式：字幕 #37 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:09,589 - INFO - 
----- 处理字幕 #37 的方案 #1 -----
2025-07-29 20:06:09,589 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 20:06:09,590 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpblbepagx
2025-07-29 20:06:09,590 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\664.mp4 (确认存在: True)
2025-07-29 20:06:09,591 - INFO - 添加场景ID=664，时长=1.32秒，累计时长=1.32秒
2025-07-29 20:06:09,591 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\665.mp4 (确认存在: True)
2025-07-29 20:06:09,591 - INFO - 添加场景ID=665，时长=2.60秒，累计时长=3.92秒
2025-07-29 20:06:09,591 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\666.mp4 (确认存在: True)
2025-07-29 20:06:09,591 - INFO - 添加场景ID=666，时长=2.36秒，累计时长=6.28秒
2025-07-29 20:06:09,591 - INFO - 准备合并 3 个场景文件，总时长约 6.28秒
2025-07-29 20:06:09,591 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/664.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/665.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/666.mp4'

2025-07-29 20:06:09,591 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpblbepagx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpblbepagx\temp_combined.mp4
2025-07-29 20:06:09,717 - INFO - 合并后的视频时长: 6.35秒，目标音频时长: 4.38秒
2025-07-29 20:06:09,717 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpblbepagx\temp_combined.mp4 -ss 0 -to 4.376 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 20:06:10,020 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:10,020 - INFO - 目标音频时长: 4.38秒
2025-07-29 20:06:10,020 - INFO - 实际视频时长: 4.42秒
2025-07-29 20:06:10,020 - INFO - 时长差异: 0.05秒 (1.07%)
2025-07-29 20:06:10,020 - INFO - ==========================================
2025-07-29 20:06:10,020 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:10,020 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 20:06:10,021 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpblbepagx
2025-07-29 20:06:10,066 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:10,066 - INFO -   - 音频时长: 4.38秒
2025-07-29 20:06:10,066 - INFO -   - 视频时长: 4.42秒
2025-07-29 20:06:10,066 - INFO -   - 时长差异: 0.05秒 (1.07%)
2025-07-29 20:06:10,066 - INFO - 
----- 处理字幕 #37 的方案 #2 -----
2025-07-29 20:06:10,066 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 20:06:10,067 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwq4yhu54
2025-07-29 20:06:10,067 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\668.mp4 (确认存在: True)
2025-07-29 20:06:10,067 - INFO - 添加场景ID=668，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:06:10,067 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\669.mp4 (确认存在: True)
2025-07-29 20:06:10,067 - INFO - 添加场景ID=669，时长=2.20秒，累计时长=3.92秒
2025-07-29 20:06:10,067 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\670.mp4 (确认存在: True)
2025-07-29 20:06:10,067 - INFO - 添加场景ID=670，时长=1.00秒，累计时长=4.92秒
2025-07-29 20:06:10,067 - INFO - 准备合并 3 个场景文件，总时长约 4.92秒
2025-07-29 20:06:10,067 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/668.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/669.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/670.mp4'

2025-07-29 20:06:10,068 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwq4yhu54\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwq4yhu54\temp_combined.mp4
2025-07-29 20:06:10,199 - INFO - 合并后的视频时长: 4.99秒，目标音频时长: 4.38秒
2025-07-29 20:06:10,199 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwq4yhu54\temp_combined.mp4 -ss 0 -to 4.376 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 20:06:10,493 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:10,493 - INFO - 目标音频时长: 4.38秒
2025-07-29 20:06:10,493 - INFO - 实际视频时长: 4.42秒
2025-07-29 20:06:10,493 - INFO - 时长差异: 0.05秒 (1.07%)
2025-07-29 20:06:10,493 - INFO - ==========================================
2025-07-29 20:06:10,493 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:10,493 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 20:06:10,494 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwq4yhu54
2025-07-29 20:06:10,540 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:10,540 - INFO -   - 音频时长: 4.38秒
2025-07-29 20:06:10,540 - INFO -   - 视频时长: 4.42秒
2025-07-29 20:06:10,540 - INFO -   - 时长差异: 0.05秒 (1.07%)
2025-07-29 20:06:10,540 - INFO - 
----- 处理字幕 #37 的方案 #3 -----
2025-07-29 20:06:10,541 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 20:06:10,541 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptpslrc55
2025-07-29 20:06:10,541 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\665.mp4 (确认存在: True)
2025-07-29 20:06:10,541 - INFO - 添加场景ID=665，时长=2.60秒，累计时长=2.60秒
2025-07-29 20:06:10,542 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\668.mp4 (确认存在: True)
2025-07-29 20:06:10,542 - INFO - 添加场景ID=668，时长=1.72秒，累计时长=4.32秒
2025-07-29 20:06:10,542 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\664.mp4 (确认存在: True)
2025-07-29 20:06:10,542 - INFO - 添加场景ID=664，时长=1.32秒，累计时长=5.64秒
2025-07-29 20:06:10,542 - INFO - 准备合并 3 个场景文件，总时长约 5.64秒
2025-07-29 20:06:10,542 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/665.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/668.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/664.mp4'

2025-07-29 20:06:10,542 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptpslrc55\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptpslrc55\temp_combined.mp4
2025-07-29 20:06:10,687 - INFO - 合并后的视频时长: 5.71秒，目标音频时长: 4.38秒
2025-07-29 20:06:10,689 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptpslrc55\temp_combined.mp4 -ss 0 -to 4.376 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 20:06:10,999 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:10,999 - INFO - 目标音频时长: 4.38秒
2025-07-29 20:06:10,999 - INFO - 实际视频时长: 4.42秒
2025-07-29 20:06:10,999 - INFO - 时长差异: 0.05秒 (1.07%)
2025-07-29 20:06:10,999 - INFO - ==========================================
2025-07-29 20:06:10,999 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:10,999 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 20:06:10,999 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptpslrc55
2025-07-29 20:06:11,047 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:11,047 - INFO -   - 音频时长: 4.38秒
2025-07-29 20:06:11,047 - INFO -   - 视频时长: 4.42秒
2025-07-29 20:06:11,047 - INFO -   - 时长差异: 0.05秒 (1.07%)
2025-07-29 20:06:11,047 - INFO - 
字幕 #37 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:11,047 - INFO - 生成的视频文件:
2025-07-29 20:06:11,047 - INFO -   1. F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 20:06:11,047 - INFO -   2. F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 20:06:11,047 - INFO -   3. F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 20:06:11,049 - INFO - ========== 字幕 #37 处理结束 ==========

