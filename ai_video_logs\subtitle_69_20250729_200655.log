2025-07-29 20:06:55,786 - INFO - ========== 字幕 #69 处理开始 ==========
2025-07-29 20:06:55,786 - INFO - 字幕内容: 女人不甘心，她追着男人，质问他是不是还爱着自己，想用安安来挽回他。
2025-07-29 20:06:55,786 - INFO - 字幕序号: [2800, 2804]
2025-07-29 20:06:55,786 - INFO - 音频文件详情:
2025-07-29 20:06:55,786 - INFO -   - 路径: output\69.wav
2025-07-29 20:06:55,786 - INFO -   - 时长: 4.96秒
2025-07-29 20:06:55,787 - INFO -   - 验证音频时长: 4.96秒
2025-07-29 20:06:55,787 - INFO - 字幕时间戳信息:
2025-07-29 20:06:55,796 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:55,796 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:55,796 - INFO -   - 根据生成的音频时长(4.96秒)已调整字幕时间戳
2025-07-29 20:06:55,796 - INFO - ========== 新模式：为字幕 #69 生成4套场景方案 ==========
2025-07-29 20:06:55,796 - INFO - 字幕序号列表: [2800, 2804]
2025-07-29 20:06:55,796 - INFO - 
--- 生成方案 #1：基于字幕序号 #2800 ---
2025-07-29 20:06:55,796 - INFO - 开始为单个字幕序号 #2800 匹配场景，目标时长: 4.96秒
2025-07-29 20:06:55,796 - INFO - 开始查找字幕序号 [2800] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:55,796 - INFO - 找到related_overlap场景: scene_id=2625, 字幕#2800
2025-07-29 20:06:55,796 - INFO - 找到related_overlap场景: scene_id=2626, 字幕#2800
2025-07-29 20:06:55,798 - INFO - 字幕 #2800 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:55,798 - INFO - 字幕序号 #2800 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:55,798 - INFO - 选择第一个overlap场景作为起点: scene_id=2625
2025-07-29 20:06:55,798 - INFO - 添加起点场景: scene_id=2625, 时长=1.92秒, 累计时长=1.92秒
2025-07-29 20:06:55,798 - INFO - 起点场景时长不足，需要延伸填充 3.04秒
2025-07-29 20:06:55,798 - INFO - 起点场景在原始列表中的索引: 2624
2025-07-29 20:06:55,798 - INFO - 延伸添加场景: scene_id=2626 (完整时长 1.72秒)
2025-07-29 20:06:55,798 - INFO - 累计时长: 3.64秒
2025-07-29 20:06:55,798 - INFO - 延伸添加场景: scene_id=2627 (裁剪至 1.32秒)
2025-07-29 20:06:55,798 - INFO - 累计时长: 4.96秒
2025-07-29 20:06:55,798 - INFO - 字幕序号 #2800 场景匹配完成，共选择 3 个场景，总时长: 4.96秒
2025-07-29 20:06:55,798 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:55,798 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:55,798 - INFO - 
--- 生成方案 #2：基于字幕序号 #2804 ---
2025-07-29 20:06:55,798 - INFO - 开始为单个字幕序号 #2804 匹配场景，目标时长: 4.96秒
2025-07-29 20:06:55,798 - INFO - 开始查找字幕序号 [2804] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:55,798 - INFO - 找到related_overlap场景: scene_id=2628, 字幕#2804
2025-07-29 20:06:55,799 - INFO - 字幕 #2804 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:55,799 - INFO - 字幕序号 #2804 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:55,799 - INFO - 选择第一个overlap场景作为起点: scene_id=2628
2025-07-29 20:06:55,799 - INFO - 添加起点场景: scene_id=2628, 时长=1.20秒, 累计时长=1.20秒
2025-07-29 20:06:55,799 - INFO - 起点场景时长不足，需要延伸填充 3.76秒
2025-07-29 20:06:55,799 - INFO - 起点场景在原始列表中的索引: 2627
2025-07-29 20:06:55,799 - INFO - 延伸添加场景: scene_id=2629 (完整时长 1.40秒)
2025-07-29 20:06:55,799 - INFO - 累计时长: 2.60秒
2025-07-29 20:06:55,799 - INFO - 延伸添加场景: scene_id=2630 (完整时长 0.92秒)
2025-07-29 20:06:55,799 - INFO - 累计时长: 3.52秒
2025-07-29 20:06:55,799 - INFO - 延伸添加场景: scene_id=2631 (完整时长 1.16秒)
2025-07-29 20:06:55,799 - INFO - 累计时长: 4.68秒
2025-07-29 20:06:55,799 - INFO - 延伸添加场景: scene_id=2632 (裁剪至 0.29秒)
2025-07-29 20:06:55,799 - INFO - 累计时长: 4.96秒
2025-07-29 20:06:55,799 - INFO - 字幕序号 #2804 场景匹配完成，共选择 5 个场景，总时长: 4.96秒
2025-07-29 20:06:55,800 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 20:06:55,800 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:55,800 - INFO - ========== 当前模式：为字幕 #69 生成 1 套场景方案 ==========
2025-07-29 20:06:55,800 - INFO - 开始查找字幕序号 [2800, 2804] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:55,800 - INFO - 找到related_overlap场景: scene_id=2625, 字幕#2800
2025-07-29 20:06:55,800 - INFO - 找到related_overlap场景: scene_id=2626, 字幕#2800
2025-07-29 20:06:55,800 - INFO - 找到related_overlap场景: scene_id=2628, 字幕#2804
2025-07-29 20:06:55,801 - INFO - 字幕 #2800 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:55,801 - INFO - 字幕 #2804 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:55,801 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:55,801 - INFO - 开始生成方案 #1
2025-07-29 20:06:55,801 - INFO - 方案 #1: 为字幕#2800选择初始化overlap场景id=2625
2025-07-29 20:06:55,801 - INFO - 方案 #1: 为字幕#2804选择初始化overlap场景id=2628
2025-07-29 20:06:55,801 - INFO - 方案 #1: 初始选择后，当前总时长=3.12秒
2025-07-29 20:06:55,801 - INFO - 方案 #1: 额外添加overlap场景id=2626, 当前总时长=4.84秒
2025-07-29 20:06:55,801 - INFO - 方案 #1: 额外between选择后，当前总时长=4.84秒
2025-07-29 20:06:55,801 - INFO - 方案 #1: 场景总时长(4.84秒)小于音频时长(4.96秒)，需要延伸填充
2025-07-29 20:06:55,801 - INFO - 方案 #1: 最后一个场景ID: 2626
2025-07-29 20:06:55,801 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2625
2025-07-29 20:06:55,801 - INFO - 方案 #1: 需要填充时长: 0.12秒
2025-07-29 20:06:55,801 - INFO - 方案 #1: 追加场景 scene_id=2627 (裁剪至 0.12秒)
2025-07-29 20:06:55,801 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:55,801 - INFO - 方案 #1 调整/填充后最终总时长: 4.96秒
2025-07-29 20:06:55,801 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:55,801 - INFO - ========== 当前模式：字幕 #69 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:55,801 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:55,801 - INFO - ========== 新模式：字幕 #69 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:55,801 - INFO - 
----- 处理字幕 #69 的方案 #1 -----
2025-07-29 20:06:55,801 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 20:06:55,802 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph22jdch1
2025-07-29 20:06:55,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2625.mp4 (确认存在: True)
2025-07-29 20:06:55,802 - INFO - 添加场景ID=2625，时长=1.92秒，累计时长=1.92秒
2025-07-29 20:06:55,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2626.mp4 (确认存在: True)
2025-07-29 20:06:55,802 - INFO - 添加场景ID=2626，时长=1.72秒，累计时长=3.64秒
2025-07-29 20:06:55,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2627.mp4 (确认存在: True)
2025-07-29 20:06:55,802 - INFO - 添加场景ID=2627，时长=1.84秒，累计时长=5.48秒
2025-07-29 20:06:55,802 - INFO - 准备合并 3 个场景文件，总时长约 5.48秒
2025-07-29 20:06:55,803 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2625.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2626.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2627.mp4'

2025-07-29 20:06:55,803 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmph22jdch1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmph22jdch1\temp_combined.mp4
2025-07-29 20:06:55,943 - INFO - 合并后的视频时长: 5.55秒，目标音频时长: 4.96秒
2025-07-29 20:06:55,943 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmph22jdch1\temp_combined.mp4 -ss 0 -to 4.963 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 20:06:56,260 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:56,260 - INFO - 目标音频时长: 4.96秒
2025-07-29 20:06:56,260 - INFO - 实际视频时长: 5.02秒
2025-07-29 20:06:56,260 - INFO - 时长差异: 0.06秒 (1.21%)
2025-07-29 20:06:56,260 - INFO - ==========================================
2025-07-29 20:06:56,260 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:56,260 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 20:06:56,260 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph22jdch1
2025-07-29 20:06:56,305 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:56,305 - INFO -   - 音频时长: 4.96秒
2025-07-29 20:06:56,305 - INFO -   - 视频时长: 5.02秒
2025-07-29 20:06:56,305 - INFO -   - 时长差异: 0.06秒 (1.21%)
2025-07-29 20:06:56,305 - INFO - 
----- 处理字幕 #69 的方案 #2 -----
2025-07-29 20:06:56,305 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 20:06:56,306 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg6o1vvwk
2025-07-29 20:06:56,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2628.mp4 (确认存在: True)
2025-07-29 20:06:56,306 - INFO - 添加场景ID=2628，时长=1.20秒，累计时长=1.20秒
2025-07-29 20:06:56,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2629.mp4 (确认存在: True)
2025-07-29 20:06:56,306 - INFO - 添加场景ID=2629，时长=1.40秒，累计时长=2.60秒
2025-07-29 20:06:56,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2630.mp4 (确认存在: True)
2025-07-29 20:06:56,306 - INFO - 添加场景ID=2630，时长=0.92秒，累计时长=3.52秒
2025-07-29 20:06:56,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2631.mp4 (确认存在: True)
2025-07-29 20:06:56,306 - INFO - 添加场景ID=2631，时长=1.16秒，累计时长=4.68秒
2025-07-29 20:06:56,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2632.mp4 (确认存在: True)
2025-07-29 20:06:56,306 - INFO - 添加场景ID=2632，时长=1.44秒，累计时长=6.12秒
2025-07-29 20:06:56,306 - INFO - 准备合并 5 个场景文件，总时长约 6.12秒
2025-07-29 20:06:56,306 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2628.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2629.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2630.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2631.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2632.mp4'

2025-07-29 20:06:56,306 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpg6o1vvwk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpg6o1vvwk\temp_combined.mp4
2025-07-29 20:06:56,466 - INFO - 合并后的视频时长: 6.21秒，目标音频时长: 4.96秒
2025-07-29 20:06:56,466 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpg6o1vvwk\temp_combined.mp4 -ss 0 -to 4.963 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 20:06:56,790 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:56,790 - INFO - 目标音频时长: 4.96秒
2025-07-29 20:06:56,790 - INFO - 实际视频时长: 5.02秒
2025-07-29 20:06:56,790 - INFO - 时长差异: 0.06秒 (1.21%)
2025-07-29 20:06:56,790 - INFO - ==========================================
2025-07-29 20:06:56,790 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:56,790 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 20:06:56,791 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg6o1vvwk
2025-07-29 20:06:56,835 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:56,835 - INFO -   - 音频时长: 4.96秒
2025-07-29 20:06:56,835 - INFO -   - 视频时长: 5.02秒
2025-07-29 20:06:56,836 - INFO -   - 时长差异: 0.06秒 (1.21%)
2025-07-29 20:06:56,836 - INFO - 
----- 处理字幕 #69 的方案 #3 -----
2025-07-29 20:06:56,836 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 20:06:56,836 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwr185jox
2025-07-29 20:06:56,836 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2625.mp4 (确认存在: True)
2025-07-29 20:06:56,837 - INFO - 添加场景ID=2625，时长=1.92秒，累计时长=1.92秒
2025-07-29 20:06:56,837 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2628.mp4 (确认存在: True)
2025-07-29 20:06:56,837 - INFO - 添加场景ID=2628，时长=1.20秒，累计时长=3.12秒
2025-07-29 20:06:56,837 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2626.mp4 (确认存在: True)
2025-07-29 20:06:56,837 - INFO - 添加场景ID=2626，时长=1.72秒，累计时长=4.84秒
2025-07-29 20:06:56,837 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2627.mp4 (确认存在: True)
2025-07-29 20:06:56,837 - INFO - 添加场景ID=2627，时长=1.84秒，累计时长=6.68秒
2025-07-29 20:06:56,837 - INFO - 准备合并 4 个场景文件，总时长约 6.68秒
2025-07-29 20:06:56,837 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2625.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2628.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2626.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2627.mp4'

2025-07-29 20:06:56,837 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwr185jox\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwr185jox\temp_combined.mp4
2025-07-29 20:06:56,983 - INFO - 合并后的视频时长: 6.77秒，目标音频时长: 4.96秒
2025-07-29 20:06:56,983 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwr185jox\temp_combined.mp4 -ss 0 -to 4.963 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 20:06:57,309 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:57,309 - INFO - 目标音频时长: 4.96秒
2025-07-29 20:06:57,309 - INFO - 实际视频时长: 5.02秒
2025-07-29 20:06:57,309 - INFO - 时长差异: 0.06秒 (1.21%)
2025-07-29 20:06:57,309 - INFO - ==========================================
2025-07-29 20:06:57,309 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:57,309 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 20:06:57,310 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwr185jox
2025-07-29 20:06:57,353 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:57,353 - INFO -   - 音频时长: 4.96秒
2025-07-29 20:06:57,353 - INFO -   - 视频时长: 5.02秒
2025-07-29 20:06:57,353 - INFO -   - 时长差异: 0.06秒 (1.21%)
2025-07-29 20:06:57,353 - INFO - 
字幕 #69 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:57,353 - INFO - 生成的视频文件:
2025-07-29 20:06:57,353 - INFO -   1. F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 20:06:57,353 - INFO -   2. F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 20:06:57,353 - INFO -   3. F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 20:06:57,353 - INFO - ========== 字幕 #69 处理结束 ==========

