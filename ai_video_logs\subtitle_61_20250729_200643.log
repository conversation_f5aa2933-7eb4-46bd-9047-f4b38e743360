2025-07-29 20:06:43,723 - INFO - ========== 字幕 #61 处理开始 ==========
2025-07-29 20:06:43,723 - INFO - 字幕内容: 他看向女人，给了她最后一次机会，她可以证明，这份计划书，她七年前就看过。
2025-07-29 20:06:43,723 - INFO - 字幕序号: [2418, 2420]
2025-07-29 20:06:43,723 - INFO - 音频文件详情:
2025-07-29 20:06:43,723 - INFO -   - 路径: output\61.wav
2025-07-29 20:06:43,723 - INFO -   - 时长: 3.66秒
2025-07-29 20:06:43,723 - INFO -   - 验证音频时长: 3.66秒
2025-07-29 20:06:43,724 - INFO - 字幕时间戳信息:
2025-07-29 20:06:43,724 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:43,724 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:43,724 - INFO -   - 根据生成的音频时长(3.66秒)已调整字幕时间戳
2025-07-29 20:06:43,724 - INFO - ========== 新模式：为字幕 #61 生成4套场景方案 ==========
2025-07-29 20:06:43,724 - INFO - 字幕序号列表: [2418, 2420]
2025-07-29 20:06:43,724 - INFO - 
--- 生成方案 #1：基于字幕序号 #2418 ---
2025-07-29 20:06:43,724 - INFO - 开始为单个字幕序号 #2418 匹配场景，目标时长: 3.66秒
2025-07-29 20:06:43,724 - INFO - 开始查找字幕序号 [2418] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:43,724 - INFO - 找到related_overlap场景: scene_id=2318, 字幕#2418
2025-07-29 20:06:43,725 - INFO - 字幕 #2418 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:43,725 - INFO - 字幕序号 #2418 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:43,725 - INFO - 选择第一个overlap场景作为起点: scene_id=2318
2025-07-29 20:06:43,725 - INFO - 添加起点场景: scene_id=2318, 时长=1.32秒, 累计时长=1.32秒
2025-07-29 20:06:43,725 - INFO - 起点场景时长不足，需要延伸填充 2.34秒
2025-07-29 20:06:43,725 - INFO - 起点场景在原始列表中的索引: 2317
2025-07-29 20:06:43,725 - INFO - 延伸添加场景: scene_id=2319 (完整时长 1.44秒)
2025-07-29 20:06:43,725 - INFO - 累计时长: 2.76秒
2025-07-29 20:06:43,725 - INFO - 延伸添加场景: scene_id=2320 (裁剪至 0.90秒)
2025-07-29 20:06:43,726 - INFO - 累计时长: 3.66秒
2025-07-29 20:06:43,726 - INFO - 字幕序号 #2418 场景匹配完成，共选择 3 个场景，总时长: 3.66秒
2025-07-29 20:06:43,726 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:43,726 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:43,726 - INFO - 
--- 生成方案 #2：基于字幕序号 #2420 ---
2025-07-29 20:06:43,726 - INFO - 开始为单个字幕序号 #2420 匹配场景，目标时长: 3.66秒
2025-07-29 20:06:43,726 - INFO - 开始查找字幕序号 [2420] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:43,726 - INFO - 找到related_overlap场景: scene_id=2320, 字幕#2420
2025-07-29 20:06:43,726 - INFO - 找到related_overlap场景: scene_id=2321, 字幕#2420
2025-07-29 20:06:43,727 - INFO - 找到related_between场景: scene_id=2322, 字幕#2420
2025-07-29 20:06:43,727 - INFO - 字幕 #2420 找到 2 个overlap场景, 1 个between场景
2025-07-29 20:06:43,727 - INFO - 字幕序号 #2420 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:43,727 - INFO - 选择第一个overlap场景作为起点: scene_id=2321
2025-07-29 20:06:43,727 - INFO - 添加起点场景: scene_id=2321, 时长=0.76秒, 累计时长=0.76秒
2025-07-29 20:06:43,727 - INFO - 起点场景时长不足，需要延伸填充 2.90秒
2025-07-29 20:06:43,727 - INFO - 起点场景在原始列表中的索引: 2320
2025-07-29 20:06:43,727 - INFO - 延伸添加场景: scene_id=2322 (完整时长 1.24秒)
2025-07-29 20:06:43,727 - INFO - 累计时长: 2.00秒
2025-07-29 20:06:43,727 - INFO - 延伸添加场景: scene_id=2323 (完整时长 1.44秒)
2025-07-29 20:06:43,727 - INFO - 累计时长: 3.44秒
2025-07-29 20:06:43,727 - INFO - 延伸添加场景: scene_id=2324 (裁剪至 0.22秒)
2025-07-29 20:06:43,727 - INFO - 累计时长: 3.66秒
2025-07-29 20:06:43,727 - INFO - 字幕序号 #2420 场景匹配完成，共选择 4 个场景，总时长: 3.66秒
2025-07-29 20:06:43,727 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:43,727 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:43,727 - INFO - ========== 当前模式：为字幕 #61 生成 1 套场景方案 ==========
2025-07-29 20:06:43,727 - INFO - 开始查找字幕序号 [2418, 2420] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:43,727 - INFO - 找到related_overlap场景: scene_id=2318, 字幕#2418
2025-07-29 20:06:43,727 - INFO - 找到related_overlap场景: scene_id=2320, 字幕#2420
2025-07-29 20:06:43,727 - INFO - 找到related_overlap场景: scene_id=2321, 字幕#2420
2025-07-29 20:06:43,728 - INFO - 找到related_between场景: scene_id=2322, 字幕#2420
2025-07-29 20:06:43,728 - INFO - 字幕 #2418 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:43,728 - INFO - 字幕 #2420 找到 2 个overlap场景, 1 个between场景
2025-07-29 20:06:43,728 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:06:43,728 - INFO - 开始生成方案 #1
2025-07-29 20:06:43,728 - INFO - 方案 #1: 为字幕#2418选择初始化overlap场景id=2318
2025-07-29 20:06:43,728 - INFO - 方案 #1: 为字幕#2420选择初始化overlap场景id=2321
2025-07-29 20:06:43,728 - INFO - 方案 #1: 初始选择后，当前总时长=2.08秒
2025-07-29 20:06:43,728 - INFO - 方案 #1: 额外添加overlap场景id=2320, 当前总时长=3.48秒
2025-07-29 20:06:43,728 - INFO - 方案 #1: 额外between选择后，当前总时长=3.48秒
2025-07-29 20:06:43,728 - INFO - 方案 #1: 额外添加between场景id=2322, 当前总时长=4.72秒
2025-07-29 20:06:43,728 - INFO - 方案 #1: 场景总时长(4.72秒)大于音频时长(3.66秒)，需要裁剪
2025-07-29 20:06:43,728 - INFO - 调整前总时长: 4.72秒, 目标时长: 3.66秒
2025-07-29 20:06:43,728 - INFO - 需要裁剪 1.06秒
2025-07-29 20:06:43,728 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:43,728 - INFO - 裁剪场景ID=2320：从1.40秒裁剪至1.00秒
2025-07-29 20:06:43,728 - INFO - 裁剪场景ID=2318：从1.32秒裁剪至1.00秒
2025-07-29 20:06:43,728 - INFO - 裁剪场景ID=2322：从1.24秒裁剪至1.00秒
2025-07-29 20:06:43,728 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.10秒
2025-07-29 20:06:43,728 - INFO - 移除场景ID=2321，时长=0.76秒
2025-07-29 20:06:43,728 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.66秒
2025-07-29 20:06:43,728 - INFO - 方案 #1 调整/填充后最终总时长: 3.00秒
2025-07-29 20:06:43,728 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:43,728 - INFO - ========== 当前模式：字幕 #61 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:43,728 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:43,728 - INFO - ========== 新模式：字幕 #61 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:43,728 - INFO - 
----- 处理字幕 #61 的方案 #1 -----
2025-07-29 20:06:43,728 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 20:06:43,729 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwrok1aod
2025-07-29 20:06:43,729 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2318.mp4 (确认存在: True)
2025-07-29 20:06:43,729 - INFO - 添加场景ID=2318，时长=1.32秒，累计时长=1.32秒
2025-07-29 20:06:43,729 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2319.mp4 (确认存在: True)
2025-07-29 20:06:43,729 - INFO - 添加场景ID=2319，时长=1.44秒，累计时长=2.76秒
2025-07-29 20:06:43,729 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2320.mp4 (确认存在: True)
2025-07-29 20:06:43,729 - INFO - 添加场景ID=2320，时长=1.40秒，累计时长=4.16秒
2025-07-29 20:06:43,730 - INFO - 准备合并 3 个场景文件，总时长约 4.16秒
2025-07-29 20:06:43,730 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2318.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2319.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2320.mp4'

2025-07-29 20:06:43,730 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwrok1aod\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwrok1aod\temp_combined.mp4
2025-07-29 20:06:43,867 - INFO - 合并后的视频时长: 4.23秒，目标音频时长: 3.66秒
2025-07-29 20:06:43,867 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwrok1aod\temp_combined.mp4 -ss 0 -to 3.66 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 20:06:44,149 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:44,149 - INFO - 目标音频时长: 3.66秒
2025-07-29 20:06:44,149 - INFO - 实际视频时长: 3.70秒
2025-07-29 20:06:44,149 - INFO - 时长差异: 0.04秒 (1.17%)
2025-07-29 20:06:44,149 - INFO - ==========================================
2025-07-29 20:06:44,149 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:44,149 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 20:06:44,150 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwrok1aod
2025-07-29 20:06:44,194 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:44,194 - INFO -   - 音频时长: 3.66秒
2025-07-29 20:06:44,194 - INFO -   - 视频时长: 3.70秒
2025-07-29 20:06:44,194 - INFO -   - 时长差异: 0.04秒 (1.17%)
2025-07-29 20:06:44,194 - INFO - 
----- 处理字幕 #61 的方案 #2 -----
2025-07-29 20:06:44,194 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 20:06:44,194 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzszvqw4y
2025-07-29 20:06:44,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2321.mp4 (确认存在: True)
2025-07-29 20:06:44,195 - INFO - 添加场景ID=2321，时长=0.76秒，累计时长=0.76秒
2025-07-29 20:06:44,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2322.mp4 (确认存在: True)
2025-07-29 20:06:44,195 - INFO - 添加场景ID=2322，时长=1.24秒，累计时长=2.00秒
2025-07-29 20:06:44,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2323.mp4 (确认存在: True)
2025-07-29 20:06:44,195 - INFO - 添加场景ID=2323，时长=1.44秒，累计时长=3.44秒
2025-07-29 20:06:44,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2324.mp4 (确认存在: True)
2025-07-29 20:06:44,195 - INFO - 添加场景ID=2324，时长=3.00秒，累计时长=6.44秒
2025-07-29 20:06:44,195 - INFO - 场景总时长(6.44秒)已达到音频时长(3.66秒)的1.5倍，停止添加场景
2025-07-29 20:06:44,195 - INFO - 准备合并 4 个场景文件，总时长约 6.44秒
2025-07-29 20:06:44,195 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2321.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2322.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2323.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2324.mp4'

2025-07-29 20:06:44,195 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzszvqw4y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzszvqw4y\temp_combined.mp4
2025-07-29 20:06:44,352 - INFO - 合并后的视频时长: 6.53秒，目标音频时长: 3.66秒
2025-07-29 20:06:44,353 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzszvqw4y\temp_combined.mp4 -ss 0 -to 3.66 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 20:06:44,631 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:44,631 - INFO - 目标音频时长: 3.66秒
2025-07-29 20:06:44,631 - INFO - 实际视频时长: 3.70秒
2025-07-29 20:06:44,631 - INFO - 时长差异: 0.04秒 (1.17%)
2025-07-29 20:06:44,631 - INFO - ==========================================
2025-07-29 20:06:44,631 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:44,631 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 20:06:44,631 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzszvqw4y
2025-07-29 20:06:44,676 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:44,676 - INFO -   - 音频时长: 3.66秒
2025-07-29 20:06:44,676 - INFO -   - 视频时长: 3.70秒
2025-07-29 20:06:44,676 - INFO -   - 时长差异: 0.04秒 (1.17%)
2025-07-29 20:06:44,676 - INFO - 
----- 处理字幕 #61 的方案 #3 -----
2025-07-29 20:06:44,676 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 20:06:44,676 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzfa6nzkr
2025-07-29 20:06:44,678 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2318.mp4 (确认存在: True)
2025-07-29 20:06:44,678 - INFO - 添加场景ID=2318，时长=1.32秒，累计时长=1.32秒
2025-07-29 20:06:44,678 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2320.mp4 (确认存在: True)
2025-07-29 20:06:44,678 - INFO - 添加场景ID=2320，时长=1.40秒，累计时长=2.72秒
2025-07-29 20:06:44,678 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2322.mp4 (确认存在: True)
2025-07-29 20:06:44,678 - INFO - 添加场景ID=2322，时长=1.24秒，累计时长=3.96秒
2025-07-29 20:06:44,678 - INFO - 准备合并 3 个场景文件，总时长约 3.96秒
2025-07-29 20:06:44,678 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2318.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2320.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2322.mp4'

2025-07-29 20:06:44,678 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzfa6nzkr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzfa6nzkr\temp_combined.mp4
2025-07-29 20:06:44,811 - INFO - 合并后的视频时长: 4.03秒，目标音频时长: 3.66秒
2025-07-29 20:06:44,811 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzfa6nzkr\temp_combined.mp4 -ss 0 -to 3.66 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 20:06:45,084 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:45,085 - INFO - 目标音频时长: 3.66秒
2025-07-29 20:06:45,085 - INFO - 实际视频时长: 3.70秒
2025-07-29 20:06:45,085 - INFO - 时长差异: 0.04秒 (1.17%)
2025-07-29 20:06:45,085 - INFO - ==========================================
2025-07-29 20:06:45,085 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:45,085 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 20:06:45,086 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzfa6nzkr
2025-07-29 20:06:45,130 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:45,130 - INFO -   - 音频时长: 3.66秒
2025-07-29 20:06:45,130 - INFO -   - 视频时长: 3.70秒
2025-07-29 20:06:45,130 - INFO -   - 时长差异: 0.04秒 (1.17%)
2025-07-29 20:06:45,130 - INFO - 
字幕 #61 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:45,130 - INFO - 生成的视频文件:
2025-07-29 20:06:45,130 - INFO -   1. F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 20:06:45,130 - INFO -   2. F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 20:06:45,130 - INFO -   3. F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 20:06:45,130 - INFO - ========== 字幕 #61 处理结束 ==========

