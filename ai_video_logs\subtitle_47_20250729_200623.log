2025-07-29 20:06:23,223 - INFO - ========== 字幕 #47 处理开始 ==========
2025-07-29 20:06:23,223 - INFO - 字幕内容: 她冲上前去，却看到男人和一个陌生的女孩亲昵互动，而那个酷似安安的女孩，也甜甜地叫着另一个女人“嫂子”。
2025-07-29 20:06:23,223 - INFO - 字幕序号: [1263, 1266]
2025-07-29 20:06:23,224 - INFO - 音频文件详情:
2025-07-29 20:06:23,224 - INFO -   - 路径: output\47.wav
2025-07-29 20:06:23,224 - INFO -   - 时长: 6.70秒
2025-07-29 20:06:23,224 - INFO -   - 验证音频时长: 6.70秒
2025-07-29 20:06:23,224 - INFO - 字幕时间戳信息:
2025-07-29 20:06:23,224 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:23,224 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:23,224 - INFO -   - 根据生成的音频时长(6.70秒)已调整字幕时间戳
2025-07-29 20:06:23,224 - INFO - ========== 新模式：为字幕 #47 生成4套场景方案 ==========
2025-07-29 20:06:23,224 - INFO - 字幕序号列表: [1263, 1266]
2025-07-29 20:06:23,224 - INFO - 
--- 生成方案 #1：基于字幕序号 #1263 ---
2025-07-29 20:06:23,224 - INFO - 开始为单个字幕序号 #1263 匹配场景，目标时长: 6.70秒
2025-07-29 20:06:23,224 - INFO - 开始查找字幕序号 [1263] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:23,225 - INFO - 找到related_overlap场景: scene_id=1342, 字幕#1263
2025-07-29 20:06:23,225 - INFO - 找到related_between场景: scene_id=1337, 字幕#1263
2025-07-29 20:06:23,226 - INFO - 找到related_between场景: scene_id=1338, 字幕#1263
2025-07-29 20:06:23,226 - INFO - 找到related_between场景: scene_id=1339, 字幕#1263
2025-07-29 20:06:23,226 - INFO - 找到related_between场景: scene_id=1340, 字幕#1263
2025-07-29 20:06:23,226 - INFO - 找到related_between场景: scene_id=1341, 字幕#1263
2025-07-29 20:06:23,226 - INFO - 字幕 #1263 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:06:23,226 - INFO - 字幕序号 #1263 找到 1 个可用overlap场景, 5 个可用between场景
2025-07-29 20:06:23,226 - INFO - 选择第一个overlap场景作为起点: scene_id=1342
2025-07-29 20:06:23,226 - INFO - 添加起点场景: scene_id=1342, 时长=1.40秒, 累计时长=1.40秒
2025-07-29 20:06:23,226 - INFO - 起点场景时长不足，需要延伸填充 5.30秒
2025-07-29 20:06:23,226 - INFO - 起点场景在原始列表中的索引: 1341
2025-07-29 20:06:23,226 - INFO - 延伸添加场景: scene_id=1343 (完整时长 2.12秒)
2025-07-29 20:06:23,226 - INFO - 累计时长: 3.52秒
2025-07-29 20:06:23,226 - INFO - 延伸添加场景: scene_id=1344 (裁剪至 3.18秒)
2025-07-29 20:06:23,226 - INFO - 累计时长: 6.70秒
2025-07-29 20:06:23,226 - INFO - 字幕序号 #1263 场景匹配完成，共选择 3 个场景，总时长: 6.70秒
2025-07-29 20:06:23,226 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:23,226 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:23,226 - INFO - 
--- 生成方案 #2：基于字幕序号 #1266 ---
2025-07-29 20:06:23,226 - INFO - 开始为单个字幕序号 #1266 匹配场景，目标时长: 6.70秒
2025-07-29 20:06:23,226 - INFO - 开始查找字幕序号 [1266] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:23,227 - INFO - 找到related_overlap场景: scene_id=1344, 字幕#1266
2025-07-29 20:06:23,227 - INFO - 字幕 #1266 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:23,227 - INFO - 字幕序号 #1266 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:23,227 - ERROR - 字幕序号 #1266 没有找到任何可用的匹配场景
2025-07-29 20:06:23,227 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:23,227 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:23,227 - INFO - ========== 当前模式：为字幕 #47 生成 1 套场景方案 ==========
2025-07-29 20:06:23,227 - INFO - 开始查找字幕序号 [1263, 1266] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:23,227 - INFO - 找到related_overlap场景: scene_id=1342, 字幕#1263
2025-07-29 20:06:23,227 - INFO - 找到related_overlap场景: scene_id=1344, 字幕#1266
2025-07-29 20:06:23,228 - INFO - 找到related_between场景: scene_id=1337, 字幕#1263
2025-07-29 20:06:23,228 - INFO - 找到related_between场景: scene_id=1338, 字幕#1263
2025-07-29 20:06:23,229 - INFO - 找到related_between场景: scene_id=1339, 字幕#1263
2025-07-29 20:06:23,229 - INFO - 找到related_between场景: scene_id=1340, 字幕#1263
2025-07-29 20:06:23,229 - INFO - 找到related_between场景: scene_id=1341, 字幕#1263
2025-07-29 20:06:23,229 - INFO - 字幕 #1263 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:06:23,229 - INFO - 字幕 #1266 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:23,229 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 20:06:23,229 - INFO - 开始生成方案 #1
2025-07-29 20:06:23,229 - INFO - 方案 #1: 为字幕#1263选择初始化overlap场景id=1342
2025-07-29 20:06:23,229 - INFO - 方案 #1: 为字幕#1266选择初始化overlap场景id=1344
2025-07-29 20:06:23,229 - INFO - 方案 #1: 初始选择后，当前总时长=6.96秒
2025-07-29 20:06:23,229 - INFO - 方案 #1: 额外between选择后，当前总时长=6.96秒
2025-07-29 20:06:23,229 - INFO - 方案 #1: 场景总时长(6.96秒)大于音频时长(6.70秒)，需要裁剪
2025-07-29 20:06:23,229 - INFO - 调整前总时长: 6.96秒, 目标时长: 6.70秒
2025-07-29 20:06:23,229 - INFO - 需要裁剪 0.26秒
2025-07-29 20:06:23,229 - INFO - 裁剪最长场景ID=1344：从5.56秒裁剪至5.30秒
2025-07-29 20:06:23,229 - INFO - 调整后总时长: 6.70秒，与目标时长差异: 0.00秒
2025-07-29 20:06:23,229 - INFO - 方案 #1 调整/填充后最终总时长: 6.70秒
2025-07-29 20:06:23,229 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:23,229 - INFO - ========== 当前模式：字幕 #47 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:23,229 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:23,229 - INFO - ========== 新模式：字幕 #47 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:23,229 - INFO - 
----- 处理字幕 #47 的方案 #1 -----
2025-07-29 20:06:23,229 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 20:06:23,229 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0sth66qs
2025-07-29 20:06:23,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1342.mp4 (确认存在: True)
2025-07-29 20:06:23,230 - INFO - 添加场景ID=1342，时长=1.40秒，累计时长=1.40秒
2025-07-29 20:06:23,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1343.mp4 (确认存在: True)
2025-07-29 20:06:23,230 - INFO - 添加场景ID=1343，时长=2.12秒，累计时长=3.52秒
2025-07-29 20:06:23,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1344.mp4 (确认存在: True)
2025-07-29 20:06:23,230 - INFO - 添加场景ID=1344，时长=5.56秒，累计时长=9.08秒
2025-07-29 20:06:23,230 - INFO - 准备合并 3 个场景文件，总时长约 9.08秒
2025-07-29 20:06:23,230 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1342.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1343.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1344.mp4'

2025-07-29 20:06:23,230 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0sth66qs\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0sth66qs\temp_combined.mp4
2025-07-29 20:06:23,374 - INFO - 合并后的视频时长: 9.15秒，目标音频时长: 6.70秒
2025-07-29 20:06:23,374 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0sth66qs\temp_combined.mp4 -ss 0 -to 6.702 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 20:06:23,757 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:23,757 - INFO - 目标音频时长: 6.70秒
2025-07-29 20:06:23,757 - INFO - 实际视频时长: 6.74秒
2025-07-29 20:06:23,757 - INFO - 时长差异: 0.04秒 (0.61%)
2025-07-29 20:06:23,757 - INFO - ==========================================
2025-07-29 20:06:23,757 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:23,757 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 20:06:23,758 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0sth66qs
2025-07-29 20:06:23,801 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:23,801 - INFO -   - 音频时长: 6.70秒
2025-07-29 20:06:23,801 - INFO -   - 视频时长: 6.74秒
2025-07-29 20:06:23,801 - INFO -   - 时长差异: 0.04秒 (0.61%)
2025-07-29 20:06:23,801 - INFO - 
----- 处理字幕 #47 的方案 #2 -----
2025-07-29 20:06:23,801 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 20:06:23,802 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpckvwwmcl
2025-07-29 20:06:23,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1342.mp4 (确认存在: True)
2025-07-29 20:06:23,802 - INFO - 添加场景ID=1342，时长=1.40秒，累计时长=1.40秒
2025-07-29 20:06:23,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1344.mp4 (确认存在: True)
2025-07-29 20:06:23,803 - INFO - 添加场景ID=1344，时长=5.56秒，累计时长=6.96秒
2025-07-29 20:06:23,803 - INFO - 准备合并 2 个场景文件，总时长约 6.96秒
2025-07-29 20:06:23,803 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1342.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1344.mp4'

2025-07-29 20:06:23,803 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpckvwwmcl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpckvwwmcl\temp_combined.mp4
2025-07-29 20:06:23,926 - INFO - 合并后的视频时长: 7.01秒，目标音频时长: 6.70秒
2025-07-29 20:06:23,926 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpckvwwmcl\temp_combined.mp4 -ss 0 -to 6.702 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 20:06:24,290 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:24,290 - INFO - 目标音频时长: 6.70秒
2025-07-29 20:06:24,290 - INFO - 实际视频时长: 6.74秒
2025-07-29 20:06:24,291 - INFO - 时长差异: 0.04秒 (0.61%)
2025-07-29 20:06:24,291 - INFO - ==========================================
2025-07-29 20:06:24,291 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:24,291 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 20:06:24,291 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpckvwwmcl
2025-07-29 20:06:24,337 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:24,337 - INFO -   - 音频时长: 6.70秒
2025-07-29 20:06:24,337 - INFO -   - 视频时长: 6.74秒
2025-07-29 20:06:24,337 - INFO -   - 时长差异: 0.04秒 (0.61%)
2025-07-29 20:06:24,338 - INFO - 
字幕 #47 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:24,338 - INFO - 生成的视频文件:
2025-07-29 20:06:24,338 - INFO -   1. F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 20:06:24,338 - INFO -   2. F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 20:06:24,338 - INFO - ========== 字幕 #47 处理结束 ==========

