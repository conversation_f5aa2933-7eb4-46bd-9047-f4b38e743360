2025-07-29 20:05:21,076 - INFO - ========== 字幕 #4 处理开始 ==========
2025-07-29 20:05:21,076 - INFO - 字幕内容: 然而现实残酷，妹妹心脏病突发被送进急诊室，生命垂危。
2025-07-29 20:05:21,076 - INFO - 字幕序号: [17, 25]
2025-07-29 20:05:21,076 - INFO - 音频文件详情:
2025-07-29 20:05:21,076 - INFO -   - 路径: output\4.wav
2025-07-29 20:05:21,076 - INFO -   - 时长: 4.29秒
2025-07-29 20:05:21,076 - INFO -   - 验证音频时长: 4.29秒
2025-07-29 20:05:21,076 - INFO - 字幕时间戳信息:
2025-07-29 20:05:21,076 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:21,076 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:21,076 - INFO -   - 根据生成的音频时长(4.29秒)已调整字幕时间戳
2025-07-29 20:05:21,076 - INFO - ========== 新模式：为字幕 #4 生成4套场景方案 ==========
2025-07-29 20:05:21,076 - INFO - 字幕序号列表: [17, 25]
2025-07-29 20:05:21,076 - INFO - 
--- 生成方案 #1：基于字幕序号 #17 ---
2025-07-29 20:05:21,076 - INFO - 开始为单个字幕序号 #17 匹配场景，目标时长: 4.29秒
2025-07-29 20:05:21,077 - INFO - 开始查找字幕序号 [17] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:21,077 - INFO - 找到related_overlap场景: scene_id=16, 字幕#17
2025-07-29 20:05:21,077 - INFO - 找到related_overlap场景: scene_id=17, 字幕#17
2025-07-29 20:05:21,077 - INFO - 找到related_between场景: scene_id=14, 字幕#17
2025-07-29 20:05:21,077 - INFO - 找到related_between场景: scene_id=15, 字幕#17
2025-07-29 20:05:21,078 - INFO - 字幕 #17 找到 2 个overlap场景, 2 个between场景
2025-07-29 20:05:21,078 - INFO - 字幕序号 #17 找到 2 个可用overlap场景, 2 个可用between场景
2025-07-29 20:05:21,078 - INFO - 选择第一个overlap场景作为起点: scene_id=16
2025-07-29 20:05:21,078 - INFO - 添加起点场景: scene_id=16, 时长=1.68秒, 累计时长=1.68秒
2025-07-29 20:05:21,078 - INFO - 起点场景时长不足，需要延伸填充 2.61秒
2025-07-29 20:05:21,078 - INFO - 起点场景在原始列表中的索引: 15
2025-07-29 20:05:21,078 - INFO - 延伸添加场景: scene_id=17 (完整时长 1.28秒)
2025-07-29 20:05:21,078 - INFO - 累计时长: 2.96秒
2025-07-29 20:05:21,078 - INFO - 延伸添加场景: scene_id=18 (裁剪至 1.33秒)
2025-07-29 20:05:21,078 - INFO - 累计时长: 4.29秒
2025-07-29 20:05:21,078 - INFO - 字幕序号 #17 场景匹配完成，共选择 3 个场景，总时长: 4.29秒
2025-07-29 20:05:21,078 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:21,078 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:21,078 - INFO - 
--- 生成方案 #2：基于字幕序号 #25 ---
2025-07-29 20:05:21,078 - INFO - 开始为单个字幕序号 #25 匹配场景，目标时长: 4.29秒
2025-07-29 20:05:21,078 - INFO - 开始查找字幕序号 [25] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:21,078 - INFO - 找到related_overlap场景: scene_id=22, 字幕#25
2025-07-29 20:05:21,079 - INFO - 字幕 #25 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:21,079 - INFO - 字幕序号 #25 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:21,079 - INFO - 选择第一个overlap场景作为起点: scene_id=22
2025-07-29 20:05:21,079 - INFO - 添加起点场景: scene_id=22, 时长=2.96秒, 累计时长=2.96秒
2025-07-29 20:05:21,079 - INFO - 起点场景时长不足，需要延伸填充 1.33秒
2025-07-29 20:05:21,079 - INFO - 起点场景在原始列表中的索引: 21
2025-07-29 20:05:21,079 - INFO - 延伸添加场景: scene_id=23 (裁剪至 1.33秒)
2025-07-29 20:05:21,079 - INFO - 累计时长: 4.29秒
2025-07-29 20:05:21,079 - INFO - 字幕序号 #25 场景匹配完成，共选择 2 个场景，总时长: 4.29秒
2025-07-29 20:05:21,079 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:05:21,079 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:21,079 - INFO - ========== 当前模式：为字幕 #4 生成 1 套场景方案 ==========
2025-07-29 20:05:21,079 - INFO - 开始查找字幕序号 [17, 25] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:21,079 - INFO - 找到related_overlap场景: scene_id=16, 字幕#17
2025-07-29 20:05:21,079 - INFO - 找到related_overlap场景: scene_id=17, 字幕#17
2025-07-29 20:05:21,079 - INFO - 找到related_overlap场景: scene_id=22, 字幕#25
2025-07-29 20:05:21,080 - INFO - 找到related_between场景: scene_id=14, 字幕#17
2025-07-29 20:05:21,080 - INFO - 找到related_between场景: scene_id=15, 字幕#17
2025-07-29 20:05:21,081 - INFO - 字幕 #17 找到 2 个overlap场景, 2 个between场景
2025-07-29 20:05:21,081 - INFO - 字幕 #25 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:21,081 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 20:05:21,081 - INFO - 开始生成方案 #1
2025-07-29 20:05:21,081 - INFO - 方案 #1: 为字幕#17选择初始化overlap场景id=16
2025-07-29 20:05:21,081 - INFO - 方案 #1: 为字幕#25选择初始化overlap场景id=22
2025-07-29 20:05:21,081 - INFO - 方案 #1: 初始选择后，当前总时长=4.64秒
2025-07-29 20:05:21,081 - INFO - 方案 #1: 额外between选择后，当前总时长=4.64秒
2025-07-29 20:05:21,081 - INFO - 方案 #1: 场景总时长(4.64秒)大于音频时长(4.29秒)，需要裁剪
2025-07-29 20:05:21,081 - INFO - 调整前总时长: 4.64秒, 目标时长: 4.29秒
2025-07-29 20:05:21,081 - INFO - 需要裁剪 0.35秒
2025-07-29 20:05:21,081 - INFO - 裁剪最长场景ID=22：从2.96秒裁剪至2.61秒
2025-07-29 20:05:21,081 - INFO - 调整后总时长: 4.29秒，与目标时长差异: 0.00秒
2025-07-29 20:05:21,081 - INFO - 方案 #1 调整/填充后最终总时长: 4.29秒
2025-07-29 20:05:21,081 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:21,081 - INFO - ========== 当前模式：字幕 #4 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:21,081 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:21,081 - INFO - ========== 新模式：字幕 #4 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:21,081 - INFO - 
----- 处理字幕 #4 的方案 #1 -----
2025-07-29 20:05:21,081 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 20:05:21,081 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuygjxdqp
2025-07-29 20:05:21,082 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\16.mp4 (确认存在: True)
2025-07-29 20:05:21,082 - INFO - 添加场景ID=16，时长=1.68秒，累计时长=1.68秒
2025-07-29 20:05:21,082 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\17.mp4 (确认存在: True)
2025-07-29 20:05:21,082 - INFO - 添加场景ID=17，时长=1.28秒，累计时长=2.96秒
2025-07-29 20:05:21,082 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\18.mp4 (确认存在: True)
2025-07-29 20:05:21,082 - INFO - 添加场景ID=18，时长=1.52秒，累计时长=4.48秒
2025-07-29 20:05:21,082 - INFO - 准备合并 3 个场景文件，总时长约 4.48秒
2025-07-29 20:05:21,082 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/16.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/17.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/18.mp4'

2025-07-29 20:05:21,082 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuygjxdqp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuygjxdqp\temp_combined.mp4
2025-07-29 20:05:21,216 - INFO - 合并后的视频时长: 4.55秒，目标音频时长: 4.29秒
2025-07-29 20:05:21,217 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuygjxdqp\temp_combined.mp4 -ss 0 -to 4.29 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 20:05:21,502 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:21,502 - INFO - 目标音频时长: 4.29秒
2025-07-29 20:05:21,502 - INFO - 实际视频时长: 4.34秒
2025-07-29 20:05:21,502 - INFO - 时长差异: 0.05秒 (1.24%)
2025-07-29 20:05:21,502 - INFO - ==========================================
2025-07-29 20:05:21,502 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:21,502 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 20:05:21,502 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuygjxdqp
2025-07-29 20:05:21,545 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:21,545 - INFO -   - 音频时长: 4.29秒
2025-07-29 20:05:21,545 - INFO -   - 视频时长: 4.34秒
2025-07-29 20:05:21,545 - INFO -   - 时长差异: 0.05秒 (1.24%)
2025-07-29 20:05:21,545 - INFO - 
----- 处理字幕 #4 的方案 #2 -----
2025-07-29 20:05:21,545 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 20:05:21,547 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcm58d7tt
2025-07-29 20:05:21,547 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-07-29 20:05:21,547 - INFO - 添加场景ID=22，时长=2.96秒，累计时长=2.96秒
2025-07-29 20:05:21,547 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\23.mp4 (确认存在: True)
2025-07-29 20:05:21,547 - INFO - 添加场景ID=23，时长=1.68秒，累计时长=4.64秒
2025-07-29 20:05:21,547 - INFO - 准备合并 2 个场景文件，总时长约 4.64秒
2025-07-29 20:05:21,547 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/23.mp4'

2025-07-29 20:05:21,548 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcm58d7tt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcm58d7tt\temp_combined.mp4
2025-07-29 20:05:21,670 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 4.29秒
2025-07-29 20:05:21,670 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcm58d7tt\temp_combined.mp4 -ss 0 -to 4.29 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 20:05:21,999 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:21,999 - INFO - 目标音频时长: 4.29秒
2025-07-29 20:05:21,999 - INFO - 实际视频时长: 4.34秒
2025-07-29 20:05:21,999 - INFO - 时长差异: 0.05秒 (1.24%)
2025-07-29 20:05:21,999 - INFO - ==========================================
2025-07-29 20:05:21,999 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:21,999 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 20:05:22,000 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcm58d7tt
2025-07-29 20:05:22,046 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:22,046 - INFO -   - 音频时长: 4.29秒
2025-07-29 20:05:22,046 - INFO -   - 视频时长: 4.34秒
2025-07-29 20:05:22,046 - INFO -   - 时长差异: 0.05秒 (1.24%)
2025-07-29 20:05:22,046 - INFO - 
----- 处理字幕 #4 的方案 #3 -----
2025-07-29 20:05:22,046 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 20:05:22,046 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpelux9xfe
2025-07-29 20:05:22,047 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\16.mp4 (确认存在: True)
2025-07-29 20:05:22,047 - INFO - 添加场景ID=16，时长=1.68秒，累计时长=1.68秒
2025-07-29 20:05:22,047 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-07-29 20:05:22,047 - INFO - 添加场景ID=22，时长=2.96秒，累计时长=4.64秒
2025-07-29 20:05:22,047 - INFO - 准备合并 2 个场景文件，总时长约 4.64秒
2025-07-29 20:05:22,047 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/16.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'

2025-07-29 20:05:22,047 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpelux9xfe\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpelux9xfe\temp_combined.mp4
2025-07-29 20:05:22,155 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 4.29秒
2025-07-29 20:05:22,155 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpelux9xfe\temp_combined.mp4 -ss 0 -to 4.29 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 20:05:22,450 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:22,450 - INFO - 目标音频时长: 4.29秒
2025-07-29 20:05:22,450 - INFO - 实际视频时长: 4.34秒
2025-07-29 20:05:22,450 - INFO - 时长差异: 0.05秒 (1.24%)
2025-07-29 20:05:22,450 - INFO - ==========================================
2025-07-29 20:05:22,450 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:22,450 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 20:05:22,451 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpelux9xfe
2025-07-29 20:05:22,498 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:22,498 - INFO -   - 音频时长: 4.29秒
2025-07-29 20:05:22,498 - INFO -   - 视频时长: 4.34秒
2025-07-29 20:05:22,498 - INFO -   - 时长差异: 0.05秒 (1.24%)
2025-07-29 20:05:22,498 - INFO - 
字幕 #4 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:22,498 - INFO - 生成的视频文件:
2025-07-29 20:05:22,498 - INFO -   1. F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 20:05:22,498 - INFO -   2. F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 20:05:22,498 - INFO -   3. F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 20:05:22,498 - INFO - ========== 字幕 #4 处理结束 ==========

