2025-07-29 20:06:48,893 - INFO - ========== 字幕 #65 处理开始 ==========
2025-07-29 20:06:48,893 - INFO - 字幕内容: 就在这时，男人的特助出现，恭敬地向他行礼，周围的喧嚣戛然而止。
2025-07-29 20:06:48,893 - INFO - 字幕序号: [2537, 2541]
2025-07-29 20:06:48,893 - INFO - 音频文件详情:
2025-07-29 20:06:48,893 - INFO -   - 路径: output\65.wav
2025-07-29 20:06:48,893 - INFO -   - 时长: 4.42秒
2025-07-29 20:06:48,894 - INFO -   - 验证音频时长: 4.42秒
2025-07-29 20:06:48,894 - INFO - 字幕时间戳信息:
2025-07-29 20:06:48,894 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:48,894 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:48,894 - INFO -   - 根据生成的音频时长(4.42秒)已调整字幕时间戳
2025-07-29 20:06:48,894 - INFO - ========== 新模式：为字幕 #65 生成4套场景方案 ==========
2025-07-29 20:06:48,894 - INFO - 字幕序号列表: [2537, 2541]
2025-07-29 20:06:48,894 - INFO - 
--- 生成方案 #1：基于字幕序号 #2537 ---
2025-07-29 20:06:48,894 - INFO - 开始为单个字幕序号 #2537 匹配场景，目标时长: 4.42秒
2025-07-29 20:06:48,894 - INFO - 开始查找字幕序号 [2537] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:48,895 - INFO - 找到related_overlap场景: scene_id=2389, 字幕#2537
2025-07-29 20:06:48,895 - INFO - 找到related_between场景: scene_id=2390, 字幕#2537
2025-07-29 20:06:48,895 - INFO - 找到related_between场景: scene_id=2391, 字幕#2537
2025-07-29 20:06:48,896 - INFO - 字幕 #2537 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:48,896 - INFO - 字幕序号 #2537 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:06:48,896 - INFO - 选择第一个overlap场景作为起点: scene_id=2389
2025-07-29 20:06:48,896 - INFO - 添加起点场景: scene_id=2389, 时长=3.68秒, 累计时长=3.68秒
2025-07-29 20:06:48,896 - INFO - 起点场景时长不足，需要延伸填充 0.74秒
2025-07-29 20:06:48,896 - INFO - 起点场景在原始列表中的索引: 2388
2025-07-29 20:06:48,896 - INFO - 延伸添加场景: scene_id=2390 (裁剪至 0.74秒)
2025-07-29 20:06:48,896 - INFO - 累计时长: 4.42秒
2025-07-29 20:06:48,896 - INFO - 字幕序号 #2537 场景匹配完成，共选择 2 个场景，总时长: 4.42秒
2025-07-29 20:06:48,896 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:06:48,896 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:06:48,896 - INFO - 
--- 生成方案 #2：基于字幕序号 #2541 ---
2025-07-29 20:06:48,896 - INFO - 开始为单个字幕序号 #2541 匹配场景，目标时长: 4.42秒
2025-07-29 20:06:48,896 - INFO - 开始查找字幕序号 [2541] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:48,896 - INFO - 找到related_overlap场景: scene_id=2395, 字幕#2541
2025-07-29 20:06:48,897 - INFO - 字幕 #2541 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:48,897 - INFO - 字幕序号 #2541 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:48,897 - INFO - 选择第一个overlap场景作为起点: scene_id=2395
2025-07-29 20:06:48,897 - INFO - 添加起点场景: scene_id=2395, 时长=1.12秒, 累计时长=1.12秒
2025-07-29 20:06:48,897 - INFO - 起点场景时长不足，需要延伸填充 3.30秒
2025-07-29 20:06:48,897 - INFO - 起点场景在原始列表中的索引: 2394
2025-07-29 20:06:48,897 - INFO - 延伸添加场景: scene_id=2396 (完整时长 2.28秒)
2025-07-29 20:06:48,897 - INFO - 累计时长: 3.40秒
2025-07-29 20:06:48,897 - INFO - 延伸添加场景: scene_id=2397 (裁剪至 1.02秒)
2025-07-29 20:06:48,897 - INFO - 累计时长: 4.42秒
2025-07-29 20:06:48,897 - INFO - 字幕序号 #2541 场景匹配完成，共选择 3 个场景，总时长: 4.42秒
2025-07-29 20:06:48,897 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:06:48,897 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:48,897 - INFO - ========== 当前模式：为字幕 #65 生成 1 套场景方案 ==========
2025-07-29 20:06:48,897 - INFO - 开始查找字幕序号 [2537, 2541] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:48,897 - INFO - 找到related_overlap场景: scene_id=2389, 字幕#2537
2025-07-29 20:06:48,897 - INFO - 找到related_overlap场景: scene_id=2395, 字幕#2541
2025-07-29 20:06:48,898 - INFO - 找到related_between场景: scene_id=2390, 字幕#2537
2025-07-29 20:06:48,898 - INFO - 找到related_between场景: scene_id=2391, 字幕#2537
2025-07-29 20:06:48,898 - INFO - 字幕 #2537 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:48,898 - INFO - 字幕 #2541 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:48,898 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 20:06:48,898 - INFO - 开始生成方案 #1
2025-07-29 20:06:48,898 - INFO - 方案 #1: 为字幕#2537选择初始化overlap场景id=2389
2025-07-29 20:06:48,898 - INFO - 方案 #1: 为字幕#2541选择初始化overlap场景id=2395
2025-07-29 20:06:48,898 - INFO - 方案 #1: 初始选择后，当前总时长=4.80秒
2025-07-29 20:06:48,898 - INFO - 方案 #1: 额外between选择后，当前总时长=4.80秒
2025-07-29 20:06:48,898 - INFO - 方案 #1: 场景总时长(4.80秒)大于音频时长(4.42秒)，需要裁剪
2025-07-29 20:06:48,898 - INFO - 调整前总时长: 4.80秒, 目标时长: 4.42秒
2025-07-29 20:06:48,898 - INFO - 需要裁剪 0.38秒
2025-07-29 20:06:48,898 - INFO - 裁剪最长场景ID=2389：从3.68秒裁剪至3.30秒
2025-07-29 20:06:48,898 - INFO - 调整后总时长: 4.42秒，与目标时长差异: 0.00秒
2025-07-29 20:06:48,898 - INFO - 方案 #1 调整/填充后最终总时长: 4.42秒
2025-07-29 20:06:48,898 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:48,899 - INFO - ========== 当前模式：字幕 #65 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:48,899 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:48,899 - INFO - ========== 新模式：字幕 #65 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:48,899 - INFO - 
----- 处理字幕 #65 的方案 #1 -----
2025-07-29 20:06:48,899 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 20:06:48,899 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6l32qpv1
2025-07-29 20:06:48,899 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2389.mp4 (确认存在: True)
2025-07-29 20:06:48,899 - INFO - 添加场景ID=2389，时长=3.68秒，累计时长=3.68秒
2025-07-29 20:06:48,900 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2390.mp4 (确认存在: True)
2025-07-29 20:06:48,900 - INFO - 添加场景ID=2390，时长=2.00秒，累计时长=5.68秒
2025-07-29 20:06:48,900 - INFO - 准备合并 2 个场景文件，总时长约 5.68秒
2025-07-29 20:06:48,900 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2389.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2390.mp4'

2025-07-29 20:06:48,900 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6l32qpv1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6l32qpv1\temp_combined.mp4
2025-07-29 20:06:49,019 - INFO - 合并后的视频时长: 5.71秒，目标音频时长: 4.42秒
2025-07-29 20:06:49,019 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6l32qpv1\temp_combined.mp4 -ss 0 -to 4.421 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 20:06:49,311 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:49,311 - INFO - 目标音频时长: 4.42秒
2025-07-29 20:06:49,311 - INFO - 实际视频时长: 4.46秒
2025-07-29 20:06:49,312 - INFO - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:06:49,312 - INFO - ==========================================
2025-07-29 20:06:49,312 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:49,312 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 20:06:49,312 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6l32qpv1
2025-07-29 20:06:49,357 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:49,357 - INFO -   - 音频时长: 4.42秒
2025-07-29 20:06:49,357 - INFO -   - 视频时长: 4.46秒
2025-07-29 20:06:49,357 - INFO -   - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:06:49,357 - INFO - 
----- 处理字幕 #65 的方案 #2 -----
2025-07-29 20:06:49,357 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 20:06:49,357 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1u2cau3f
2025-07-29 20:06:49,358 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2395.mp4 (确认存在: True)
2025-07-29 20:06:49,358 - INFO - 添加场景ID=2395，时长=1.12秒，累计时长=1.12秒
2025-07-29 20:06:49,358 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2396.mp4 (确认存在: True)
2025-07-29 20:06:49,358 - INFO - 添加场景ID=2396，时长=2.28秒，累计时长=3.40秒
2025-07-29 20:06:49,358 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2397.mp4 (确认存在: True)
2025-07-29 20:06:49,358 - INFO - 添加场景ID=2397，时长=2.36秒，累计时长=5.76秒
2025-07-29 20:06:49,358 - INFO - 准备合并 3 个场景文件，总时长约 5.76秒
2025-07-29 20:06:49,358 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2395.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2396.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2397.mp4'

2025-07-29 20:06:49,358 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1u2cau3f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1u2cau3f\temp_combined.mp4
2025-07-29 20:06:49,516 - INFO - 合并后的视频时长: 5.83秒，目标音频时长: 4.42秒
2025-07-29 20:06:49,516 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1u2cau3f\temp_combined.mp4 -ss 0 -to 4.421 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 20:06:49,828 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:49,828 - INFO - 目标音频时长: 4.42秒
2025-07-29 20:06:49,828 - INFO - 实际视频时长: 4.46秒
2025-07-29 20:06:49,828 - INFO - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:06:49,828 - INFO - ==========================================
2025-07-29 20:06:49,828 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:49,828 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 20:06:49,829 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1u2cau3f
2025-07-29 20:06:49,874 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:49,874 - INFO -   - 音频时长: 4.42秒
2025-07-29 20:06:49,874 - INFO -   - 视频时长: 4.46秒
2025-07-29 20:06:49,874 - INFO -   - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:06:49,874 - INFO - 
----- 处理字幕 #65 的方案 #3 -----
2025-07-29 20:06:49,874 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 20:06:49,874 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyebo44ns
2025-07-29 20:06:49,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2389.mp4 (确认存在: True)
2025-07-29 20:06:49,875 - INFO - 添加场景ID=2389，时长=3.68秒，累计时长=3.68秒
2025-07-29 20:06:49,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2395.mp4 (确认存在: True)
2025-07-29 20:06:49,875 - INFO - 添加场景ID=2395，时长=1.12秒，累计时长=4.80秒
2025-07-29 20:06:49,875 - INFO - 准备合并 2 个场景文件，总时长约 4.80秒
2025-07-29 20:06:49,875 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2389.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2395.mp4'

2025-07-29 20:06:49,875 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyebo44ns\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyebo44ns\temp_combined.mp4
2025-07-29 20:06:49,988 - INFO - 合并后的视频时长: 4.85秒，目标音频时长: 4.42秒
2025-07-29 20:06:49,988 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyebo44ns\temp_combined.mp4 -ss 0 -to 4.421 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 20:06:50,267 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:50,268 - INFO - 目标音频时长: 4.42秒
2025-07-29 20:06:50,268 - INFO - 实际视频时长: 4.46秒
2025-07-29 20:06:50,268 - INFO - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:06:50,268 - INFO - ==========================================
2025-07-29 20:06:50,268 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:50,268 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 20:06:50,269 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyebo44ns
2025-07-29 20:06:50,312 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:50,312 - INFO -   - 音频时长: 4.42秒
2025-07-29 20:06:50,312 - INFO -   - 视频时长: 4.46秒
2025-07-29 20:06:50,312 - INFO -   - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:06:50,312 - INFO - 
字幕 #65 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:50,312 - INFO - 生成的视频文件:
2025-07-29 20:06:50,312 - INFO -   1. F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 20:06:50,312 - INFO -   2. F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 20:06:50,312 - INFO -   3. F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 20:06:50,312 - INFO - ========== 字幕 #65 处理结束 ==========

