2025-07-29 20:07:01,769 - INFO - ========== 字幕 #74 处理开始 ==========
2025-07-29 20:07:01,769 - INFO - 字幕内容: 绝望的新欢为了保住自己的利益，竟丧心病狂地绑架了男人收养的妹妹念念。
2025-07-29 20:07:01,769 - INFO - 字幕序号: [3280, 3284]
2025-07-29 20:07:01,769 - INFO - 音频文件详情:
2025-07-29 20:07:01,769 - INFO -   - 路径: output\74.wav
2025-07-29 20:07:01,769 - INFO -   - 时长: 5.46秒
2025-07-29 20:07:01,770 - INFO -   - 验证音频时长: 5.46秒
2025-07-29 20:07:01,770 - INFO - 字幕时间戳信息:
2025-07-29 20:07:01,770 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:01,770 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:01,770 - INFO -   - 根据生成的音频时长(5.46秒)已调整字幕时间戳
2025-07-29 20:07:01,770 - INFO - ========== 新模式：为字幕 #74 生成4套场景方案 ==========
2025-07-29 20:07:01,770 - INFO - 字幕序号列表: [3280, 3284]
2025-07-29 20:07:01,770 - INFO - 
--- 生成方案 #1：基于字幕序号 #3280 ---
2025-07-29 20:07:01,770 - INFO - 开始为单个字幕序号 #3280 匹配场景，目标时长: 5.46秒
2025-07-29 20:07:01,770 - INFO - 开始查找字幕序号 [3280] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:01,771 - INFO - 找到related_overlap场景: scene_id=2992, 字幕#3280
2025-07-29 20:07:01,771 - INFO - 字幕 #3280 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:01,771 - INFO - 字幕序号 #3280 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:01,771 - INFO - 选择第一个overlap场景作为起点: scene_id=2992
2025-07-29 20:07:01,771 - INFO - 添加起点场景: scene_id=2992, 时长=1.56秒, 累计时长=1.56秒
2025-07-29 20:07:01,771 - INFO - 起点场景时长不足，需要延伸填充 3.90秒
2025-07-29 20:07:01,772 - INFO - 起点场景在原始列表中的索引: 2991
2025-07-29 20:07:01,772 - INFO - 延伸添加场景: scene_id=2993 (完整时长 1.72秒)
2025-07-29 20:07:01,772 - INFO - 累计时长: 3.28秒
2025-07-29 20:07:01,772 - INFO - 延伸添加场景: scene_id=2994 (裁剪至 2.18秒)
2025-07-29 20:07:01,772 - INFO - 累计时长: 5.46秒
2025-07-29 20:07:01,772 - INFO - 字幕序号 #3280 场景匹配完成，共选择 3 个场景，总时长: 5.46秒
2025-07-29 20:07:01,772 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:07:01,772 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:07:01,772 - INFO - 
--- 生成方案 #2：基于字幕序号 #3284 ---
2025-07-29 20:07:01,772 - INFO - 开始为单个字幕序号 #3284 匹配场景，目标时长: 5.46秒
2025-07-29 20:07:01,772 - INFO - 开始查找字幕序号 [3284] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:01,772 - INFO - 找到related_overlap场景: scene_id=2995, 字幕#3284
2025-07-29 20:07:01,773 - INFO - 字幕 #3284 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:01,773 - INFO - 字幕序号 #3284 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:01,773 - INFO - 选择第一个overlap场景作为起点: scene_id=2995
2025-07-29 20:07:01,773 - INFO - 添加起点场景: scene_id=2995, 时长=1.16秒, 累计时长=1.16秒
2025-07-29 20:07:01,773 - INFO - 起点场景时长不足，需要延伸填充 4.30秒
2025-07-29 20:07:01,773 - INFO - 起点场景在原始列表中的索引: 2994
2025-07-29 20:07:01,773 - INFO - 延伸添加场景: scene_id=2996 (完整时长 1.96秒)
2025-07-29 20:07:01,773 - INFO - 累计时长: 3.12秒
2025-07-29 20:07:01,773 - INFO - 延伸添加场景: scene_id=2997 (完整时长 0.68秒)
2025-07-29 20:07:01,773 - INFO - 累计时长: 3.80秒
2025-07-29 20:07:01,773 - INFO - 延伸添加场景: scene_id=2998 (完整时长 0.60秒)
2025-07-29 20:07:01,773 - INFO - 累计时长: 4.40秒
2025-07-29 20:07:01,773 - INFO - 延伸添加场景: scene_id=2999 (完整时长 0.80秒)
2025-07-29 20:07:01,773 - INFO - 累计时长: 5.20秒
2025-07-29 20:07:01,773 - INFO - 延伸添加场景: scene_id=3000 (裁剪至 0.26秒)
2025-07-29 20:07:01,773 - INFO - 累计时长: 5.46秒
2025-07-29 20:07:01,773 - INFO - 字幕序号 #3284 场景匹配完成，共选择 6 个场景，总时长: 5.46秒
2025-07-29 20:07:01,773 - INFO - 方案 #2 生成成功，包含 6 个场景
2025-07-29 20:07:01,773 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:07:01,773 - INFO - ========== 当前模式：为字幕 #74 生成 1 套场景方案 ==========
2025-07-29 20:07:01,773 - INFO - 开始查找字幕序号 [3280, 3284] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:01,774 - INFO - 找到related_overlap场景: scene_id=2992, 字幕#3280
2025-07-29 20:07:01,774 - INFO - 找到related_overlap场景: scene_id=2995, 字幕#3284
2025-07-29 20:07:01,774 - INFO - 字幕 #3280 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:01,774 - INFO - 字幕 #3284 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:01,774 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:07:01,774 - INFO - 开始生成方案 #1
2025-07-29 20:07:01,774 - INFO - 方案 #1: 为字幕#3280选择初始化overlap场景id=2992
2025-07-29 20:07:01,774 - INFO - 方案 #1: 为字幕#3284选择初始化overlap场景id=2995
2025-07-29 20:07:01,774 - INFO - 方案 #1: 初始选择后，当前总时长=2.72秒
2025-07-29 20:07:01,775 - INFO - 方案 #1: 额外between选择后，当前总时长=2.72秒
2025-07-29 20:07:01,775 - INFO - 方案 #1: 场景总时长(2.72秒)小于音频时长(5.46秒)，需要延伸填充
2025-07-29 20:07:01,775 - INFO - 方案 #1: 最后一个场景ID: 2995
2025-07-29 20:07:01,775 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2994
2025-07-29 20:07:01,775 - INFO - 方案 #1: 需要填充时长: 2.74秒
2025-07-29 20:07:01,775 - INFO - 方案 #1: 追加场景 scene_id=2996 (完整时长 1.96秒)
2025-07-29 20:07:01,775 - INFO - 方案 #1: 追加场景 scene_id=2997 (完整时长 0.68秒)
2025-07-29 20:07:01,775 - INFO - 方案 #1: 追加场景 scene_id=2998 (裁剪至 0.10秒)
2025-07-29 20:07:01,775 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:07:01,775 - INFO - 方案 #1 调整/填充后最终总时长: 5.46秒
2025-07-29 20:07:01,775 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:01,775 - INFO - ========== 当前模式：字幕 #74 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:01,775 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:07:01,775 - INFO - ========== 新模式：字幕 #74 共生成 3 套有效场景方案 ==========
2025-07-29 20:07:01,775 - INFO - 
----- 处理字幕 #74 的方案 #1 -----
2025-07-29 20:07:01,775 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 20:07:01,775 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5xovs0bl
2025-07-29 20:07:01,776 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2992.mp4 (确认存在: True)
2025-07-29 20:07:01,776 - INFO - 添加场景ID=2992，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:07:01,776 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2993.mp4 (确认存在: True)
2025-07-29 20:07:01,776 - INFO - 添加场景ID=2993，时长=1.72秒，累计时长=3.28秒
2025-07-29 20:07:01,776 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2994.mp4 (确认存在: True)
2025-07-29 20:07:01,776 - INFO - 添加场景ID=2994，时长=2.28秒，累计时长=5.56秒
2025-07-29 20:07:01,776 - INFO - 准备合并 3 个场景文件，总时长约 5.56秒
2025-07-29 20:07:01,776 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2992.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2993.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2994.mp4'

2025-07-29 20:07:01,776 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5xovs0bl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5xovs0bl\temp_combined.mp4
2025-07-29 20:07:01,919 - INFO - 合并后的视频时长: 5.63秒，目标音频时长: 5.46秒
2025-07-29 20:07:01,919 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5xovs0bl\temp_combined.mp4 -ss 0 -to 5.456 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 20:07:02,251 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:02,251 - INFO - 目标音频时长: 5.46秒
2025-07-29 20:07:02,251 - INFO - 实际视频时长: 5.50秒
2025-07-29 20:07:02,251 - INFO - 时长差异: 0.05秒 (0.86%)
2025-07-29 20:07:02,251 - INFO - ==========================================
2025-07-29 20:07:02,251 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:02,251 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 20:07:02,252 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5xovs0bl
2025-07-29 20:07:02,301 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:02,301 - INFO -   - 音频时长: 5.46秒
2025-07-29 20:07:02,301 - INFO -   - 视频时长: 5.50秒
2025-07-29 20:07:02,301 - INFO -   - 时长差异: 0.05秒 (0.86%)
2025-07-29 20:07:02,301 - INFO - 
----- 处理字幕 #74 的方案 #2 -----
2025-07-29 20:07:02,301 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 20:07:02,301 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_82x703q
2025-07-29 20:07:02,302 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2995.mp4 (确认存在: True)
2025-07-29 20:07:02,302 - INFO - 添加场景ID=2995，时长=1.16秒，累计时长=1.16秒
2025-07-29 20:07:02,302 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2996.mp4 (确认存在: True)
2025-07-29 20:07:02,302 - INFO - 添加场景ID=2996，时长=1.96秒，累计时长=3.12秒
2025-07-29 20:07:02,302 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2997.mp4 (确认存在: True)
2025-07-29 20:07:02,302 - INFO - 添加场景ID=2997，时长=0.68秒，累计时长=3.80秒
2025-07-29 20:07:02,302 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2998.mp4 (确认存在: True)
2025-07-29 20:07:02,302 - INFO - 添加场景ID=2998，时长=0.60秒，累计时长=4.40秒
2025-07-29 20:07:02,302 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2999.mp4 (确认存在: True)
2025-07-29 20:07:02,302 - INFO - 添加场景ID=2999，时长=0.80秒，累计时长=5.20秒
2025-07-29 20:07:02,302 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3000.mp4 (确认存在: True)
2025-07-29 20:07:02,302 - INFO - 添加场景ID=3000，时长=1.44秒，累计时长=6.64秒
2025-07-29 20:07:02,302 - INFO - 准备合并 6 个场景文件，总时长约 6.64秒
2025-07-29 20:07:02,302 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2995.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2996.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2997.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2998.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2999.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3000.mp4'

2025-07-29 20:07:02,303 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_82x703q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_82x703q\temp_combined.mp4
2025-07-29 20:07:02,465 - INFO - 合并后的视频时长: 6.78秒，目标音频时长: 5.46秒
2025-07-29 20:07:02,465 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_82x703q\temp_combined.mp4 -ss 0 -to 5.456 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 20:07:02,815 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:02,815 - INFO - 目标音频时长: 5.46秒
2025-07-29 20:07:02,815 - INFO - 实际视频时长: 5.50秒
2025-07-29 20:07:02,815 - INFO - 时长差异: 0.05秒 (0.86%)
2025-07-29 20:07:02,815 - INFO - ==========================================
2025-07-29 20:07:02,815 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:02,815 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 20:07:02,816 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_82x703q
2025-07-29 20:07:02,861 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:02,861 - INFO -   - 音频时长: 5.46秒
2025-07-29 20:07:02,861 - INFO -   - 视频时长: 5.50秒
2025-07-29 20:07:02,861 - INFO -   - 时长差异: 0.05秒 (0.86%)
2025-07-29 20:07:02,861 - INFO - 
----- 处理字幕 #74 的方案 #3 -----
2025-07-29 20:07:02,861 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 20:07:02,862 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsijr_pdl
2025-07-29 20:07:02,862 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2992.mp4 (确认存在: True)
2025-07-29 20:07:02,862 - INFO - 添加场景ID=2992，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:07:02,862 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2995.mp4 (确认存在: True)
2025-07-29 20:07:02,862 - INFO - 添加场景ID=2995，时长=1.16秒，累计时长=2.72秒
2025-07-29 20:07:02,862 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2996.mp4 (确认存在: True)
2025-07-29 20:07:02,862 - INFO - 添加场景ID=2996，时长=1.96秒，累计时长=4.68秒
2025-07-29 20:07:02,862 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2997.mp4 (确认存在: True)
2025-07-29 20:07:02,862 - INFO - 添加场景ID=2997，时长=0.68秒，累计时长=5.36秒
2025-07-29 20:07:02,862 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2998.mp4 (确认存在: True)
2025-07-29 20:07:02,862 - INFO - 添加场景ID=2998，时长=0.60秒，累计时长=5.96秒
2025-07-29 20:07:02,862 - INFO - 准备合并 5 个场景文件，总时长约 5.96秒
2025-07-29 20:07:02,862 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2992.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2995.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2996.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2997.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2998.mp4'

2025-07-29 20:07:02,863 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsijr_pdl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsijr_pdl\temp_combined.mp4
2025-07-29 20:07:03,014 - INFO - 合并后的视频时长: 6.08秒，目标音频时长: 5.46秒
2025-07-29 20:07:03,014 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsijr_pdl\temp_combined.mp4 -ss 0 -to 5.456 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 20:07:03,357 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:03,357 - INFO - 目标音频时长: 5.46秒
2025-07-29 20:07:03,357 - INFO - 实际视频时长: 5.50秒
2025-07-29 20:07:03,357 - INFO - 时长差异: 0.05秒 (0.86%)
2025-07-29 20:07:03,357 - INFO - ==========================================
2025-07-29 20:07:03,357 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:03,357 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 20:07:03,357 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsijr_pdl
2025-07-29 20:07:03,401 - INFO - 方案 #3 处理完成:
2025-07-29 20:07:03,401 - INFO -   - 音频时长: 5.46秒
2025-07-29 20:07:03,401 - INFO -   - 视频时长: 5.50秒
2025-07-29 20:07:03,402 - INFO -   - 时长差异: 0.05秒 (0.86%)
2025-07-29 20:07:03,402 - INFO - 
字幕 #74 处理完成，成功生成 3/3 套方案
2025-07-29 20:07:03,402 - INFO - 生成的视频文件:
2025-07-29 20:07:03,402 - INFO -   1. F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 20:07:03,402 - INFO -   2. F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 20:07:03,402 - INFO -   3. F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 20:07:03,402 - INFO - ========== 字幕 #74 处理结束 ==========

