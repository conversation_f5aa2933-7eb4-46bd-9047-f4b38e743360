2025-07-29 20:05:42,662 - INFO - ========== 字幕 #19 处理开始 ==========
2025-07-29 20:05:42,662 - INFO - 字幕内容: 她嘱咐哥哥，自己死后，一定要为自己而活，随后便永远地闭上了眼睛。
2025-07-29 20:05:42,663 - INFO - 字幕序号: [202, 208]
2025-07-29 20:05:42,663 - INFO - 音频文件详情:
2025-07-29 20:05:42,663 - INFO -   - 路径: output\19.wav
2025-07-29 20:05:42,663 - INFO -   - 时长: 3.40秒
2025-07-29 20:05:42,663 - INFO -   - 验证音频时长: 3.40秒
2025-07-29 20:05:42,663 - INFO - 字幕时间戳信息:
2025-07-29 20:05:42,663 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:42,663 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:42,663 - INFO -   - 根据生成的音频时长(3.40秒)已调整字幕时间戳
2025-07-29 20:05:42,663 - INFO - ========== 新模式：为字幕 #19 生成4套场景方案 ==========
2025-07-29 20:05:42,663 - INFO - 字幕序号列表: [202, 208]
2025-07-29 20:05:42,663 - INFO - 
--- 生成方案 #1：基于字幕序号 #202 ---
2025-07-29 20:05:42,663 - INFO - 开始为单个字幕序号 #202 匹配场景，目标时长: 3.40秒
2025-07-29 20:05:42,663 - INFO - 开始查找字幕序号 [202] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:42,663 - INFO - 找到related_overlap场景: scene_id=228, 字幕#202
2025-07-29 20:05:42,665 - INFO - 字幕 #202 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:42,665 - INFO - 字幕序号 #202 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:42,665 - INFO - 选择第一个overlap场景作为起点: scene_id=228
2025-07-29 20:05:42,665 - INFO - 添加起点场景: scene_id=228, 时长=2.36秒, 累计时长=2.36秒
2025-07-29 20:05:42,665 - INFO - 起点场景时长不足，需要延伸填充 1.04秒
2025-07-29 20:05:42,665 - INFO - 起点场景在原始列表中的索引: 227
2025-07-29 20:05:42,665 - INFO - 延伸添加场景: scene_id=229 (裁剪至 1.04秒)
2025-07-29 20:05:42,665 - INFO - 累计时长: 3.40秒
2025-07-29 20:05:42,665 - INFO - 字幕序号 #202 场景匹配完成，共选择 2 个场景，总时长: 3.40秒
2025-07-29 20:05:42,665 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:05:42,665 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:05:42,665 - INFO - 
--- 生成方案 #2：基于字幕序号 #208 ---
2025-07-29 20:05:42,665 - INFO - 开始为单个字幕序号 #208 匹配场景，目标时长: 3.40秒
2025-07-29 20:05:42,665 - INFO - 开始查找字幕序号 [208] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:42,665 - INFO - 找到related_overlap场景: scene_id=232, 字幕#208
2025-07-29 20:05:42,666 - INFO - 找到related_between场景: scene_id=233, 字幕#208
2025-07-29 20:05:42,666 - INFO - 字幕 #208 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:42,666 - INFO - 字幕序号 #208 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:42,666 - INFO - 选择第一个overlap场景作为起点: scene_id=232
2025-07-29 20:05:42,666 - INFO - 添加起点场景: scene_id=232, 时长=5.24秒, 累计时长=5.24秒
2025-07-29 20:05:42,666 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:05:42,666 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 20:05:42,666 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:42,666 - INFO - ========== 当前模式：为字幕 #19 生成 1 套场景方案 ==========
2025-07-29 20:05:42,666 - INFO - 开始查找字幕序号 [202, 208] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:42,666 - INFO - 找到related_overlap场景: scene_id=228, 字幕#202
2025-07-29 20:05:42,666 - INFO - 找到related_overlap场景: scene_id=232, 字幕#208
2025-07-29 20:05:42,667 - INFO - 找到related_between场景: scene_id=233, 字幕#208
2025-07-29 20:05:42,668 - INFO - 字幕 #202 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:42,668 - INFO - 字幕 #208 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:42,668 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:05:42,668 - INFO - 开始生成方案 #1
2025-07-29 20:05:42,668 - INFO - 方案 #1: 为字幕#202选择初始化overlap场景id=228
2025-07-29 20:05:42,668 - INFO - 方案 #1: 为字幕#208选择初始化overlap场景id=232
2025-07-29 20:05:42,668 - INFO - 方案 #1: 初始选择后，当前总时长=7.60秒
2025-07-29 20:05:42,668 - INFO - 方案 #1: 额外between选择后，当前总时长=7.60秒
2025-07-29 20:05:42,668 - INFO - 方案 #1: 场景总时长(7.60秒)大于音频时长(3.40秒)，需要裁剪
2025-07-29 20:05:42,668 - INFO - 调整前总时长: 7.60秒, 目标时长: 3.40秒
2025-07-29 20:05:42,668 - INFO - 需要裁剪 4.20秒
2025-07-29 20:05:42,668 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:05:42,668 - INFO - 裁剪场景ID=232：从5.24秒裁剪至1.57秒
2025-07-29 20:05:42,668 - INFO - 裁剪场景ID=228：从2.36秒裁剪至1.83秒
2025-07-29 20:05:42,668 - INFO - 调整后总时长: 3.40秒，与目标时长差异: 0.00秒
2025-07-29 20:05:42,668 - INFO - 方案 #1 调整/填充后最终总时长: 3.40秒
2025-07-29 20:05:42,668 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:42,668 - INFO - ========== 当前模式：字幕 #19 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:42,668 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:42,668 - INFO - ========== 新模式：字幕 #19 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:42,668 - INFO - 
----- 处理字幕 #19 的方案 #1 -----
2025-07-29 20:05:42,668 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 20:05:42,668 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptqpwwesm
2025-07-29 20:05:42,668 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\228.mp4 (确认存在: True)
2025-07-29 20:05:42,668 - INFO - 添加场景ID=228，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:05:42,670 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\229.mp4 (确认存在: True)
2025-07-29 20:05:42,670 - INFO - 添加场景ID=229，时长=3.92秒，累计时长=6.28秒
2025-07-29 20:05:42,670 - INFO - 场景总时长(6.28秒)已达到音频时长(3.40秒)的1.5倍，停止添加场景
2025-07-29 20:05:42,670 - INFO - 准备合并 2 个场景文件，总时长约 6.28秒
2025-07-29 20:05:42,670 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/228.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/229.mp4'

2025-07-29 20:05:42,670 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptqpwwesm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptqpwwesm\temp_combined.mp4
2025-07-29 20:05:42,794 - INFO - 合并后的视频时长: 6.33秒，目标音频时长: 3.40秒
2025-07-29 20:05:42,794 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptqpwwesm\temp_combined.mp4 -ss 0 -to 3.399 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 20:05:43,055 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:43,055 - INFO - 目标音频时长: 3.40秒
2025-07-29 20:05:43,055 - INFO - 实际视频时长: 3.42秒
2025-07-29 20:05:43,055 - INFO - 时长差异: 0.02秒 (0.71%)
2025-07-29 20:05:43,055 - INFO - ==========================================
2025-07-29 20:05:43,055 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:43,055 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 20:05:43,056 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptqpwwesm
2025-07-29 20:05:43,100 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:43,100 - INFO -   - 音频时长: 3.40秒
2025-07-29 20:05:43,100 - INFO -   - 视频时长: 3.42秒
2025-07-29 20:05:43,100 - INFO -   - 时长差异: 0.02秒 (0.71%)
2025-07-29 20:05:43,100 - INFO - 
----- 处理字幕 #19 的方案 #2 -----
2025-07-29 20:05:43,100 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 20:05:43,100 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl6jf3o83
2025-07-29 20:05:43,101 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\232.mp4 (确认存在: True)
2025-07-29 20:05:43,101 - INFO - 添加场景ID=232，时长=5.24秒，累计时长=5.24秒
2025-07-29 20:05:43,101 - INFO - 场景总时长(5.24秒)已达到音频时长(3.40秒)的1.5倍，停止添加场景
2025-07-29 20:05:43,101 - INFO - 准备合并 1 个场景文件，总时长约 5.24秒
2025-07-29 20:05:43,101 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/232.mp4'

2025-07-29 20:05:43,101 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl6jf3o83\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl6jf3o83\temp_combined.mp4
2025-07-29 20:05:43,228 - INFO - 合并后的视频时长: 5.26秒，目标音频时长: 3.40秒
2025-07-29 20:05:43,228 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl6jf3o83\temp_combined.mp4 -ss 0 -to 3.399 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 20:05:43,498 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:43,498 - INFO - 目标音频时长: 3.40秒
2025-07-29 20:05:43,498 - INFO - 实际视频时长: 3.42秒
2025-07-29 20:05:43,498 - INFO - 时长差异: 0.02秒 (0.71%)
2025-07-29 20:05:43,498 - INFO - ==========================================
2025-07-29 20:05:43,498 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:43,498 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 20:05:43,498 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl6jf3o83
2025-07-29 20:05:43,542 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:43,542 - INFO -   - 音频时长: 3.40秒
2025-07-29 20:05:43,542 - INFO -   - 视频时长: 3.42秒
2025-07-29 20:05:43,542 - INFO -   - 时长差异: 0.02秒 (0.71%)
2025-07-29 20:05:43,542 - INFO - 
----- 处理字幕 #19 的方案 #3 -----
2025-07-29 20:05:43,542 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 20:05:43,542 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp032gr3ye
2025-07-29 20:05:43,543 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\228.mp4 (确认存在: True)
2025-07-29 20:05:43,543 - INFO - 添加场景ID=228，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:05:43,543 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\232.mp4 (确认存在: True)
2025-07-29 20:05:43,543 - INFO - 添加场景ID=232，时长=5.24秒，累计时长=7.60秒
2025-07-29 20:05:43,543 - INFO - 场景总时长(7.60秒)已达到音频时长(3.40秒)的1.5倍，停止添加场景
2025-07-29 20:05:43,543 - INFO - 准备合并 2 个场景文件，总时长约 7.60秒
2025-07-29 20:05:43,543 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/228.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/232.mp4'

2025-07-29 20:05:43,543 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp032gr3ye\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp032gr3ye\temp_combined.mp4
2025-07-29 20:05:43,670 - INFO - 合并后的视频时长: 7.65秒，目标音频时长: 3.40秒
2025-07-29 20:05:43,670 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp032gr3ye\temp_combined.mp4 -ss 0 -to 3.399 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 20:05:43,926 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:43,926 - INFO - 目标音频时长: 3.40秒
2025-07-29 20:05:43,926 - INFO - 实际视频时长: 3.42秒
2025-07-29 20:05:43,926 - INFO - 时长差异: 0.02秒 (0.71%)
2025-07-29 20:05:43,926 - INFO - ==========================================
2025-07-29 20:05:43,926 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:43,926 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 20:05:43,927 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp032gr3ye
2025-07-29 20:05:43,970 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:43,970 - INFO -   - 音频时长: 3.40秒
2025-07-29 20:05:43,970 - INFO -   - 视频时长: 3.42秒
2025-07-29 20:05:43,970 - INFO -   - 时长差异: 0.02秒 (0.71%)
2025-07-29 20:05:43,971 - INFO - 
字幕 #19 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:43,971 - INFO - 生成的视频文件:
2025-07-29 20:05:43,971 - INFO -   1. F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 20:05:43,971 - INFO -   2. F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 20:05:43,971 - INFO -   3. F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 20:05:43,971 - INFO - ========== 字幕 #19 处理结束 ==========

