2025-07-29 20:06:47,490 - INFO - ========== 字幕 #64 处理开始 ==========
2025-07-29 20:06:47,490 - INFO - 字幕内容: 男人彻底失望了，他来此的目的，就是想看看五年过去，她是否有所改变，结果，她还是一样。
2025-07-29 20:06:47,490 - INFO - 字幕序号: [2531, 2536]
2025-07-29 20:06:47,490 - INFO - 音频文件详情:
2025-07-29 20:06:47,490 - INFO -   - 路径: output\64.wav
2025-07-29 20:06:47,490 - INFO -   - 时长: 4.53秒
2025-07-29 20:06:47,491 - INFO -   - 验证音频时长: 4.53秒
2025-07-29 20:06:47,491 - INFO - 字幕时间戳信息:
2025-07-29 20:06:47,491 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:47,491 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:47,491 - INFO -   - 根据生成的音频时长(4.53秒)已调整字幕时间戳
2025-07-29 20:06:47,491 - INFO - ========== 新模式：为字幕 #64 生成4套场景方案 ==========
2025-07-29 20:06:47,491 - INFO - 字幕序号列表: [2531, 2536]
2025-07-29 20:06:47,491 - INFO - 
--- 生成方案 #1：基于字幕序号 #2531 ---
2025-07-29 20:06:47,491 - INFO - 开始为单个字幕序号 #2531 匹配场景，目标时长: 4.53秒
2025-07-29 20:06:47,491 - INFO - 开始查找字幕序号 [2531] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:47,491 - INFO - 找到related_overlap场景: scene_id=2387, 字幕#2531
2025-07-29 20:06:47,492 - INFO - 字幕 #2531 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:47,492 - INFO - 字幕序号 #2531 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:47,492 - INFO - 选择第一个overlap场景作为起点: scene_id=2387
2025-07-29 20:06:47,492 - INFO - 添加起点场景: scene_id=2387, 时长=6.20秒, 累计时长=6.20秒
2025-07-29 20:06:47,492 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:47,492 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 20:06:47,492 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 20:06:47,492 - INFO - 
--- 生成方案 #2：基于字幕序号 #2536 ---
2025-07-29 20:06:47,492 - INFO - 开始为单个字幕序号 #2536 匹配场景，目标时长: 4.53秒
2025-07-29 20:06:47,493 - INFO - 开始查找字幕序号 [2536] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:47,493 - INFO - 找到related_overlap场景: scene_id=2389, 字幕#2536
2025-07-29 20:06:47,494 - INFO - 字幕 #2536 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:47,494 - INFO - 字幕序号 #2536 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:47,494 - INFO - 选择第一个overlap场景作为起点: scene_id=2389
2025-07-29 20:06:47,494 - INFO - 添加起点场景: scene_id=2389, 时长=3.68秒, 累计时长=3.68秒
2025-07-29 20:06:47,494 - INFO - 起点场景时长不足，需要延伸填充 0.85秒
2025-07-29 20:06:47,494 - INFO - 起点场景在原始列表中的索引: 2388
2025-07-29 20:06:47,494 - INFO - 延伸添加场景: scene_id=2390 (裁剪至 0.85秒)
2025-07-29 20:06:47,494 - INFO - 累计时长: 4.53秒
2025-07-29 20:06:47,494 - INFO - 字幕序号 #2536 场景匹配完成，共选择 2 个场景，总时长: 4.53秒
2025-07-29 20:06:47,494 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:06:47,494 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:47,494 - INFO - ========== 当前模式：为字幕 #64 生成 1 套场景方案 ==========
2025-07-29 20:06:47,494 - INFO - 开始查找字幕序号 [2531, 2536] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:47,494 - INFO - 找到related_overlap场景: scene_id=2387, 字幕#2531
2025-07-29 20:06:47,494 - INFO - 找到related_overlap场景: scene_id=2389, 字幕#2536
2025-07-29 20:06:47,495 - INFO - 字幕 #2531 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:47,495 - INFO - 字幕 #2536 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:47,495 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:47,495 - INFO - 开始生成方案 #1
2025-07-29 20:06:47,495 - INFO - 方案 #1: 为字幕#2531选择初始化overlap场景id=2387
2025-07-29 20:06:47,495 - INFO - 方案 #1: 为字幕#2536选择初始化overlap场景id=2389
2025-07-29 20:06:47,495 - INFO - 方案 #1: 初始选择后，当前总时长=9.88秒
2025-07-29 20:06:47,495 - INFO - 方案 #1: 额外between选择后，当前总时长=9.88秒
2025-07-29 20:06:47,495 - INFO - 方案 #1: 场景总时长(9.88秒)大于音频时长(4.53秒)，需要裁剪
2025-07-29 20:06:47,495 - INFO - 调整前总时长: 9.88秒, 目标时长: 4.53秒
2025-07-29 20:06:47,495 - INFO - 需要裁剪 5.35秒
2025-07-29 20:06:47,495 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:47,495 - INFO - 裁剪场景ID=2387：从6.20秒裁剪至1.86秒
2025-07-29 20:06:47,495 - INFO - 裁剪场景ID=2389：从3.68秒裁剪至2.67秒
2025-07-29 20:06:47,495 - INFO - 调整后总时长: 4.53秒，与目标时长差异: 0.00秒
2025-07-29 20:06:47,495 - INFO - 方案 #1 调整/填充后最终总时长: 4.53秒
2025-07-29 20:06:47,495 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:47,495 - INFO - ========== 当前模式：字幕 #64 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:47,495 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:47,495 - INFO - ========== 新模式：字幕 #64 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:47,495 - INFO - 
----- 处理字幕 #64 的方案 #1 -----
2025-07-29 20:06:47,495 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 20:06:47,496 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy9c1_2nf
2025-07-29 20:06:47,496 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2387.mp4 (确认存在: True)
2025-07-29 20:06:47,496 - INFO - 添加场景ID=2387，时长=6.20秒，累计时长=6.20秒
2025-07-29 20:06:47,496 - INFO - 准备合并 1 个场景文件，总时长约 6.20秒
2025-07-29 20:06:47,496 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2387.mp4'

2025-07-29 20:06:47,496 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpy9c1_2nf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpy9c1_2nf\temp_combined.mp4
2025-07-29 20:06:47,613 - INFO - 合并后的视频时长: 6.22秒，目标音频时长: 4.53秒
2025-07-29 20:06:47,613 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpy9c1_2nf\temp_combined.mp4 -ss 0 -to 4.534 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 20:06:47,894 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:47,894 - INFO - 目标音频时长: 4.53秒
2025-07-29 20:06:47,894 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:06:47,894 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:06:47,894 - INFO - ==========================================
2025-07-29 20:06:47,894 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:47,894 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 20:06:47,895 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy9c1_2nf
2025-07-29 20:06:47,939 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:47,939 - INFO -   - 音频时长: 4.53秒
2025-07-29 20:06:47,939 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:06:47,939 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:06:47,939 - INFO - 
----- 处理字幕 #64 的方案 #2 -----
2025-07-29 20:06:47,939 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 20:06:47,939 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphumq273b
2025-07-29 20:06:47,940 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2389.mp4 (确认存在: True)
2025-07-29 20:06:47,940 - INFO - 添加场景ID=2389，时长=3.68秒，累计时长=3.68秒
2025-07-29 20:06:47,940 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2390.mp4 (确认存在: True)
2025-07-29 20:06:47,940 - INFO - 添加场景ID=2390，时长=2.00秒，累计时长=5.68秒
2025-07-29 20:06:47,940 - INFO - 准备合并 2 个场景文件，总时长约 5.68秒
2025-07-29 20:06:47,940 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2389.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2390.mp4'

2025-07-29 20:06:47,940 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphumq273b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphumq273b\temp_combined.mp4
2025-07-29 20:06:48,065 - INFO - 合并后的视频时长: 5.71秒，目标音频时长: 4.53秒
2025-07-29 20:06:48,065 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphumq273b\temp_combined.mp4 -ss 0 -to 4.534 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 20:06:48,364 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:48,364 - INFO - 目标音频时长: 4.53秒
2025-07-29 20:06:48,364 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:06:48,364 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:06:48,364 - INFO - ==========================================
2025-07-29 20:06:48,364 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:48,364 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 20:06:48,365 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphumq273b
2025-07-29 20:06:48,410 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:48,410 - INFO -   - 音频时长: 4.53秒
2025-07-29 20:06:48,410 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:06:48,410 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:06:48,410 - INFO - 
----- 处理字幕 #64 的方案 #3 -----
2025-07-29 20:06:48,410 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 20:06:48,410 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3sfmkb79
2025-07-29 20:06:48,411 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2387.mp4 (确认存在: True)
2025-07-29 20:06:48,411 - INFO - 添加场景ID=2387，时长=6.20秒，累计时长=6.20秒
2025-07-29 20:06:48,411 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2389.mp4 (确认存在: True)
2025-07-29 20:06:48,411 - INFO - 添加场景ID=2389，时长=3.68秒，累计时长=9.88秒
2025-07-29 20:06:48,411 - INFO - 场景总时长(9.88秒)已达到音频时长(4.53秒)的1.5倍，停止添加场景
2025-07-29 20:06:48,411 - INFO - 准备合并 2 个场景文件，总时长约 9.88秒
2025-07-29 20:06:48,411 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2387.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2389.mp4'

2025-07-29 20:06:48,411 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3sfmkb79\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3sfmkb79\temp_combined.mp4
2025-07-29 20:06:48,537 - INFO - 合并后的视频时长: 9.93秒，目标音频时长: 4.53秒
2025-07-29 20:06:48,537 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3sfmkb79\temp_combined.mp4 -ss 0 -to 4.534 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 20:06:48,841 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:48,841 - INFO - 目标音频时长: 4.53秒
2025-07-29 20:06:48,841 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:06:48,841 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:06:48,841 - INFO - ==========================================
2025-07-29 20:06:48,841 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:48,842 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 20:06:48,842 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3sfmkb79
2025-07-29 20:06:48,892 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:48,892 - INFO -   - 音频时长: 4.53秒
2025-07-29 20:06:48,892 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:06:48,892 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:06:48,893 - INFO - 
字幕 #64 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:48,893 - INFO - 生成的视频文件:
2025-07-29 20:06:48,893 - INFO -   1. F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 20:06:48,893 - INFO -   2. F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 20:06:48,893 - INFO -   3. F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 20:06:48,893 - INFO - ========== 字幕 #64 处理结束 ==========

