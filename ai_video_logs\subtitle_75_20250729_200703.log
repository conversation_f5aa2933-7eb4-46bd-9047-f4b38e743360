2025-07-29 20:07:03,402 - INFO - ========== 字幕 #75 处理开始 ==========
2025-07-29 20:07:03,402 - INFO - 字幕内容: 危急关头，女人赶到，看着被刀挟持的念念，她第一次感到了真正的恐惧。
2025-07-29 20:07:03,402 - INFO - 字幕序号: [3959, 3963]
2025-07-29 20:07:03,403 - INFO - 音频文件详情:
2025-07-29 20:07:03,403 - INFO -   - 路径: output\75.wav
2025-07-29 20:07:03,403 - INFO -   - 时长: 4.50秒
2025-07-29 20:07:03,403 - INFO -   - 验证音频时长: 4.50秒
2025-07-29 20:07:03,403 - INFO - 字幕时间戳信息:
2025-07-29 20:07:03,413 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:03,413 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:03,413 - INFO -   - 根据生成的音频时长(4.50秒)已调整字幕时间戳
2025-07-29 20:07:03,413 - INFO - ========== 新模式：为字幕 #75 生成4套场景方案 ==========
2025-07-29 20:07:03,413 - INFO - 字幕序号列表: [3959, 3963]
2025-07-29 20:07:03,413 - INFO - 
--- 生成方案 #1：基于字幕序号 #3959 ---
2025-07-29 20:07:03,413 - INFO - 开始为单个字幕序号 #3959 匹配场景，目标时长: 4.50秒
2025-07-29 20:07:03,413 - INFO - 开始查找字幕序号 [3959] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:03,414 - INFO - 找到related_overlap场景: scene_id=3526, 字幕#3959
2025-07-29 20:07:03,414 - INFO - 找到related_between场景: scene_id=3527, 字幕#3959
2025-07-29 20:07:03,414 - INFO - 找到related_between场景: scene_id=3528, 字幕#3959
2025-07-29 20:07:03,414 - INFO - 找到related_between场景: scene_id=3529, 字幕#3959
2025-07-29 20:07:03,414 - INFO - 字幕 #3959 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:07:03,414 - INFO - 字幕序号 #3959 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 20:07:03,414 - INFO - 选择第一个overlap场景作为起点: scene_id=3526
2025-07-29 20:07:03,414 - INFO - 添加起点场景: scene_id=3526, 时长=2.32秒, 累计时长=2.32秒
2025-07-29 20:07:03,414 - INFO - 起点场景时长不足，需要延伸填充 2.18秒
2025-07-29 20:07:03,415 - INFO - 起点场景在原始列表中的索引: 3525
2025-07-29 20:07:03,415 - INFO - 延伸添加场景: scene_id=3527 (完整时长 1.52秒)
2025-07-29 20:07:03,415 - INFO - 累计时长: 3.84秒
2025-07-29 20:07:03,415 - INFO - 延伸添加场景: scene_id=3528 (完整时长 0.60秒)
2025-07-29 20:07:03,415 - INFO - 累计时长: 4.44秒
2025-07-29 20:07:03,415 - INFO - 延伸添加场景: scene_id=3529 (裁剪至 0.06秒)
2025-07-29 20:07:03,415 - INFO - 累计时长: 4.50秒
2025-07-29 20:07:03,415 - INFO - 字幕序号 #3959 场景匹配完成，共选择 4 个场景，总时长: 4.50秒
2025-07-29 20:07:03,415 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:07:03,415 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:07:03,415 - INFO - 
--- 生成方案 #2：基于字幕序号 #3963 ---
2025-07-29 20:07:03,415 - INFO - 开始为单个字幕序号 #3963 匹配场景，目标时长: 4.50秒
2025-07-29 20:07:03,415 - INFO - 开始查找字幕序号 [3963] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:03,416 - INFO - 找到related_overlap场景: scene_id=3533, 字幕#3963
2025-07-29 20:07:03,416 - INFO - 字幕 #3963 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:03,416 - INFO - 字幕序号 #3963 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:03,416 - INFO - 选择第一个overlap场景作为起点: scene_id=3533
2025-07-29 20:07:03,416 - INFO - 添加起点场景: scene_id=3533, 时长=3.04秒, 累计时长=3.04秒
2025-07-29 20:07:03,416 - INFO - 起点场景时长不足，需要延伸填充 1.46秒
2025-07-29 20:07:03,417 - INFO - 起点场景在原始列表中的索引: 3532
2025-07-29 20:07:03,417 - INFO - 延伸添加场景: scene_id=3534 (裁剪至 1.46秒)
2025-07-29 20:07:03,417 - INFO - 累计时长: 4.50秒
2025-07-29 20:07:03,417 - INFO - 字幕序号 #3963 场景匹配完成，共选择 2 个场景，总时长: 4.50秒
2025-07-29 20:07:03,417 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:07:03,417 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:07:03,417 - INFO - ========== 当前模式：为字幕 #75 生成 1 套场景方案 ==========
2025-07-29 20:07:03,417 - INFO - 开始查找字幕序号 [3959, 3963] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:03,417 - INFO - 找到related_overlap场景: scene_id=3526, 字幕#3959
2025-07-29 20:07:03,417 - INFO - 找到related_overlap场景: scene_id=3533, 字幕#3963
2025-07-29 20:07:03,418 - INFO - 找到related_between场景: scene_id=3527, 字幕#3959
2025-07-29 20:07:03,418 - INFO - 找到related_between场景: scene_id=3528, 字幕#3959
2025-07-29 20:07:03,418 - INFO - 找到related_between场景: scene_id=3529, 字幕#3959
2025-07-29 20:07:03,418 - INFO - 字幕 #3959 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:07:03,418 - INFO - 字幕 #3963 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:03,418 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:07:03,418 - INFO - 开始生成方案 #1
2025-07-29 20:07:03,418 - INFO - 方案 #1: 为字幕#3959选择初始化overlap场景id=3526
2025-07-29 20:07:03,418 - INFO - 方案 #1: 为字幕#3963选择初始化overlap场景id=3533
2025-07-29 20:07:03,418 - INFO - 方案 #1: 初始选择后，当前总时长=5.36秒
2025-07-29 20:07:03,418 - INFO - 方案 #1: 额外between选择后，当前总时长=5.36秒
2025-07-29 20:07:03,418 - INFO - 方案 #1: 场景总时长(5.36秒)大于音频时长(4.50秒)，需要裁剪
2025-07-29 20:07:03,418 - INFO - 调整前总时长: 5.36秒, 目标时长: 4.50秒
2025-07-29 20:07:03,418 - INFO - 需要裁剪 0.86秒
2025-07-29 20:07:03,418 - INFO - 裁剪最长场景ID=3533：从3.04秒裁剪至2.18秒
2025-07-29 20:07:03,418 - INFO - 调整后总时长: 4.50秒，与目标时长差异: 0.00秒
2025-07-29 20:07:03,418 - INFO - 方案 #1 调整/填充后最终总时长: 4.50秒
2025-07-29 20:07:03,418 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:03,418 - INFO - ========== 当前模式：字幕 #75 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:03,418 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:07:03,418 - INFO - ========== 新模式：字幕 #75 共生成 3 套有效场景方案 ==========
2025-07-29 20:07:03,418 - INFO - 
----- 处理字幕 #75 的方案 #1 -----
2025-07-29 20:07:03,418 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\75_1.mp4
2025-07-29 20:07:03,418 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuzwwfp6h
2025-07-29 20:07:03,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3526.mp4 (确认存在: True)
2025-07-29 20:07:03,419 - INFO - 添加场景ID=3526，时长=2.32秒，累计时长=2.32秒
2025-07-29 20:07:03,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3527.mp4 (确认存在: True)
2025-07-29 20:07:03,419 - INFO - 添加场景ID=3527，时长=1.52秒，累计时长=3.84秒
2025-07-29 20:07:03,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3528.mp4 (确认存在: True)
2025-07-29 20:07:03,419 - INFO - 添加场景ID=3528，时长=0.60秒，累计时长=4.44秒
2025-07-29 20:07:03,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3529.mp4 (确认存在: True)
2025-07-29 20:07:03,419 - INFO - 添加场景ID=3529，时长=0.76秒，累计时长=5.20秒
2025-07-29 20:07:03,419 - INFO - 准备合并 4 个场景文件，总时长约 5.20秒
2025-07-29 20:07:03,419 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3527.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3528.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3529.mp4'

2025-07-29 20:07:03,419 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuzwwfp6h\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuzwwfp6h\temp_combined.mp4
2025-07-29 20:07:03,568 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.50秒
2025-07-29 20:07:03,568 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuzwwfp6h\temp_combined.mp4 -ss 0 -to 4.498 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\75_1.mp4
2025-07-29 20:07:03,859 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:03,859 - INFO - 目标音频时长: 4.50秒
2025-07-29 20:07:03,859 - INFO - 实际视频时长: 4.54秒
2025-07-29 20:07:03,859 - INFO - 时长差异: 0.04秒 (1.00%)
2025-07-29 20:07:03,859 - INFO - ==========================================
2025-07-29 20:07:03,859 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:03,859 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\75_1.mp4
2025-07-29 20:07:03,859 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuzwwfp6h
2025-07-29 20:07:03,904 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:03,904 - INFO -   - 音频时长: 4.50秒
2025-07-29 20:07:03,904 - INFO -   - 视频时长: 4.54秒
2025-07-29 20:07:03,904 - INFO -   - 时长差异: 0.04秒 (1.00%)
2025-07-29 20:07:03,904 - INFO - 
----- 处理字幕 #75 的方案 #2 -----
2025-07-29 20:07:03,904 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\75_2.mp4
2025-07-29 20:07:03,904 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo_mj_bl8
2025-07-29 20:07:03,905 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3533.mp4 (确认存在: True)
2025-07-29 20:07:03,905 - INFO - 添加场景ID=3533，时长=3.04秒，累计时长=3.04秒
2025-07-29 20:07:03,905 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3534.mp4 (确认存在: True)
2025-07-29 20:07:03,905 - INFO - 添加场景ID=3534，时长=3.16秒，累计时长=6.20秒
2025-07-29 20:07:03,905 - INFO - 准备合并 2 个场景文件，总时长约 6.20秒
2025-07-29 20:07:03,905 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3533.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3534.mp4'

2025-07-29 20:07:03,905 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpo_mj_bl8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpo_mj_bl8\temp_combined.mp4
2025-07-29 20:07:04,025 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 4.50秒
2025-07-29 20:07:04,025 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpo_mj_bl8\temp_combined.mp4 -ss 0 -to 4.498 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\75_2.mp4
2025-07-29 20:07:04,336 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:04,336 - INFO - 目标音频时长: 4.50秒
2025-07-29 20:07:04,336 - INFO - 实际视频时长: 4.54秒
2025-07-29 20:07:04,336 - INFO - 时长差异: 0.04秒 (1.00%)
2025-07-29 20:07:04,336 - INFO - ==========================================
2025-07-29 20:07:04,336 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:04,336 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\75_2.mp4
2025-07-29 20:07:04,336 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo_mj_bl8
2025-07-29 20:07:04,383 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:04,383 - INFO -   - 音频时长: 4.50秒
2025-07-29 20:07:04,383 - INFO -   - 视频时长: 4.54秒
2025-07-29 20:07:04,383 - INFO -   - 时长差异: 0.04秒 (1.00%)
2025-07-29 20:07:04,383 - INFO - 
----- 处理字幕 #75 的方案 #3 -----
2025-07-29 20:07:04,383 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\75_3.mp4
2025-07-29 20:07:04,384 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplhin8weg
2025-07-29 20:07:04,384 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3526.mp4 (确认存在: True)
2025-07-29 20:07:04,384 - INFO - 添加场景ID=3526，时长=2.32秒，累计时长=2.32秒
2025-07-29 20:07:04,384 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3533.mp4 (确认存在: True)
2025-07-29 20:07:04,384 - INFO - 添加场景ID=3533，时长=3.04秒，累计时长=5.36秒
2025-07-29 20:07:04,384 - INFO - 准备合并 2 个场景文件，总时长约 5.36秒
2025-07-29 20:07:04,385 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3533.mp4'

2025-07-29 20:07:04,385 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplhin8weg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplhin8weg\temp_combined.mp4
2025-07-29 20:07:04,494 - INFO - 合并后的视频时长: 5.41秒，目标音频时长: 4.50秒
2025-07-29 20:07:04,494 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplhin8weg\temp_combined.mp4 -ss 0 -to 4.498 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\75_3.mp4
2025-07-29 20:07:04,783 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:04,783 - INFO - 目标音频时长: 4.50秒
2025-07-29 20:07:04,783 - INFO - 实际视频时长: 4.54秒
2025-07-29 20:07:04,783 - INFO - 时长差异: 0.04秒 (1.00%)
2025-07-29 20:07:04,784 - INFO - ==========================================
2025-07-29 20:07:04,784 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:04,784 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\75_3.mp4
2025-07-29 20:07:04,784 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplhin8weg
2025-07-29 20:07:04,828 - INFO - 方案 #3 处理完成:
2025-07-29 20:07:04,828 - INFO -   - 音频时长: 4.50秒
2025-07-29 20:07:04,828 - INFO -   - 视频时长: 4.54秒
2025-07-29 20:07:04,828 - INFO -   - 时长差异: 0.04秒 (1.00%)
2025-07-29 20:07:04,828 - INFO - 
字幕 #75 处理完成，成功生成 3/3 套方案
2025-07-29 20:07:04,828 - INFO - 生成的视频文件:
2025-07-29 20:07:04,828 - INFO -   1. F:/github/aicut_auto/newcut_ai\75_1.mp4
2025-07-29 20:07:04,828 - INFO -   2. F:/github/aicut_auto/newcut_ai\75_2.mp4
2025-07-29 20:07:04,828 - INFO -   3. F:/github/aicut_auto/newcut_ai\75_3.mp4
2025-07-29 20:07:04,828 - INFO - ========== 字幕 #75 处理结束 ==========

