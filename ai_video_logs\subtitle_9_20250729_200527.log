2025-07-29 20:05:27,368 - INFO - ========== 字幕 #9 处理开始 ==========
2025-07-29 20:05:27,368 - INFO - 字幕内容: 他将自己七年来所有的工资都上交给了女人，此刻只求她能把钱还回来，救妹妹一命！
2025-07-29 20:05:27,368 - INFO - 字幕序号: [162, 165]
2025-07-29 20:05:27,368 - INFO - 音频文件详情:
2025-07-29 20:05:27,368 - INFO -   - 路径: output\9.wav
2025-07-29 20:05:27,368 - INFO -   - 时长: 4.63秒
2025-07-29 20:05:27,368 - INFO -   - 验证音频时长: 4.63秒
2025-07-29 20:05:27,368 - INFO - 字幕时间戳信息:
2025-07-29 20:05:27,377 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:27,377 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:27,377 - INFO -   - 根据生成的音频时长(4.63秒)已调整字幕时间戳
2025-07-29 20:05:27,377 - INFO - ========== 新模式：为字幕 #9 生成4套场景方案 ==========
2025-07-29 20:05:27,377 - INFO - 字幕序号列表: [162, 165]
2025-07-29 20:05:27,377 - INFO - 
--- 生成方案 #1：基于字幕序号 #162 ---
2025-07-29 20:05:27,377 - INFO - 开始为单个字幕序号 #162 匹配场景，目标时长: 4.63秒
2025-07-29 20:05:27,377 - INFO - 开始查找字幕序号 [162] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:27,377 - INFO - 找到related_overlap场景: scene_id=183, 字幕#162
2025-07-29 20:05:27,377 - INFO - 找到related_overlap场景: scene_id=184, 字幕#162
2025-07-29 20:05:27,379 - INFO - 字幕 #162 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:27,379 - INFO - 字幕序号 #162 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:27,379 - INFO - 选择第一个overlap场景作为起点: scene_id=183
2025-07-29 20:05:27,379 - INFO - 添加起点场景: scene_id=183, 时长=3.44秒, 累计时长=3.44秒
2025-07-29 20:05:27,379 - INFO - 起点场景时长不足，需要延伸填充 1.19秒
2025-07-29 20:05:27,380 - INFO - 起点场景在原始列表中的索引: 182
2025-07-29 20:05:27,380 - INFO - 延伸添加场景: scene_id=184 (裁剪至 1.19秒)
2025-07-29 20:05:27,380 - INFO - 累计时长: 4.63秒
2025-07-29 20:05:27,380 - INFO - 字幕序号 #162 场景匹配完成，共选择 2 个场景，总时长: 4.63秒
2025-07-29 20:05:27,380 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:05:27,380 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:05:27,380 - INFO - 
--- 生成方案 #2：基于字幕序号 #165 ---
2025-07-29 20:05:27,380 - INFO - 开始为单个字幕序号 #165 匹配场景，目标时长: 4.63秒
2025-07-29 20:05:27,380 - INFO - 开始查找字幕序号 [165] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:27,380 - INFO - 找到related_overlap场景: scene_id=185, 字幕#165
2025-07-29 20:05:27,380 - INFO - 找到related_overlap场景: scene_id=186, 字幕#165
2025-07-29 20:05:27,380 - INFO - 字幕 #165 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:27,380 - INFO - 字幕序号 #165 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:27,380 - INFO - 选择第一个overlap场景作为起点: scene_id=185
2025-07-29 20:05:27,380 - INFO - 添加起点场景: scene_id=185, 时长=1.48秒, 累计时长=1.48秒
2025-07-29 20:05:27,380 - INFO - 起点场景时长不足，需要延伸填充 3.15秒
2025-07-29 20:05:27,380 - INFO - 起点场景在原始列表中的索引: 184
2025-07-29 20:05:27,380 - INFO - 延伸添加场景: scene_id=186 (完整时长 2.40秒)
2025-07-29 20:05:27,380 - INFO - 累计时长: 3.88秒
2025-07-29 20:05:27,380 - INFO - 延伸添加场景: scene_id=187 (裁剪至 0.75秒)
2025-07-29 20:05:27,380 - INFO - 累计时长: 4.63秒
2025-07-29 20:05:27,380 - INFO - 字幕序号 #165 场景匹配完成，共选择 3 个场景，总时长: 4.63秒
2025-07-29 20:05:27,380 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:27,380 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:27,381 - INFO - ========== 当前模式：为字幕 #9 生成 1 套场景方案 ==========
2025-07-29 20:05:27,381 - INFO - 开始查找字幕序号 [162, 165] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:27,381 - INFO - 找到related_overlap场景: scene_id=183, 字幕#162
2025-07-29 20:05:27,381 - INFO - 找到related_overlap场景: scene_id=184, 字幕#162
2025-07-29 20:05:27,381 - INFO - 找到related_overlap场景: scene_id=185, 字幕#165
2025-07-29 20:05:27,381 - INFO - 找到related_overlap场景: scene_id=186, 字幕#165
2025-07-29 20:05:27,382 - INFO - 字幕 #162 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:27,382 - INFO - 字幕 #165 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:27,382 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:27,382 - INFO - 开始生成方案 #1
2025-07-29 20:05:27,382 - INFO - 方案 #1: 为字幕#162选择初始化overlap场景id=183
2025-07-29 20:05:27,382 - INFO - 方案 #1: 为字幕#165选择初始化overlap场景id=186
2025-07-29 20:05:27,382 - INFO - 方案 #1: 初始选择后，当前总时长=5.84秒
2025-07-29 20:05:27,382 - INFO - 方案 #1: 额外between选择后，当前总时长=5.84秒
2025-07-29 20:05:27,382 - INFO - 方案 #1: 场景总时长(5.84秒)大于音频时长(4.63秒)，需要裁剪
2025-07-29 20:05:27,382 - INFO - 调整前总时长: 5.84秒, 目标时长: 4.63秒
2025-07-29 20:05:27,382 - INFO - 需要裁剪 1.21秒
2025-07-29 20:05:27,382 - INFO - 裁剪最长场景ID=183：从3.44秒裁剪至2.23秒
2025-07-29 20:05:27,382 - INFO - 调整后总时长: 4.63秒，与目标时长差异: 0.00秒
2025-07-29 20:05:27,382 - INFO - 方案 #1 调整/填充后最终总时长: 4.63秒
2025-07-29 20:05:27,382 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:27,382 - INFO - ========== 当前模式：字幕 #9 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:27,382 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:27,382 - INFO - ========== 新模式：字幕 #9 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:27,382 - INFO - 
----- 处理字幕 #9 的方案 #1 -----
2025-07-29 20:05:27,382 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 20:05:27,382 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuwzivjbq
2025-07-29 20:05:27,383 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\183.mp4 (确认存在: True)
2025-07-29 20:05:27,383 - INFO - 添加场景ID=183，时长=3.44秒，累计时长=3.44秒
2025-07-29 20:05:27,383 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\184.mp4 (确认存在: True)
2025-07-29 20:05:27,383 - INFO - 添加场景ID=184，时长=1.76秒，累计时长=5.20秒
2025-07-29 20:05:27,383 - INFO - 准备合并 2 个场景文件，总时长约 5.20秒
2025-07-29 20:05:27,383 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/183.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/184.mp4'

2025-07-29 20:05:27,383 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuwzivjbq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuwzivjbq\temp_combined.mp4
2025-07-29 20:05:27,509 - INFO - 合并后的视频时长: 5.25秒，目标音频时长: 4.63秒
2025-07-29 20:05:27,509 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuwzivjbq\temp_combined.mp4 -ss 0 -to 4.632 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 20:05:27,821 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:27,821 - INFO - 目标音频时长: 4.63秒
2025-07-29 20:05:27,821 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:05:27,821 - INFO - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:05:27,821 - INFO - ==========================================
2025-07-29 20:05:27,821 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:27,821 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 20:05:27,822 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuwzivjbq
2025-07-29 20:05:27,866 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:27,866 - INFO -   - 音频时长: 4.63秒
2025-07-29 20:05:27,866 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:05:27,866 - INFO -   - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:05:27,866 - INFO - 
----- 处理字幕 #9 的方案 #2 -----
2025-07-29 20:05:27,866 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 20:05:27,866 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp36q6wjsc
2025-07-29 20:05:27,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\185.mp4 (确认存在: True)
2025-07-29 20:05:27,867 - INFO - 添加场景ID=185，时长=1.48秒，累计时长=1.48秒
2025-07-29 20:05:27,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\186.mp4 (确认存在: True)
2025-07-29 20:05:27,867 - INFO - 添加场景ID=186，时长=2.40秒，累计时长=3.88秒
2025-07-29 20:05:27,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\187.mp4 (确认存在: True)
2025-07-29 20:05:27,867 - INFO - 添加场景ID=187，时长=0.80秒，累计时长=4.68秒
2025-07-29 20:05:27,867 - INFO - 准备合并 3 个场景文件，总时长约 4.68秒
2025-07-29 20:05:27,867 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/185.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/186.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/187.mp4'

2025-07-29 20:05:27,867 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp36q6wjsc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp36q6wjsc\temp_combined.mp4
2025-07-29 20:05:27,997 - INFO - 合并后的视频时长: 4.75秒，目标音频时长: 4.63秒
2025-07-29 20:05:27,998 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp36q6wjsc\temp_combined.mp4 -ss 0 -to 4.632 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 20:05:28,290 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:28,290 - INFO - 目标音频时长: 4.63秒
2025-07-29 20:05:28,290 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:05:28,290 - INFO - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:05:28,290 - INFO - ==========================================
2025-07-29 20:05:28,291 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:28,291 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 20:05:28,291 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp36q6wjsc
2025-07-29 20:05:28,336 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:28,336 - INFO -   - 音频时长: 4.63秒
2025-07-29 20:05:28,336 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:05:28,336 - INFO -   - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:05:28,336 - INFO - 
----- 处理字幕 #9 的方案 #3 -----
2025-07-29 20:05:28,336 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 20:05:28,336 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq6pcvs_8
2025-07-29 20:05:28,337 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\183.mp4 (确认存在: True)
2025-07-29 20:05:28,337 - INFO - 添加场景ID=183，时长=3.44秒，累计时长=3.44秒
2025-07-29 20:05:28,337 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\186.mp4 (确认存在: True)
2025-07-29 20:05:28,337 - INFO - 添加场景ID=186，时长=2.40秒，累计时长=5.84秒
2025-07-29 20:05:28,337 - INFO - 准备合并 2 个场景文件，总时长约 5.84秒
2025-07-29 20:05:28,337 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/183.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/186.mp4'

2025-07-29 20:05:28,337 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq6pcvs_8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq6pcvs_8\temp_combined.mp4
2025-07-29 20:05:28,460 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 4.63秒
2025-07-29 20:05:28,460 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq6pcvs_8\temp_combined.mp4 -ss 0 -to 4.632 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 20:05:28,769 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:28,769 - INFO - 目标音频时长: 4.63秒
2025-07-29 20:05:28,769 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:05:28,769 - INFO - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:05:28,769 - INFO - ==========================================
2025-07-29 20:05:28,769 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:28,769 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 20:05:28,770 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq6pcvs_8
2025-07-29 20:05:28,814 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:28,814 - INFO -   - 音频时长: 4.63秒
2025-07-29 20:05:28,814 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:05:28,814 - INFO -   - 时长差异: 0.03秒 (0.67%)
2025-07-29 20:05:28,814 - INFO - 
字幕 #9 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:28,814 - INFO - 生成的视频文件:
2025-07-29 20:05:28,814 - INFO -   1. F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 20:05:28,814 - INFO -   2. F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 20:05:28,814 - INFO -   3. F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 20:05:28,814 - INFO - ========== 字幕 #9 处理结束 ==========

