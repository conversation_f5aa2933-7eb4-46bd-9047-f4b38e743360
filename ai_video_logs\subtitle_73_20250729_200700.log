2025-07-29 20:07:00,321 - INFO - ========== 字幕 #73 处理开始 ==========
2025-07-29 20:07:00,321 - INFO - 字幕内容: 男人当众承认将与南宫小姐结婚，给了女人和她家人最沉重的一击。
2025-07-29 20:07:00,321 - INFO - 字幕序号: [3022, 3027]
2025-07-29 20:07:00,321 - INFO - 音频文件详情:
2025-07-29 20:07:00,321 - INFO -   - 路径: output\73.wav
2025-07-29 20:07:00,321 - INFO -   - 时长: 4.62秒
2025-07-29 20:07:00,321 - INFO -   - 验证音频时长: 4.62秒
2025-07-29 20:07:00,321 - INFO - 字幕时间戳信息:
2025-07-29 20:07:00,330 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:00,330 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:00,330 - INFO -   - 根据生成的音频时长(4.62秒)已调整字幕时间戳
2025-07-29 20:07:00,330 - INFO - ========== 新模式：为字幕 #73 生成4套场景方案 ==========
2025-07-29 20:07:00,330 - INFO - 字幕序号列表: [3022, 3027]
2025-07-29 20:07:00,330 - INFO - 
--- 生成方案 #1：基于字幕序号 #3022 ---
2025-07-29 20:07:00,330 - INFO - 开始为单个字幕序号 #3022 匹配场景，目标时长: 4.62秒
2025-07-29 20:07:00,330 - INFO - 开始查找字幕序号 [3022] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:00,330 - INFO - 找到related_overlap场景: scene_id=2779, 字幕#3022
2025-07-29 20:07:00,331 - INFO - 找到related_overlap场景: scene_id=2780, 字幕#3022
2025-07-29 20:07:00,331 - INFO - 字幕 #3022 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:07:00,331 - INFO - 字幕序号 #3022 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:00,331 - INFO - 选择第一个overlap场景作为起点: scene_id=2779
2025-07-29 20:07:00,331 - INFO - 添加起点场景: scene_id=2779, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 20:07:00,331 - INFO - 起点场景时长不足，需要延伸填充 2.74秒
2025-07-29 20:07:00,332 - INFO - 起点场景在原始列表中的索引: 2778
2025-07-29 20:07:00,332 - INFO - 延伸添加场景: scene_id=2780 (裁剪至 2.74秒)
2025-07-29 20:07:00,332 - INFO - 累计时长: 4.62秒
2025-07-29 20:07:00,332 - INFO - 字幕序号 #3022 场景匹配完成，共选择 2 个场景，总时长: 4.62秒
2025-07-29 20:07:00,332 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:07:00,332 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:07:00,332 - INFO - 
--- 生成方案 #2：基于字幕序号 #3027 ---
2025-07-29 20:07:00,332 - INFO - 开始为单个字幕序号 #3027 匹配场景，目标时长: 4.62秒
2025-07-29 20:07:00,332 - INFO - 开始查找字幕序号 [3027] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:00,332 - INFO - 找到related_overlap场景: scene_id=2784, 字幕#3027
2025-07-29 20:07:00,333 - INFO - 字幕 #3027 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:00,333 - INFO - 字幕序号 #3027 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:00,333 - INFO - 选择第一个overlap场景作为起点: scene_id=2784
2025-07-29 20:07:00,333 - INFO - 添加起点场景: scene_id=2784, 时长=2.44秒, 累计时长=2.44秒
2025-07-29 20:07:00,333 - INFO - 起点场景时长不足，需要延伸填充 2.18秒
2025-07-29 20:07:00,333 - INFO - 起点场景在原始列表中的索引: 2783
2025-07-29 20:07:00,333 - INFO - 延伸添加场景: scene_id=2785 (裁剪至 2.18秒)
2025-07-29 20:07:00,333 - INFO - 累计时长: 4.62秒
2025-07-29 20:07:00,333 - INFO - 字幕序号 #3027 场景匹配完成，共选择 2 个场景，总时长: 4.62秒
2025-07-29 20:07:00,333 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:07:00,333 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:07:00,333 - INFO - ========== 当前模式：为字幕 #73 生成 1 套场景方案 ==========
2025-07-29 20:07:00,333 - INFO - 开始查找字幕序号 [3022, 3027] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:00,334 - INFO - 找到related_overlap场景: scene_id=2779, 字幕#3022
2025-07-29 20:07:00,334 - INFO - 找到related_overlap场景: scene_id=2780, 字幕#3022
2025-07-29 20:07:00,334 - INFO - 找到related_overlap场景: scene_id=2784, 字幕#3027
2025-07-29 20:07:00,334 - INFO - 字幕 #3022 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:07:00,334 - INFO - 字幕 #3027 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:00,334 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:07:00,334 - INFO - 开始生成方案 #1
2025-07-29 20:07:00,334 - INFO - 方案 #1: 为字幕#3022选择初始化overlap场景id=2779
2025-07-29 20:07:00,334 - INFO - 方案 #1: 为字幕#3027选择初始化overlap场景id=2784
2025-07-29 20:07:00,334 - INFO - 方案 #1: 初始选择后，当前总时长=4.32秒
2025-07-29 20:07:00,334 - INFO - 方案 #1: 额外添加overlap场景id=2780, 当前总时长=8.08秒
2025-07-29 20:07:00,334 - INFO - 方案 #1: 额外between选择后，当前总时长=8.08秒
2025-07-29 20:07:00,334 - INFO - 方案 #1: 场景总时长(8.08秒)大于音频时长(4.62秒)，需要裁剪
2025-07-29 20:07:00,334 - INFO - 调整前总时长: 8.08秒, 目标时长: 4.62秒
2025-07-29 20:07:00,335 - INFO - 需要裁剪 3.46秒
2025-07-29 20:07:00,335 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:07:00,335 - INFO - 裁剪场景ID=2780：从3.76秒裁剪至1.13秒
2025-07-29 20:07:00,335 - INFO - 裁剪场景ID=2784：从2.44秒裁剪至1.61秒
2025-07-29 20:07:00,335 - INFO - 调整后总时长: 4.62秒，与目标时长差异: 0.00秒
2025-07-29 20:07:00,335 - INFO - 方案 #1 调整/填充后最终总时长: 4.62秒
2025-07-29 20:07:00,335 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:00,335 - INFO - ========== 当前模式：字幕 #73 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:00,335 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:07:00,335 - INFO - ========== 新模式：字幕 #73 共生成 3 套有效场景方案 ==========
2025-07-29 20:07:00,335 - INFO - 
----- 处理字幕 #73 的方案 #1 -----
2025-07-29 20:07:00,335 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 20:07:00,335 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphxcjkpy1
2025-07-29 20:07:00,336 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2779.mp4 (确认存在: True)
2025-07-29 20:07:00,336 - INFO - 添加场景ID=2779，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:07:00,336 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2780.mp4 (确认存在: True)
2025-07-29 20:07:00,336 - INFO - 添加场景ID=2780，时长=3.76秒，累计时长=5.64秒
2025-07-29 20:07:00,336 - INFO - 准备合并 2 个场景文件，总时长约 5.64秒
2025-07-29 20:07:00,336 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2779.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2780.mp4'

2025-07-29 20:07:00,336 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphxcjkpy1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphxcjkpy1\temp_combined.mp4
2025-07-29 20:07:00,461 - INFO - 合并后的视频时长: 5.69秒，目标音频时长: 4.62秒
2025-07-29 20:07:00,461 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphxcjkpy1\temp_combined.mp4 -ss 0 -to 4.619 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 20:07:00,756 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:00,756 - INFO - 目标音频时长: 4.62秒
2025-07-29 20:07:00,756 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:07:00,756 - INFO - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:07:00,756 - INFO - ==========================================
2025-07-29 20:07:00,756 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:00,756 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 20:07:00,756 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphxcjkpy1
2025-07-29 20:07:00,801 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:00,801 - INFO -   - 音频时长: 4.62秒
2025-07-29 20:07:00,801 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:07:00,801 - INFO -   - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:07:00,801 - INFO - 
----- 处理字幕 #73 的方案 #2 -----
2025-07-29 20:07:00,801 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 20:07:00,802 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8e0sh282
2025-07-29 20:07:00,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2784.mp4 (确认存在: True)
2025-07-29 20:07:00,802 - INFO - 添加场景ID=2784，时长=2.44秒，累计时长=2.44秒
2025-07-29 20:07:00,802 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2785.mp4 (确认存在: True)
2025-07-29 20:07:00,802 - INFO - 添加场景ID=2785，时长=2.96秒，累计时长=5.40秒
2025-07-29 20:07:00,802 - INFO - 准备合并 2 个场景文件，总时长约 5.40秒
2025-07-29 20:07:00,802 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2784.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2785.mp4'

2025-07-29 20:07:00,803 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8e0sh282\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8e0sh282\temp_combined.mp4
2025-07-29 20:07:00,930 - INFO - 合并后的视频时长: 5.45秒，目标音频时长: 4.62秒
2025-07-29 20:07:00,930 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8e0sh282\temp_combined.mp4 -ss 0 -to 4.619 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 20:07:01,227 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:01,227 - INFO - 目标音频时长: 4.62秒
2025-07-29 20:07:01,227 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:07:01,227 - INFO - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:07:01,227 - INFO - ==========================================
2025-07-29 20:07:01,227 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:01,227 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 20:07:01,227 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8e0sh282
2025-07-29 20:07:01,273 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:01,273 - INFO -   - 音频时长: 4.62秒
2025-07-29 20:07:01,273 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:07:01,273 - INFO -   - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:07:01,273 - INFO - 
----- 处理字幕 #73 的方案 #3 -----
2025-07-29 20:07:01,273 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-07-29 20:07:01,274 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw30cksna
2025-07-29 20:07:01,274 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2779.mp4 (确认存在: True)
2025-07-29 20:07:01,274 - INFO - 添加场景ID=2779，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:07:01,274 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2784.mp4 (确认存在: True)
2025-07-29 20:07:01,274 - INFO - 添加场景ID=2784，时长=2.44秒，累计时长=4.32秒
2025-07-29 20:07:01,274 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2780.mp4 (确认存在: True)
2025-07-29 20:07:01,274 - INFO - 添加场景ID=2780，时长=3.76秒，累计时长=8.08秒
2025-07-29 20:07:01,274 - INFO - 场景总时长(8.08秒)已达到音频时长(4.62秒)的1.5倍，停止添加场景
2025-07-29 20:07:01,274 - INFO - 准备合并 3 个场景文件，总时长约 8.08秒
2025-07-29 20:07:01,274 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2779.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2784.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2780.mp4'

2025-07-29 20:07:01,274 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpw30cksna\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpw30cksna\temp_combined.mp4
2025-07-29 20:07:01,421 - INFO - 合并后的视频时长: 8.15秒，目标音频时长: 4.62秒
2025-07-29 20:07:01,421 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpw30cksna\temp_combined.mp4 -ss 0 -to 4.619 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-07-29 20:07:01,721 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:01,721 - INFO - 目标音频时长: 4.62秒
2025-07-29 20:07:01,721 - INFO - 实际视频时长: 4.66秒
2025-07-29 20:07:01,721 - INFO - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:07:01,721 - INFO - ==========================================
2025-07-29 20:07:01,721 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:01,721 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-07-29 20:07:01,722 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw30cksna
2025-07-29 20:07:01,768 - INFO - 方案 #3 处理完成:
2025-07-29 20:07:01,768 - INFO -   - 音频时长: 4.62秒
2025-07-29 20:07:01,768 - INFO -   - 视频时长: 4.66秒
2025-07-29 20:07:01,768 - INFO -   - 时长差异: 0.04秒 (0.95%)
2025-07-29 20:07:01,768 - INFO - 
字幕 #73 处理完成，成功生成 3/3 套方案
2025-07-29 20:07:01,768 - INFO - 生成的视频文件:
2025-07-29 20:07:01,768 - INFO -   1. F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 20:07:01,768 - INFO -   2. F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 20:07:01,768 - INFO -   3. F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-07-29 20:07:01,768 - INFO - ========== 字幕 #73 处理结束 ==========

