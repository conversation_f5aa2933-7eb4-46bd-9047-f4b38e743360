2025-07-29 20:06:11,049 - INFO - ========== 字幕 #38 处理开始 ==========
2025-07-29 20:06:11,049 - INFO - 字幕内容: 女人的脑海中瞬间闪回妹妹天真烂漫的笑脸，那些美好的回忆此刻都化作了最锋利的刀。
2025-07-29 20:06:11,049 - INFO - 字幕序号: [620, 627]
2025-07-29 20:06:11,049 - INFO - 音频文件详情:
2025-07-29 20:06:11,049 - INFO -   - 路径: output\38.wav
2025-07-29 20:06:11,049 - INFO -   - 时长: 5.66秒
2025-07-29 20:06:11,050 - INFO -   - 验证音频时长: 5.66秒
2025-07-29 20:06:11,050 - INFO - 字幕时间戳信息:
2025-07-29 20:06:11,050 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:11,050 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:11,050 - INFO -   - 根据生成的音频时长(5.66秒)已调整字幕时间戳
2025-07-29 20:06:11,050 - INFO - ========== 新模式：为字幕 #38 生成4套场景方案 ==========
2025-07-29 20:06:11,050 - INFO - 字幕序号列表: [620, 627]
2025-07-29 20:06:11,050 - INFO - 
--- 生成方案 #1：基于字幕序号 #620 ---
2025-07-29 20:06:11,050 - INFO - 开始为单个字幕序号 #620 匹配场景，目标时长: 5.66秒
2025-07-29 20:06:11,050 - INFO - 开始查找字幕序号 [620] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:11,050 - INFO - 找到related_overlap场景: scene_id=669, 字幕#620
2025-07-29 20:06:11,052 - INFO - 字幕 #620 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:11,052 - INFO - 字幕序号 #620 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:11,052 - INFO - 选择第一个overlap场景作为起点: scene_id=669
2025-07-29 20:06:11,052 - INFO - 添加起点场景: scene_id=669, 时长=2.20秒, 累计时长=2.20秒
2025-07-29 20:06:11,052 - INFO - 起点场景时长不足，需要延伸填充 3.46秒
2025-07-29 20:06:11,052 - INFO - 起点场景在原始列表中的索引: 668
2025-07-29 20:06:11,052 - INFO - 延伸添加场景: scene_id=670 (完整时长 1.00秒)
2025-07-29 20:06:11,052 - INFO - 累计时长: 3.20秒
2025-07-29 20:06:11,052 - INFO - 延伸添加场景: scene_id=671 (完整时长 1.56秒)
2025-07-29 20:06:11,052 - INFO - 累计时长: 4.76秒
2025-07-29 20:06:11,052 - INFO - 延伸添加场景: scene_id=672 (裁剪至 0.90秒)
2025-07-29 20:06:11,052 - INFO - 累计时长: 5.66秒
2025-07-29 20:06:11,052 - INFO - 字幕序号 #620 场景匹配完成，共选择 4 个场景，总时长: 5.66秒
2025-07-29 20:06:11,052 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:11,052 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:11,052 - INFO - 
--- 生成方案 #2：基于字幕序号 #627 ---
2025-07-29 20:06:11,052 - INFO - 开始为单个字幕序号 #627 匹配场景，目标时长: 5.66秒
2025-07-29 20:06:11,052 - INFO - 开始查找字幕序号 [627] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:11,052 - INFO - 找到related_overlap场景: scene_id=674, 字幕#627
2025-07-29 20:06:11,053 - INFO - 字幕 #627 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:11,053 - INFO - 字幕序号 #627 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:11,053 - INFO - 选择第一个overlap场景作为起点: scene_id=674
2025-07-29 20:06:11,053 - INFO - 添加起点场景: scene_id=674, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 20:06:11,053 - INFO - 起点场景时长不足，需要延伸填充 3.78秒
2025-07-29 20:06:11,054 - INFO - 起点场景在原始列表中的索引: 673
2025-07-29 20:06:11,054 - INFO - 延伸添加场景: scene_id=675 (完整时长 1.08秒)
2025-07-29 20:06:11,054 - INFO - 累计时长: 2.96秒
2025-07-29 20:06:11,054 - INFO - 延伸添加场景: scene_id=676 (完整时长 1.00秒)
2025-07-29 20:06:11,054 - INFO - 累计时长: 3.96秒
2025-07-29 20:06:11,054 - INFO - 延伸添加场景: scene_id=677 (完整时长 1.48秒)
2025-07-29 20:06:11,054 - INFO - 累计时长: 5.44秒
2025-07-29 20:06:11,054 - INFO - 延伸添加场景: scene_id=678 (裁剪至 0.22秒)
2025-07-29 20:06:11,054 - INFO - 累计时长: 5.66秒
2025-07-29 20:06:11,054 - INFO - 字幕序号 #627 场景匹配完成，共选择 5 个场景，总时长: 5.66秒
2025-07-29 20:06:11,054 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 20:06:11,054 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:11,054 - INFO - ========== 当前模式：为字幕 #38 生成 1 套场景方案 ==========
2025-07-29 20:06:11,054 - INFO - 开始查找字幕序号 [620, 627] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:11,054 - INFO - 找到related_overlap场景: scene_id=669, 字幕#620
2025-07-29 20:06:11,054 - INFO - 找到related_overlap场景: scene_id=674, 字幕#627
2025-07-29 20:06:11,055 - INFO - 字幕 #620 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:11,055 - INFO - 字幕 #627 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:11,055 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:11,055 - INFO - 开始生成方案 #1
2025-07-29 20:06:11,055 - INFO - 方案 #1: 为字幕#620选择初始化overlap场景id=669
2025-07-29 20:06:11,055 - INFO - 方案 #1: 为字幕#627选择初始化overlap场景id=674
2025-07-29 20:06:11,055 - INFO - 方案 #1: 初始选择后，当前总时长=4.08秒
2025-07-29 20:06:11,055 - INFO - 方案 #1: 额外between选择后，当前总时长=4.08秒
2025-07-29 20:06:11,055 - INFO - 方案 #1: 场景总时长(4.08秒)小于音频时长(5.66秒)，需要延伸填充
2025-07-29 20:06:11,055 - INFO - 方案 #1: 最后一个场景ID: 674
2025-07-29 20:06:11,055 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 673
2025-07-29 20:06:11,055 - INFO - 方案 #1: 需要填充时长: 1.58秒
2025-07-29 20:06:11,055 - INFO - 方案 #1: 追加场景 scene_id=675 (完整时长 1.08秒)
2025-07-29 20:06:11,055 - INFO - 方案 #1: 追加场景 scene_id=676 (裁剪至 0.50秒)
2025-07-29 20:06:11,055 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:11,055 - INFO - 方案 #1 调整/填充后最终总时长: 5.66秒
2025-07-29 20:06:11,055 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:11,055 - INFO - ========== 当前模式：字幕 #38 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:11,055 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:11,055 - INFO - ========== 新模式：字幕 #38 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:11,055 - INFO - 
----- 处理字幕 #38 的方案 #1 -----
2025-07-29 20:06:11,056 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 20:06:11,056 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiv7wn9yl
2025-07-29 20:06:11,057 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\669.mp4 (确认存在: True)
2025-07-29 20:06:11,057 - INFO - 添加场景ID=669，时长=2.20秒，累计时长=2.20秒
2025-07-29 20:06:11,057 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\670.mp4 (确认存在: True)
2025-07-29 20:06:11,057 - INFO - 添加场景ID=670，时长=1.00秒，累计时长=3.20秒
2025-07-29 20:06:11,057 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\671.mp4 (确认存在: True)
2025-07-29 20:06:11,057 - INFO - 添加场景ID=671，时长=1.56秒，累计时长=4.76秒
2025-07-29 20:06:11,057 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\672.mp4 (确认存在: True)
2025-07-29 20:06:11,057 - INFO - 添加场景ID=672，时长=1.72秒，累计时长=6.48秒
2025-07-29 20:06:11,057 - INFO - 准备合并 4 个场景文件，总时长约 6.48秒
2025-07-29 20:06:11,057 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/669.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/670.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/671.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/672.mp4'

2025-07-29 20:06:11,057 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiv7wn9yl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiv7wn9yl\temp_combined.mp4
2025-07-29 20:06:11,199 - INFO - 合并后的视频时长: 6.57秒，目标音频时长: 5.66秒
2025-07-29 20:06:11,199 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiv7wn9yl\temp_combined.mp4 -ss 0 -to 5.656 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 20:06:11,556 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:11,557 - INFO - 目标音频时长: 5.66秒
2025-07-29 20:06:11,557 - INFO - 实际视频时长: 5.70秒
2025-07-29 20:06:11,557 - INFO - 时长差异: 0.05秒 (0.83%)
2025-07-29 20:06:11,557 - INFO - ==========================================
2025-07-29 20:06:11,557 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:11,557 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 20:06:11,557 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiv7wn9yl
2025-07-29 20:06:11,604 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:11,604 - INFO -   - 音频时长: 5.66秒
2025-07-29 20:06:11,604 - INFO -   - 视频时长: 5.70秒
2025-07-29 20:06:11,604 - INFO -   - 时长差异: 0.05秒 (0.83%)
2025-07-29 20:06:11,604 - INFO - 
----- 处理字幕 #38 的方案 #2 -----
2025-07-29 20:06:11,604 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 20:06:11,605 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphc5d0g0y
2025-07-29 20:06:11,605 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\674.mp4 (确认存在: True)
2025-07-29 20:06:11,605 - INFO - 添加场景ID=674，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:06:11,605 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\675.mp4 (确认存在: True)
2025-07-29 20:06:11,605 - INFO - 添加场景ID=675，时长=1.08秒，累计时长=2.96秒
2025-07-29 20:06:11,605 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\676.mp4 (确认存在: True)
2025-07-29 20:06:11,605 - INFO - 添加场景ID=676，时长=1.00秒，累计时长=3.96秒
2025-07-29 20:06:11,605 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\677.mp4 (确认存在: True)
2025-07-29 20:06:11,605 - INFO - 添加场景ID=677，时长=1.48秒，累计时长=5.44秒
2025-07-29 20:06:11,606 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\678.mp4 (确认存在: True)
2025-07-29 20:06:11,606 - INFO - 添加场景ID=678，时长=1.48秒，累计时长=6.92秒
2025-07-29 20:06:11,606 - INFO - 准备合并 5 个场景文件，总时长约 6.92秒
2025-07-29 20:06:11,606 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/674.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/675.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/676.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/677.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/678.mp4'

2025-07-29 20:06:11,606 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphc5d0g0y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphc5d0g0y\temp_combined.mp4
2025-07-29 20:06:11,749 - INFO - 合并后的视频时长: 7.04秒，目标音频时长: 5.66秒
2025-07-29 20:06:11,749 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphc5d0g0y\temp_combined.mp4 -ss 0 -to 5.656 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 20:06:12,101 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:12,102 - INFO - 目标音频时长: 5.66秒
2025-07-29 20:06:12,102 - INFO - 实际视频时长: 5.70秒
2025-07-29 20:06:12,102 - INFO - 时长差异: 0.05秒 (0.83%)
2025-07-29 20:06:12,102 - INFO - ==========================================
2025-07-29 20:06:12,102 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:12,102 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 20:06:12,102 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphc5d0g0y
2025-07-29 20:06:12,148 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:12,148 - INFO -   - 音频时长: 5.66秒
2025-07-29 20:06:12,148 - INFO -   - 视频时长: 5.70秒
2025-07-29 20:06:12,148 - INFO -   - 时长差异: 0.05秒 (0.83%)
2025-07-29 20:06:12,149 - INFO - 
----- 处理字幕 #38 的方案 #3 -----
2025-07-29 20:06:12,149 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-07-29 20:06:12,149 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaqd3jjh1
2025-07-29 20:06:12,149 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\669.mp4 (确认存在: True)
2025-07-29 20:06:12,149 - INFO - 添加场景ID=669，时长=2.20秒，累计时长=2.20秒
2025-07-29 20:06:12,149 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\674.mp4 (确认存在: True)
2025-07-29 20:06:12,149 - INFO - 添加场景ID=674，时长=1.88秒，累计时长=4.08秒
2025-07-29 20:06:12,149 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\675.mp4 (确认存在: True)
2025-07-29 20:06:12,149 - INFO - 添加场景ID=675，时长=1.08秒，累计时长=5.16秒
2025-07-29 20:06:12,149 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\676.mp4 (确认存在: True)
2025-07-29 20:06:12,149 - INFO - 添加场景ID=676，时长=1.00秒，累计时长=6.16秒
2025-07-29 20:06:12,149 - INFO - 准备合并 4 个场景文件，总时长约 6.16秒
2025-07-29 20:06:12,149 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/669.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/674.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/675.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/676.mp4'

2025-07-29 20:06:12,149 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpaqd3jjh1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpaqd3jjh1\temp_combined.mp4
2025-07-29 20:06:12,270 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 5.66秒
2025-07-29 20:06:12,270 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpaqd3jjh1\temp_combined.mp4 -ss 0 -to 5.656 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-07-29 20:06:12,633 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:12,633 - INFO - 目标音频时长: 5.66秒
2025-07-29 20:06:12,633 - INFO - 实际视频时长: 5.70秒
2025-07-29 20:06:12,633 - INFO - 时长差异: 0.05秒 (0.83%)
2025-07-29 20:06:12,633 - INFO - ==========================================
2025-07-29 20:06:12,633 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:12,633 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-07-29 20:06:12,634 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaqd3jjh1
2025-07-29 20:06:12,677 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:12,677 - INFO -   - 音频时长: 5.66秒
2025-07-29 20:06:12,677 - INFO -   - 视频时长: 5.70秒
2025-07-29 20:06:12,677 - INFO -   - 时长差异: 0.05秒 (0.83%)
2025-07-29 20:06:12,677 - INFO - 
字幕 #38 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:12,677 - INFO - 生成的视频文件:
2025-07-29 20:06:12,677 - INFO -   1. F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 20:06:12,677 - INFO -   2. F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 20:06:12,677 - INFO -   3. F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-07-29 20:06:12,677 - INFO - ========== 字幕 #38 处理结束 ==========

