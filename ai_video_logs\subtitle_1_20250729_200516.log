2025-07-29 20:05:16,533 - INFO - ========== 字幕 #1 处理开始 ==========
2025-07-29 20:05:16,533 - INFO - 字幕内容: 女人正憧憬着和未婚夫的美好未来，殊不知，一场改变所有人命运的风暴即将来临。
2025-07-29 20:05:16,533 - INFO - 字幕序号: [1, 5]
2025-07-29 20:05:16,537 - INFO - 音频文件详情:
2025-07-29 20:05:16,537 - INFO -   - 路径: output\1.wav
2025-07-29 20:05:16,537 - INFO -   - 时长: 5.01秒
2025-07-29 20:05:16,537 - INFO -   - 验证音频时长: 5.01秒
2025-07-29 20:05:16,537 - INFO - 字幕时间戳信息:
2025-07-29 20:05:16,537 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:16,537 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:16,537 - INFO -   - 根据生成的音频时长(5.01秒)已调整字幕时间戳
2025-07-29 20:05:16,537 - INFO - ========== 新模式：为字幕 #1 生成4套场景方案 ==========
2025-07-29 20:05:16,537 - INFO - 字幕序号列表: [1, 5]
2025-07-29 20:05:16,537 - INFO - 
--- 生成方案 #1：基于字幕序号 #1 ---
2025-07-29 20:05:16,537 - INFO - 开始为单个字幕序号 #1 匹配场景，目标时长: 5.01秒
2025-07-29 20:05:16,537 - INFO - 开始查找字幕序号 [1] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:16,537 - INFO - 找到related_overlap场景: scene_id=2, 字幕#1
2025-07-29 20:05:16,539 - INFO - 字幕 #1 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:16,539 - INFO - 字幕序号 #1 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:16,539 - INFO - 选择第一个overlap场景作为起点: scene_id=2
2025-07-29 20:05:16,539 - INFO - 添加起点场景: scene_id=2, 时长=4.68秒, 累计时长=4.68秒
2025-07-29 20:05:16,539 - INFO - 起点场景时长不足，需要延伸填充 0.33秒
2025-07-29 20:05:16,539 - INFO - 起点场景在原始列表中的索引: 1
2025-07-29 20:05:16,539 - INFO - 延伸添加场景: scene_id=3 (裁剪至 0.33秒)
2025-07-29 20:05:16,539 - INFO - 累计时长: 5.01秒
2025-07-29 20:05:16,539 - INFO - 字幕序号 #1 场景匹配完成，共选择 2 个场景，总时长: 5.01秒
2025-07-29 20:05:16,539 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:05:16,539 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:05:16,539 - INFO - 
--- 生成方案 #2：基于字幕序号 #5 ---
2025-07-29 20:05:16,539 - INFO - 开始为单个字幕序号 #5 匹配场景，目标时长: 5.01秒
2025-07-29 20:05:16,539 - INFO - 开始查找字幕序号 [5] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:16,539 - INFO - 找到related_overlap场景: scene_id=5, 字幕#5
2025-07-29 20:05:16,541 - INFO - 字幕 #5 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:16,541 - INFO - 字幕序号 #5 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:16,541 - INFO - 选择第一个overlap场景作为起点: scene_id=5
2025-07-29 20:05:16,541 - INFO - 添加起点场景: scene_id=5, 时长=1.20秒, 累计时长=1.20秒
2025-07-29 20:05:16,541 - INFO - 起点场景时长不足，需要延伸填充 3.81秒
2025-07-29 20:05:16,541 - INFO - 起点场景在原始列表中的索引: 4
2025-07-29 20:05:16,541 - INFO - 延伸添加场景: scene_id=6 (裁剪至 3.81秒)
2025-07-29 20:05:16,541 - INFO - 累计时长: 5.01秒
2025-07-29 20:05:16,541 - INFO - 字幕序号 #5 场景匹配完成，共选择 2 个场景，总时长: 5.01秒
2025-07-29 20:05:16,541 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:05:16,541 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:16,541 - INFO - ========== 当前模式：为字幕 #1 生成 1 套场景方案 ==========
2025-07-29 20:05:16,541 - INFO - 开始查找字幕序号 [1, 5] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:16,541 - INFO - 找到related_overlap场景: scene_id=2, 字幕#1
2025-07-29 20:05:16,541 - INFO - 找到related_overlap场景: scene_id=5, 字幕#5
2025-07-29 20:05:16,542 - INFO - 字幕 #1 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:16,542 - INFO - 字幕 #5 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:16,542 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:16,542 - INFO - 开始生成方案 #1
2025-07-29 20:05:16,542 - INFO - 方案 #1: 为字幕#1选择初始化overlap场景id=2
2025-07-29 20:05:16,542 - INFO - 方案 #1: 为字幕#5选择初始化overlap场景id=5
2025-07-29 20:05:16,542 - INFO - 方案 #1: 初始选择后，当前总时长=5.88秒
2025-07-29 20:05:16,542 - INFO - 方案 #1: 额外between选择后，当前总时长=5.88秒
2025-07-29 20:05:16,542 - INFO - 方案 #1: 场景总时长(5.88秒)大于音频时长(5.01秒)，需要裁剪
2025-07-29 20:05:16,542 - INFO - 调整前总时长: 5.88秒, 目标时长: 5.01秒
2025-07-29 20:05:16,542 - INFO - 需要裁剪 0.87秒
2025-07-29 20:05:16,542 - INFO - 裁剪最长场景ID=2：从4.68秒裁剪至3.81秒
2025-07-29 20:05:16,542 - INFO - 调整后总时长: 5.01秒，与目标时长差异: 0.00秒
2025-07-29 20:05:16,542 - INFO - 方案 #1 调整/填充后最终总时长: 5.01秒
2025-07-29 20:05:16,542 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:16,542 - INFO - ========== 当前模式：字幕 #1 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:16,542 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:16,542 - INFO - ========== 新模式：字幕 #1 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:16,542 - INFO - 
----- 处理字幕 #1 的方案 #1 -----
2025-07-29 20:05:16,542 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 20:05:16,544 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0l6m7ehg
2025-07-29 20:05:16,544 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2.mp4 (确认存在: True)
2025-07-29 20:05:16,544 - INFO - 添加场景ID=2，时长=4.68秒，累计时长=4.68秒
2025-07-29 20:05:16,544 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3.mp4 (确认存在: True)
2025-07-29 20:05:16,544 - INFO - 添加场景ID=3，时长=1.28秒，累计时长=5.96秒
2025-07-29 20:05:16,545 - INFO - 准备合并 2 个场景文件，总时长约 5.96秒
2025-07-29 20:05:16,545 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3.mp4'

2025-07-29 20:05:16,545 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0l6m7ehg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0l6m7ehg\temp_combined.mp4
2025-07-29 20:05:16,888 - INFO - 合并后的视频时长: 6.01秒，目标音频时长: 5.01秒
2025-07-29 20:05:16,888 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0l6m7ehg\temp_combined.mp4 -ss 0 -to 5.009 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 20:05:17,179 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:17,179 - INFO - 目标音频时长: 5.01秒
2025-07-29 20:05:17,179 - INFO - 实际视频时长: 5.06秒
2025-07-29 20:05:17,179 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:05:17,179 - INFO - ==========================================
2025-07-29 20:05:17,179 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:17,179 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 20:05:17,179 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0l6m7ehg
2025-07-29 20:05:17,225 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:17,225 - INFO -   - 音频时长: 5.01秒
2025-07-29 20:05:17,225 - INFO -   - 视频时长: 5.06秒
2025-07-29 20:05:17,225 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:05:17,225 - INFO - 
----- 处理字幕 #1 的方案 #2 -----
2025-07-29 20:05:17,225 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 20:05:17,225 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzyo6i6nw
2025-07-29 20:05:17,226 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\5.mp4 (确认存在: True)
2025-07-29 20:05:17,226 - INFO - 添加场景ID=5，时长=1.20秒，累计时长=1.20秒
2025-07-29 20:05:17,226 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\6.mp4 (确认存在: True)
2025-07-29 20:05:17,226 - INFO - 添加场景ID=6，时长=5.60秒，累计时长=6.80秒
2025-07-29 20:05:17,226 - INFO - 准备合并 2 个场景文件，总时长约 6.80秒
2025-07-29 20:05:17,226 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/5.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/6.mp4'

2025-07-29 20:05:17,226 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzyo6i6nw\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzyo6i6nw\temp_combined.mp4
2025-07-29 20:05:17,371 - INFO - 合并后的视频时长: 6.85秒，目标音频时长: 5.01秒
2025-07-29 20:05:17,371 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzyo6i6nw\temp_combined.mp4 -ss 0 -to 5.009 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 20:05:17,694 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:17,694 - INFO - 目标音频时长: 5.01秒
2025-07-29 20:05:17,694 - INFO - 实际视频时长: 5.06秒
2025-07-29 20:05:17,694 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:05:17,694 - INFO - ==========================================
2025-07-29 20:05:17,694 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:17,694 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 20:05:17,695 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzyo6i6nw
2025-07-29 20:05:17,739 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:17,739 - INFO -   - 音频时长: 5.01秒
2025-07-29 20:05:17,739 - INFO -   - 视频时长: 5.06秒
2025-07-29 20:05:17,739 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:05:17,739 - INFO - 
----- 处理字幕 #1 的方案 #3 -----
2025-07-29 20:05:17,739 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 20:05:17,739 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt9nb60tl
2025-07-29 20:05:17,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2.mp4 (确认存在: True)
2025-07-29 20:05:17,739 - INFO - 添加场景ID=2，时长=4.68秒，累计时长=4.68秒
2025-07-29 20:05:17,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\5.mp4 (确认存在: True)
2025-07-29 20:05:17,739 - INFO - 添加场景ID=5，时长=1.20秒，累计时长=5.88秒
2025-07-29 20:05:17,739 - INFO - 准备合并 2 个场景文件，总时长约 5.88秒
2025-07-29 20:05:17,739 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/5.mp4'

2025-07-29 20:05:17,739 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt9nb60tl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt9nb60tl\temp_combined.mp4
2025-07-29 20:05:17,859 - INFO - 合并后的视频时长: 5.93秒，目标音频时长: 5.01秒
2025-07-29 20:05:17,859 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt9nb60tl\temp_combined.mp4 -ss 0 -to 5.009 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 20:05:18,138 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:18,139 - INFO - 目标音频时长: 5.01秒
2025-07-29 20:05:18,139 - INFO - 实际视频时长: 5.06秒
2025-07-29 20:05:18,139 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:05:18,139 - INFO - ==========================================
2025-07-29 20:05:18,139 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:18,139 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 20:05:18,139 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt9nb60tl
2025-07-29 20:05:18,186 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:18,186 - INFO -   - 音频时长: 5.01秒
2025-07-29 20:05:18,186 - INFO -   - 视频时长: 5.06秒
2025-07-29 20:05:18,186 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 20:05:18,186 - INFO - 
字幕 #1 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:18,186 - INFO - 生成的视频文件:
2025-07-29 20:05:18,186 - INFO -   1. F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 20:05:18,186 - INFO -   2. F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 20:05:18,186 - INFO -   3. F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 20:05:18,186 - INFO - ========== 字幕 #1 处理结束 ==========

