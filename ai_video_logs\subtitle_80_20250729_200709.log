2025-07-29 20:07:09,662 - INFO - ========== 字幕 #80 处理开始 ==========
2025-07-29 20:07:09,662 - INFO - 字幕内容: 她用生命偿还了所有的罪孽，只是这句迟来的道歉，终究是太晚了。下辈子，还是不要再见了吧。
2025-07-29 20:07:09,663 - INFO - 字幕序号: [4286, 4291]
2025-07-29 20:07:09,663 - INFO - 音频文件详情:
2025-07-29 20:07:09,663 - INFO -   - 路径: output\80.wav
2025-07-29 20:07:09,663 - INFO -   - 时长: 5.33秒
2025-07-29 20:07:09,663 - INFO -   - 验证音频时长: 5.33秒
2025-07-29 20:07:09,663 - INFO - 字幕时间戳信息:
2025-07-29 20:07:09,663 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:07:09,663 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:07:09,663 - INFO -   - 根据生成的音频时长(5.33秒)已调整字幕时间戳
2025-07-29 20:07:09,663 - INFO - ========== 新模式：为字幕 #80 生成4套场景方案 ==========
2025-07-29 20:07:09,663 - INFO - 字幕序号列表: [4286, 4291]
2025-07-29 20:07:09,663 - INFO - 
--- 生成方案 #1：基于字幕序号 #4286 ---
2025-07-29 20:07:09,663 - INFO - 开始为单个字幕序号 #4286 匹配场景，目标时长: 5.33秒
2025-07-29 20:07:09,663 - INFO - 开始查找字幕序号 [4286] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:09,664 - INFO - 找到related_overlap场景: scene_id=3812, 字幕#4286
2025-07-29 20:07:09,664 - INFO - 找到related_overlap场景: scene_id=3813, 字幕#4286
2025-07-29 20:07:09,665 - INFO - 找到related_between场景: scene_id=3809, 字幕#4286
2025-07-29 20:07:09,665 - INFO - 找到related_between场景: scene_id=3810, 字幕#4286
2025-07-29 20:07:09,665 - INFO - 找到related_between场景: scene_id=3811, 字幕#4286
2025-07-29 20:07:09,665 - INFO - 字幕 #4286 找到 2 个overlap场景, 3 个between场景
2025-07-29 20:07:09,665 - INFO - 字幕序号 #4286 找到 2 个可用overlap场景, 3 个可用between场景
2025-07-29 20:07:09,665 - INFO - 选择第一个overlap场景作为起点: scene_id=3812
2025-07-29 20:07:09,665 - INFO - 添加起点场景: scene_id=3812, 时长=2.36秒, 累计时长=2.36秒
2025-07-29 20:07:09,665 - INFO - 起点场景时长不足，需要延伸填充 2.97秒
2025-07-29 20:07:09,665 - INFO - 起点场景在原始列表中的索引: 3811
2025-07-29 20:07:09,665 - INFO - 延伸添加场景: scene_id=3813 (完整时长 1.24秒)
2025-07-29 20:07:09,665 - INFO - 累计时长: 3.60秒
2025-07-29 20:07:09,665 - INFO - 延伸添加场景: scene_id=3814 (裁剪至 1.73秒)
2025-07-29 20:07:09,665 - INFO - 累计时长: 5.33秒
2025-07-29 20:07:09,665 - INFO - 字幕序号 #4286 场景匹配完成，共选择 3 个场景，总时长: 5.33秒
2025-07-29 20:07:09,665 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:07:09,665 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:07:09,665 - INFO - 
--- 生成方案 #2：基于字幕序号 #4291 ---
2025-07-29 20:07:09,665 - INFO - 开始为单个字幕序号 #4291 匹配场景，目标时长: 5.33秒
2025-07-29 20:07:09,665 - INFO - 开始查找字幕序号 [4291] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:09,666 - INFO - 找到related_overlap场景: scene_id=3816, 字幕#4291
2025-07-29 20:07:09,666 - INFO - 字幕 #4291 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:09,666 - INFO - 字幕序号 #4291 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:07:09,666 - INFO - 选择第一个overlap场景作为起点: scene_id=3816
2025-07-29 20:07:09,666 - INFO - 添加起点场景: scene_id=3816, 时长=2.92秒, 累计时长=2.92秒
2025-07-29 20:07:09,666 - INFO - 起点场景时长不足，需要延伸填充 2.41秒
2025-07-29 20:07:09,667 - INFO - 起点场景在原始列表中的索引: 3815
2025-07-29 20:07:09,667 - INFO - 到达列表末尾，仍需填充 2.41秒，从列表开头继续查找
2025-07-29 20:07:09,667 - INFO - 从列表开头延伸添加场景: scene_id=1 (裁剪至 2.41秒)
2025-07-29 20:07:09,667 - INFO - 累计时长: 5.33秒
2025-07-29 20:07:09,667 - INFO - 字幕序号 #4291 场景匹配完成，共选择 2 个场景，总时长: 5.33秒
2025-07-29 20:07:09,667 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:07:09,667 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:07:09,667 - INFO - ========== 当前模式：为字幕 #80 生成 1 套场景方案 ==========
2025-07-29 20:07:09,667 - INFO - 开始查找字幕序号 [4286, 4291] 对应的场景，共有 3816 个场景可选
2025-07-29 20:07:09,668 - INFO - 找到related_overlap场景: scene_id=3812, 字幕#4286
2025-07-29 20:07:09,668 - INFO - 找到related_overlap场景: scene_id=3813, 字幕#4286
2025-07-29 20:07:09,668 - INFO - 找到related_overlap场景: scene_id=3816, 字幕#4291
2025-07-29 20:07:09,668 - INFO - 找到related_between场景: scene_id=3809, 字幕#4286
2025-07-29 20:07:09,668 - INFO - 找到related_between场景: scene_id=3810, 字幕#4286
2025-07-29 20:07:09,668 - INFO - 找到related_between场景: scene_id=3811, 字幕#4286
2025-07-29 20:07:09,668 - INFO - 字幕 #4286 找到 2 个overlap场景, 3 个between场景
2025-07-29 20:07:09,668 - INFO - 字幕 #4291 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:07:09,668 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:07:09,668 - INFO - 开始生成方案 #1
2025-07-29 20:07:09,668 - INFO - 方案 #1: 为字幕#4286选择初始化overlap场景id=3813
2025-07-29 20:07:09,668 - INFO - 方案 #1: 为字幕#4291选择初始化overlap场景id=3816
2025-07-29 20:07:09,668 - INFO - 方案 #1: 初始选择后，当前总时长=4.16秒
2025-07-29 20:07:09,668 - INFO - 方案 #1: 额外添加overlap场景id=3812, 当前总时长=6.52秒
2025-07-29 20:07:09,668 - INFO - 方案 #1: 额外between选择后，当前总时长=6.52秒
2025-07-29 20:07:09,669 - INFO - 方案 #1: 场景总时长(6.52秒)大于音频时长(5.33秒)，需要裁剪
2025-07-29 20:07:09,669 - INFO - 调整前总时长: 6.52秒, 目标时长: 5.33秒
2025-07-29 20:07:09,669 - INFO - 需要裁剪 1.19秒
2025-07-29 20:07:09,669 - INFO - 裁剪最长场景ID=3816：从2.92秒裁剪至1.73秒
2025-07-29 20:07:09,669 - INFO - 调整后总时长: 5.33秒，与目标时长差异: 0.00秒
2025-07-29 20:07:09,669 - INFO - 方案 #1 调整/填充后最终总时长: 5.33秒
2025-07-29 20:07:09,669 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:07:09,669 - INFO - ========== 当前模式：字幕 #80 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:07:09,669 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:07:09,669 - INFO - ========== 新模式：字幕 #80 共生成 3 套有效场景方案 ==========
2025-07-29 20:07:09,669 - INFO - 
----- 处理字幕 #80 的方案 #1 -----
2025-07-29 20:07:09,669 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\80_1.mp4
2025-07-29 20:07:09,669 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp53_gxm4e
2025-07-29 20:07:09,670 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3812.mp4 (确认存在: True)
2025-07-29 20:07:09,670 - INFO - 添加场景ID=3812，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:07:09,670 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3813.mp4 (确认存在: True)
2025-07-29 20:07:09,670 - INFO - 添加场景ID=3813，时长=1.24秒，累计时长=3.60秒
2025-07-29 20:07:09,670 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3814.mp4 (确认存在: True)
2025-07-29 20:07:09,670 - INFO - 添加场景ID=3814，时长=2.24秒，累计时长=5.84秒
2025-07-29 20:07:09,670 - INFO - 准备合并 3 个场景文件，总时长约 5.84秒
2025-07-29 20:07:09,670 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3812.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3813.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3814.mp4'

2025-07-29 20:07:09,670 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp53_gxm4e\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp53_gxm4e\temp_combined.mp4
2025-07-29 20:07:09,801 - INFO - 合并后的视频时长: 5.91秒，目标音频时长: 5.33秒
2025-07-29 20:07:09,801 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp53_gxm4e\temp_combined.mp4 -ss 0 -to 5.326 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\80_1.mp4
2025-07-29 20:07:10,124 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:10,124 - INFO - 目标音频时长: 5.33秒
2025-07-29 20:07:10,124 - INFO - 实际视频时长: 5.38秒
2025-07-29 20:07:10,124 - INFO - 时长差异: 0.06秒 (1.07%)
2025-07-29 20:07:10,124 - INFO - ==========================================
2025-07-29 20:07:10,124 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:10,124 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\80_1.mp4
2025-07-29 20:07:10,124 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp53_gxm4e
2025-07-29 20:07:10,174 - INFO - 方案 #1 处理完成:
2025-07-29 20:07:10,174 - INFO -   - 音频时长: 5.33秒
2025-07-29 20:07:10,174 - INFO -   - 视频时长: 5.38秒
2025-07-29 20:07:10,174 - INFO -   - 时长差异: 0.06秒 (1.07%)
2025-07-29 20:07:10,174 - INFO - 
----- 处理字幕 #80 的方案 #2 -----
2025-07-29 20:07:10,174 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\80_2.mp4
2025-07-29 20:07:10,174 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp37oc7fix
2025-07-29 20:07:10,175 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3816.mp4 (确认存在: True)
2025-07-29 20:07:10,175 - INFO - 添加场景ID=3816，时长=2.92秒，累计时长=2.92秒
2025-07-29 20:07:10,175 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1.mp4 (确认存在: True)
2025-07-29 20:07:10,175 - INFO - 添加场景ID=1，时长=3.48秒，累计时长=6.40秒
2025-07-29 20:07:10,175 - INFO - 准备合并 2 个场景文件，总时长约 6.40秒
2025-07-29 20:07:10,175 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3816.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1.mp4'

2025-07-29 20:07:10,175 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp37oc7fix\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp37oc7fix\temp_combined.mp4
2025-07-29 20:07:10,311 - INFO - 合并后的视频时长: 6.45秒，目标音频时长: 5.33秒
2025-07-29 20:07:10,311 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp37oc7fix\temp_combined.mp4 -ss 0 -to 5.326 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\80_2.mp4
2025-07-29 20:07:10,626 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:10,626 - INFO - 目标音频时长: 5.33秒
2025-07-29 20:07:10,626 - INFO - 实际视频时长: 5.38秒
2025-07-29 20:07:10,626 - INFO - 时长差异: 0.06秒 (1.07%)
2025-07-29 20:07:10,626 - INFO - ==========================================
2025-07-29 20:07:10,626 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:10,626 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\80_2.mp4
2025-07-29 20:07:10,626 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp37oc7fix
2025-07-29 20:07:10,671 - INFO - 方案 #2 处理完成:
2025-07-29 20:07:10,671 - INFO -   - 音频时长: 5.33秒
2025-07-29 20:07:10,671 - INFO -   - 视频时长: 5.38秒
2025-07-29 20:07:10,671 - INFO -   - 时长差异: 0.06秒 (1.07%)
2025-07-29 20:07:10,671 - INFO - 
----- 处理字幕 #80 的方案 #3 -----
2025-07-29 20:07:10,671 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\80_3.mp4
2025-07-29 20:07:10,671 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnjtbdih4
2025-07-29 20:07:10,672 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3813.mp4 (确认存在: True)
2025-07-29 20:07:10,672 - INFO - 添加场景ID=3813，时长=1.24秒，累计时长=1.24秒
2025-07-29 20:07:10,672 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3816.mp4 (确认存在: True)
2025-07-29 20:07:10,672 - INFO - 添加场景ID=3816，时长=2.92秒，累计时长=4.16秒
2025-07-29 20:07:10,672 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3812.mp4 (确认存在: True)
2025-07-29 20:07:10,672 - INFO - 添加场景ID=3812，时长=2.36秒，累计时长=6.52秒
2025-07-29 20:07:10,672 - INFO - 准备合并 3 个场景文件，总时长约 6.52秒
2025-07-29 20:07:10,672 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3813.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3816.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3812.mp4'

2025-07-29 20:07:10,672 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnjtbdih4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnjtbdih4\temp_combined.mp4
2025-07-29 20:07:10,794 - INFO - 合并后的视频时长: 6.59秒，目标音频时长: 5.33秒
2025-07-29 20:07:10,794 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnjtbdih4\temp_combined.mp4 -ss 0 -to 5.326 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\80_3.mp4
2025-07-29 20:07:11,096 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:07:11,096 - INFO - 目标音频时长: 5.33秒
2025-07-29 20:07:11,096 - INFO - 实际视频时长: 5.38秒
2025-07-29 20:07:11,096 - INFO - 时长差异: 0.06秒 (1.07%)
2025-07-29 20:07:11,096 - INFO - ==========================================
2025-07-29 20:07:11,096 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:07:11,096 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\80_3.mp4
2025-07-29 20:07:11,097 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnjtbdih4
2025-07-29 20:07:11,139 - INFO - 方案 #3 处理完成:
2025-07-29 20:07:11,139 - INFO -   - 音频时长: 5.33秒
2025-07-29 20:07:11,139 - INFO -   - 视频时长: 5.38秒
2025-07-29 20:07:11,139 - INFO -   - 时长差异: 0.06秒 (1.07%)
2025-07-29 20:07:11,139 - INFO - 
字幕 #80 处理完成，成功生成 3/3 套方案
2025-07-29 20:07:11,139 - INFO - 生成的视频文件:
2025-07-29 20:07:11,139 - INFO -   1. F:/github/aicut_auto/newcut_ai\80_1.mp4
2025-07-29 20:07:11,139 - INFO -   2. F:/github/aicut_auto/newcut_ai\80_2.mp4
2025-07-29 20:07:11,139 - INFO -   3. F:/github/aicut_auto/newcut_ai\80_3.mp4
2025-07-29 20:07:11,139 - INFO - ========== 字幕 #80 处理结束 ==========

