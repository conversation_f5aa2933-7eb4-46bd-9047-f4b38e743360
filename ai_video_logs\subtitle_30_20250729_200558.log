2025-07-29 20:05:58,818 - INFO - ========== 字幕 #30 处理开始 ==========
2025-07-29 20:05:58,818 - INFO - 字幕内容: 女人恼羞成怒，反斥男人忘恩负义，没有许氏集团，他连工作都没有，哪来的工资。
2025-07-29 20:05:58,818 - INFO - 字幕序号: [524, 529]
2025-07-29 20:05:58,818 - INFO - 音频文件详情:
2025-07-29 20:05:58,818 - INFO -   - 路径: output\30.wav
2025-07-29 20:05:58,818 - INFO -   - 时长: 4.85秒
2025-07-29 20:05:58,818 - INFO -   - 验证音频时长: 4.85秒
2025-07-29 20:05:58,818 - INFO - 字幕时间戳信息:
2025-07-29 20:05:58,828 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:58,828 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:58,828 - INFO -   - 根据生成的音频时长(4.85秒)已调整字幕时间戳
2025-07-29 20:05:58,828 - INFO - ========== 新模式：为字幕 #30 生成4套场景方案 ==========
2025-07-29 20:05:58,828 - INFO - 字幕序号列表: [524, 529]
2025-07-29 20:05:58,828 - INFO - 
--- 生成方案 #1：基于字幕序号 #524 ---
2025-07-29 20:05:58,828 - INFO - 开始为单个字幕序号 #524 匹配场景，目标时长: 4.85秒
2025-07-29 20:05:58,828 - INFO - 开始查找字幕序号 [524] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:58,828 - INFO - 找到related_overlap场景: scene_id=544, 字幕#524
2025-07-29 20:05:58,830 - INFO - 字幕 #524 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:58,830 - INFO - 字幕序号 #524 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:58,830 - INFO - 选择第一个overlap场景作为起点: scene_id=544
2025-07-29 20:05:58,830 - INFO - 添加起点场景: scene_id=544, 时长=0.88秒, 累计时长=0.88秒
2025-07-29 20:05:58,830 - INFO - 起点场景时长不足，需要延伸填充 3.97秒
2025-07-29 20:05:58,830 - INFO - 起点场景在原始列表中的索引: 543
2025-07-29 20:05:58,830 - INFO - 延伸添加场景: scene_id=545 (完整时长 1.92秒)
2025-07-29 20:05:58,830 - INFO - 累计时长: 2.80秒
2025-07-29 20:05:58,830 - INFO - 延伸添加场景: scene_id=546 (完整时长 1.44秒)
2025-07-29 20:05:58,830 - INFO - 累计时长: 4.24秒
2025-07-29 20:05:58,830 - INFO - 延伸添加场景: scene_id=547 (裁剪至 0.61秒)
2025-07-29 20:05:58,830 - INFO - 累计时长: 4.85秒
2025-07-29 20:05:58,830 - INFO - 字幕序号 #524 场景匹配完成，共选择 4 个场景，总时长: 4.85秒
2025-07-29 20:05:58,830 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:05:58,830 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:05:58,830 - INFO - 
--- 生成方案 #2：基于字幕序号 #529 ---
2025-07-29 20:05:58,830 - INFO - 开始为单个字幕序号 #529 匹配场景，目标时长: 4.85秒
2025-07-29 20:05:58,830 - INFO - 开始查找字幕序号 [529] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:58,830 - INFO - 找到related_overlap场景: scene_id=548, 字幕#529
2025-07-29 20:05:58,830 - INFO - 找到related_overlap场景: scene_id=549, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=550, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=551, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=552, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=553, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=554, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=555, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=556, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=557, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_between场景: scene_id=558, 字幕#529
2025-07-29 20:05:58,831 - INFO - 字幕 #529 找到 2 个overlap场景, 9 个between场景
2025-07-29 20:05:58,831 - INFO - 字幕序号 #529 找到 2 个可用overlap场景, 9 个可用between场景
2025-07-29 20:05:58,831 - INFO - 选择第一个overlap场景作为起点: scene_id=548
2025-07-29 20:05:58,831 - INFO - 添加起点场景: scene_id=548, 时长=1.84秒, 累计时长=1.84秒
2025-07-29 20:05:58,831 - INFO - 起点场景时长不足，需要延伸填充 3.01秒
2025-07-29 20:05:58,831 - INFO - 起点场景在原始列表中的索引: 547
2025-07-29 20:05:58,831 - INFO - 延伸添加场景: scene_id=549 (完整时长 1.08秒)
2025-07-29 20:05:58,831 - INFO - 累计时长: 2.92秒
2025-07-29 20:05:58,831 - INFO - 延伸添加场景: scene_id=550 (完整时长 1.44秒)
2025-07-29 20:05:58,831 - INFO - 累计时长: 4.36秒
2025-07-29 20:05:58,831 - INFO - 延伸添加场景: scene_id=551 (裁剪至 0.49秒)
2025-07-29 20:05:58,831 - INFO - 累计时长: 4.85秒
2025-07-29 20:05:58,831 - INFO - 字幕序号 #529 场景匹配完成，共选择 4 个场景，总时长: 4.85秒
2025-07-29 20:05:58,831 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:58,831 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:58,831 - INFO - ========== 当前模式：为字幕 #30 生成 1 套场景方案 ==========
2025-07-29 20:05:58,831 - INFO - 开始查找字幕序号 [524, 529] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:58,831 - INFO - 找到related_overlap场景: scene_id=544, 字幕#524
2025-07-29 20:05:58,831 - INFO - 找到related_overlap场景: scene_id=548, 字幕#529
2025-07-29 20:05:58,831 - INFO - 找到related_overlap场景: scene_id=549, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=550, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=551, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=552, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=553, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=554, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=555, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=556, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=557, 字幕#529
2025-07-29 20:05:58,832 - INFO - 找到related_between场景: scene_id=558, 字幕#529
2025-07-29 20:05:58,833 - INFO - 字幕 #524 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:58,833 - INFO - 字幕 #529 找到 2 个overlap场景, 9 个between场景
2025-07-29 20:05:58,833 - INFO - 共收集 3 个未使用的overlap场景和 9 个未使用的between场景
2025-07-29 20:05:58,833 - INFO - 开始生成方案 #1
2025-07-29 20:05:58,833 - INFO - 方案 #1: 为字幕#524选择初始化overlap场景id=544
2025-07-29 20:05:58,833 - INFO - 方案 #1: 为字幕#529选择初始化overlap场景id=548
2025-07-29 20:05:58,833 - INFO - 方案 #1: 初始选择后，当前总时长=2.72秒
2025-07-29 20:05:58,833 - INFO - 方案 #1: 额外添加overlap场景id=549, 当前总时长=3.80秒
2025-07-29 20:05:58,833 - INFO - 方案 #1: 额外between选择后，当前总时长=3.80秒
2025-07-29 20:05:58,833 - INFO - 方案 #1: 额外添加between场景id=557, 当前总时长=5.56秒
2025-07-29 20:05:58,833 - INFO - 方案 #1: 场景总时长(5.56秒)大于音频时长(4.85秒)，需要裁剪
2025-07-29 20:05:58,833 - INFO - 调整前总时长: 5.56秒, 目标时长: 4.85秒
2025-07-29 20:05:58,833 - INFO - 需要裁剪 0.71秒
2025-07-29 20:05:58,833 - INFO - 裁剪最长场景ID=548：从1.84秒裁剪至1.13秒
2025-07-29 20:05:58,833 - INFO - 调整后总时长: 4.85秒，与目标时长差异: 0.00秒
2025-07-29 20:05:58,833 - INFO - 方案 #1 调整/填充后最终总时长: 4.85秒
2025-07-29 20:05:58,833 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:58,833 - INFO - ========== 当前模式：字幕 #30 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:58,833 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:58,833 - INFO - ========== 新模式：字幕 #30 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:58,833 - INFO - 
----- 处理字幕 #30 的方案 #1 -----
2025-07-29 20:05:58,833 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 20:05:58,834 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp34z3648g
2025-07-29 20:05:58,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\544.mp4 (确认存在: True)
2025-07-29 20:05:58,834 - INFO - 添加场景ID=544，时长=0.88秒，累计时长=0.88秒
2025-07-29 20:05:58,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\545.mp4 (确认存在: True)
2025-07-29 20:05:58,834 - INFO - 添加场景ID=545，时长=1.92秒，累计时长=2.80秒
2025-07-29 20:05:58,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\546.mp4 (确认存在: True)
2025-07-29 20:05:58,834 - INFO - 添加场景ID=546，时长=1.44秒，累计时长=4.24秒
2025-07-29 20:05:58,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\547.mp4 (确认存在: True)
2025-07-29 20:05:58,834 - INFO - 添加场景ID=547，时长=2.28秒，累计时长=6.52秒
2025-07-29 20:05:58,835 - INFO - 准备合并 4 个场景文件，总时长约 6.52秒
2025-07-29 20:05:58,835 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/544.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/545.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/546.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/547.mp4'

2025-07-29 20:05:58,835 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp34z3648g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp34z3648g\temp_combined.mp4
2025-07-29 20:05:58,967 - INFO - 合并后的视频时长: 6.61秒，目标音频时长: 4.85秒
2025-07-29 20:05:58,968 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp34z3648g\temp_combined.mp4 -ss 0 -to 4.848 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 20:05:59,276 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:59,276 - INFO - 目标音频时长: 4.85秒
2025-07-29 20:05:59,276 - INFO - 实际视频时长: 4.90秒
2025-07-29 20:05:59,276 - INFO - 时长差异: 0.05秒 (1.13%)
2025-07-29 20:05:59,276 - INFO - ==========================================
2025-07-29 20:05:59,276 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:59,276 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 20:05:59,277 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp34z3648g
2025-07-29 20:05:59,321 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:59,321 - INFO -   - 音频时长: 4.85秒
2025-07-29 20:05:59,321 - INFO -   - 视频时长: 4.90秒
2025-07-29 20:05:59,321 - INFO -   - 时长差异: 0.05秒 (1.13%)
2025-07-29 20:05:59,321 - INFO - 
----- 处理字幕 #30 的方案 #2 -----
2025-07-29 20:05:59,321 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 20:05:59,322 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmzdnzeri
2025-07-29 20:05:59,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\548.mp4 (确认存在: True)
2025-07-29 20:05:59,322 - INFO - 添加场景ID=548，时长=1.84秒，累计时长=1.84秒
2025-07-29 20:05:59,323 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\549.mp4 (确认存在: True)
2025-07-29 20:05:59,323 - INFO - 添加场景ID=549，时长=1.08秒，累计时长=2.92秒
2025-07-29 20:05:59,323 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\550.mp4 (确认存在: True)
2025-07-29 20:05:59,323 - INFO - 添加场景ID=550，时长=1.44秒，累计时长=4.36秒
2025-07-29 20:05:59,323 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\551.mp4 (确认存在: True)
2025-07-29 20:05:59,323 - INFO - 添加场景ID=551，时长=1.20秒，累计时长=5.56秒
2025-07-29 20:05:59,323 - INFO - 准备合并 4 个场景文件，总时长约 5.56秒
2025-07-29 20:05:59,323 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/548.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/549.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/550.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/551.mp4'

2025-07-29 20:05:59,323 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmzdnzeri\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmzdnzeri\temp_combined.mp4
2025-07-29 20:05:59,485 - INFO - 合并后的视频时长: 5.65秒，目标音频时长: 4.85秒
2025-07-29 20:05:59,485 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmzdnzeri\temp_combined.mp4 -ss 0 -to 4.848 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 20:05:59,818 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:59,820 - INFO - 目标音频时长: 4.85秒
2025-07-29 20:05:59,820 - INFO - 实际视频时长: 4.90秒
2025-07-29 20:05:59,820 - INFO - 时长差异: 0.05秒 (1.13%)
2025-07-29 20:05:59,820 - INFO - ==========================================
2025-07-29 20:05:59,820 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:59,820 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 20:05:59,820 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmzdnzeri
2025-07-29 20:05:59,865 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:59,865 - INFO -   - 音频时长: 4.85秒
2025-07-29 20:05:59,865 - INFO -   - 视频时长: 4.90秒
2025-07-29 20:05:59,865 - INFO -   - 时长差异: 0.05秒 (1.13%)
2025-07-29 20:05:59,865 - INFO - 
----- 处理字幕 #30 的方案 #3 -----
2025-07-29 20:05:59,865 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-07-29 20:05:59,866 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6ndsl8jm
2025-07-29 20:05:59,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\544.mp4 (确认存在: True)
2025-07-29 20:05:59,867 - INFO - 添加场景ID=544，时长=0.88秒，累计时长=0.88秒
2025-07-29 20:05:59,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\548.mp4 (确认存在: True)
2025-07-29 20:05:59,867 - INFO - 添加场景ID=548，时长=1.84秒，累计时长=2.72秒
2025-07-29 20:05:59,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\549.mp4 (确认存在: True)
2025-07-29 20:05:59,867 - INFO - 添加场景ID=549，时长=1.08秒，累计时长=3.80秒
2025-07-29 20:05:59,867 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\557.mp4 (确认存在: True)
2025-07-29 20:05:59,867 - INFO - 添加场景ID=557，时长=1.76秒，累计时长=5.56秒
2025-07-29 20:05:59,867 - INFO - 准备合并 4 个场景文件，总时长约 5.56秒
2025-07-29 20:05:59,867 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/544.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/548.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/549.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/557.mp4'

2025-07-29 20:05:59,867 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6ndsl8jm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6ndsl8jm\temp_combined.mp4
2025-07-29 20:06:00,009 - INFO - 合并后的视频时长: 5.65秒，目标音频时长: 4.85秒
2025-07-29 20:06:00,009 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6ndsl8jm\temp_combined.mp4 -ss 0 -to 4.848 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-07-29 20:06:00,346 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:00,346 - INFO - 目标音频时长: 4.85秒
2025-07-29 20:06:00,346 - INFO - 实际视频时长: 4.90秒
2025-07-29 20:06:00,346 - INFO - 时长差异: 0.05秒 (1.13%)
2025-07-29 20:06:00,346 - INFO - ==========================================
2025-07-29 20:06:00,346 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:00,346 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-07-29 20:06:00,347 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6ndsl8jm
2025-07-29 20:06:00,392 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:00,392 - INFO -   - 音频时长: 4.85秒
2025-07-29 20:06:00,392 - INFO -   - 视频时长: 4.90秒
2025-07-29 20:06:00,392 - INFO -   - 时长差异: 0.05秒 (1.13%)
2025-07-29 20:06:00,392 - INFO - 
字幕 #30 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:00,392 - INFO - 生成的视频文件:
2025-07-29 20:06:00,392 - INFO -   1. F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 20:06:00,392 - INFO -   2. F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 20:06:00,392 - INFO -   3. F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-07-29 20:06:00,392 - INFO - ========== 字幕 #30 处理结束 ==========

