2025-07-29 20:06:58,316 - INFO - ========== 字幕 #71 处理开始 ==========
2025-07-29 20:06:58,316 - INFO - 字幕内容: 他冷冷地告诉她，不要再提安安，因为她不配！
2025-07-29 20:06:58,316 - INFO - 字幕序号: [2888, 2891]
2025-07-29 20:06:58,316 - INFO - 音频文件详情:
2025-07-29 20:06:58,316 - INFO -   - 路径: output\71.wav
2025-07-29 20:06:58,316 - INFO -   - 时长: 2.80秒
2025-07-29 20:06:58,316 - INFO -   - 验证音频时长: 2.80秒
2025-07-29 20:06:58,316 - INFO - 字幕时间戳信息:
2025-07-29 20:06:58,316 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:58,317 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:58,317 - INFO -   - 根据生成的音频时长(2.80秒)已调整字幕时间戳
2025-07-29 20:06:58,317 - INFO - ========== 新模式：为字幕 #71 生成4套场景方案 ==========
2025-07-29 20:06:58,317 - INFO - 字幕序号列表: [2888, 2891]
2025-07-29 20:06:58,317 - INFO - 
--- 生成方案 #1：基于字幕序号 #2888 ---
2025-07-29 20:06:58,317 - INFO - 开始为单个字幕序号 #2888 匹配场景，目标时长: 2.80秒
2025-07-29 20:06:58,317 - INFO - 开始查找字幕序号 [2888] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:58,317 - INFO - 找到related_overlap场景: scene_id=2679, 字幕#2888
2025-07-29 20:06:58,317 - INFO - 找到related_overlap场景: scene_id=2680, 字幕#2888
2025-07-29 20:06:58,318 - INFO - 字幕 #2888 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:58,318 - INFO - 字幕序号 #2888 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:58,318 - INFO - 选择第一个overlap场景作为起点: scene_id=2679
2025-07-29 20:06:58,318 - INFO - 添加起点场景: scene_id=2679, 时长=1.56秒, 累计时长=1.56秒
2025-07-29 20:06:58,318 - INFO - 起点场景时长不足，需要延伸填充 1.24秒
2025-07-29 20:06:58,318 - INFO - 起点场景在原始列表中的索引: 2678
2025-07-29 20:06:58,318 - INFO - 延伸添加场景: scene_id=2680 (裁剪至 1.24秒)
2025-07-29 20:06:58,318 - INFO - 累计时长: 2.80秒
2025-07-29 20:06:58,318 - INFO - 字幕序号 #2888 场景匹配完成，共选择 2 个场景，总时长: 2.80秒
2025-07-29 20:06:58,318 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:06:58,318 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:06:58,318 - INFO - 
--- 生成方案 #2：基于字幕序号 #2891 ---
2025-07-29 20:06:58,318 - INFO - 开始为单个字幕序号 #2891 匹配场景，目标时长: 2.80秒
2025-07-29 20:06:58,318 - INFO - 开始查找字幕序号 [2891] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:58,319 - INFO - 找到related_overlap场景: scene_id=2680, 字幕#2891
2025-07-29 20:06:58,320 - INFO - 字幕 #2891 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:58,320 - INFO - 字幕序号 #2891 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:58,320 - ERROR - 字幕序号 #2891 没有找到任何可用的匹配场景
2025-07-29 20:06:58,320 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:58,320 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:58,320 - INFO - ========== 当前模式：为字幕 #71 生成 1 套场景方案 ==========
2025-07-29 20:06:58,320 - INFO - 开始查找字幕序号 [2888, 2891] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:58,320 - INFO - 找到related_overlap场景: scene_id=2679, 字幕#2888
2025-07-29 20:06:58,320 - INFO - 找到related_overlap场景: scene_id=2680, 字幕#2888
2025-07-29 20:06:58,320 - INFO - 字幕 #2888 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:58,320 - INFO - 字幕 #2891 找到 0 个overlap场景, 0 个between场景
2025-07-29 20:06:58,320 - WARNING - 字幕 #2891 没有找到任何匹配场景!
2025-07-29 20:06:58,320 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:58,320 - INFO - 开始生成方案 #1
2025-07-29 20:06:58,320 - INFO - 方案 #1: 为字幕#2888选择初始化overlap场景id=2680
2025-07-29 20:06:58,320 - INFO - 方案 #1: 初始选择后，当前总时长=4.00秒
2025-07-29 20:06:58,320 - INFO - 方案 #1: 额外between选择后，当前总时长=4.00秒
2025-07-29 20:06:58,320 - INFO - 方案 #1: 场景总时长(4.00秒)大于音频时长(2.80秒)，需要裁剪
2025-07-29 20:06:58,320 - INFO - 调整前总时长: 4.00秒, 目标时长: 2.80秒
2025-07-29 20:06:58,320 - INFO - 需要裁剪 1.20秒
2025-07-29 20:06:58,321 - INFO - 裁剪最长场景ID=2680：从4.00秒裁剪至2.80秒
2025-07-29 20:06:58,321 - INFO - 调整后总时长: 2.80秒，与目标时长差异: 0.00秒
2025-07-29 20:06:58,321 - INFO - 方案 #1 调整/填充后最终总时长: 2.80秒
2025-07-29 20:06:58,321 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:58,321 - INFO - ========== 当前模式：字幕 #71 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:58,321 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:58,321 - INFO - ========== 新模式：字幕 #71 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:58,321 - INFO - 
----- 处理字幕 #71 的方案 #1 -----
2025-07-29 20:06:58,321 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 20:06:58,321 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp15lz7jjy
2025-07-29 20:06:58,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2679.mp4 (确认存在: True)
2025-07-29 20:06:58,322 - INFO - 添加场景ID=2679，时长=1.56秒，累计时长=1.56秒
2025-07-29 20:06:58,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2680.mp4 (确认存在: True)
2025-07-29 20:06:58,322 - INFO - 添加场景ID=2680，时长=4.00秒，累计时长=5.56秒
2025-07-29 20:06:58,322 - INFO - 场景总时长(5.56秒)已达到音频时长(2.80秒)的1.5倍，停止添加场景
2025-07-29 20:06:58,322 - INFO - 准备合并 2 个场景文件，总时长约 5.56秒
2025-07-29 20:06:58,322 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2679.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2680.mp4'

2025-07-29 20:06:58,322 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp15lz7jjy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp15lz7jjy\temp_combined.mp4
2025-07-29 20:06:58,455 - INFO - 合并后的视频时长: 5.61秒，目标音频时长: 2.80秒
2025-07-29 20:06:58,455 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp15lz7jjy\temp_combined.mp4 -ss 0 -to 2.799 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 20:06:58,713 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:58,713 - INFO - 目标音频时长: 2.80秒
2025-07-29 20:06:58,713 - INFO - 实际视频时长: 2.82秒
2025-07-29 20:06:58,713 - INFO - 时长差异: 0.02秒 (0.86%)
2025-07-29 20:06:58,713 - INFO - ==========================================
2025-07-29 20:06:58,713 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:58,713 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 20:06:58,714 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp15lz7jjy
2025-07-29 20:06:58,759 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:58,759 - INFO -   - 音频时长: 2.80秒
2025-07-29 20:06:58,759 - INFO -   - 视频时长: 2.82秒
2025-07-29 20:06:58,759 - INFO -   - 时长差异: 0.02秒 (0.86%)
2025-07-29 20:06:58,759 - INFO - 
----- 处理字幕 #71 的方案 #2 -----
2025-07-29 20:06:58,759 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 20:06:58,760 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsi3cwghn
2025-07-29 20:06:58,760 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2680.mp4 (确认存在: True)
2025-07-29 20:06:58,760 - INFO - 添加场景ID=2680，时长=4.00秒，累计时长=4.00秒
2025-07-29 20:06:58,760 - INFO - 准备合并 1 个场景文件，总时长约 4.00秒
2025-07-29 20:06:58,760 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2680.mp4'

2025-07-29 20:06:58,760 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsi3cwghn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsi3cwghn\temp_combined.mp4
2025-07-29 20:06:58,867 - INFO - 合并后的视频时长: 4.02秒，目标音频时长: 2.80秒
2025-07-29 20:06:58,867 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsi3cwghn\temp_combined.mp4 -ss 0 -to 2.799 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 20:06:59,094 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:59,094 - INFO - 目标音频时长: 2.80秒
2025-07-29 20:06:59,094 - INFO - 实际视频时长: 2.82秒
2025-07-29 20:06:59,094 - INFO - 时长差异: 0.02秒 (0.86%)
2025-07-29 20:06:59,094 - INFO - ==========================================
2025-07-29 20:06:59,094 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:59,094 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 20:06:59,095 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsi3cwghn
2025-07-29 20:06:59,139 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:59,139 - INFO -   - 音频时长: 2.80秒
2025-07-29 20:06:59,139 - INFO -   - 视频时长: 2.82秒
2025-07-29 20:06:59,139 - INFO -   - 时长差异: 0.02秒 (0.86%)
2025-07-29 20:06:59,139 - INFO - 
字幕 #71 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:59,139 - INFO - 生成的视频文件:
2025-07-29 20:06:59,139 - INFO -   1. F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 20:06:59,139 - INFO -   2. F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 20:06:59,139 - INFO - ========== 字幕 #71 处理结束 ==========

