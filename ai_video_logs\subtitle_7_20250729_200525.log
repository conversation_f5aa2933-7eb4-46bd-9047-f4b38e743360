2025-07-29 20:05:25,197 - INFO - ========== 字幕 #7 处理开始 ==========
2025-07-29 20:05:25,197 - INFO - 字幕内容: 女人见状，立刻将男人拉到一旁，低声警告他不要破坏自己的“假结婚”计划。
2025-07-29 20:05:25,198 - INFO - 字幕序号: [155, 158]
2025-07-29 20:05:25,198 - INFO - 音频文件详情:
2025-07-29 20:05:25,198 - INFO -   - 路径: output\7.wav
2025-07-29 20:05:25,198 - INFO -   - 时长: 4.55秒
2025-07-29 20:05:25,198 - INFO -   - 验证音频时长: 4.55秒
2025-07-29 20:05:25,198 - INFO - 字幕时间戳信息:
2025-07-29 20:05:25,207 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:25,207 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:25,207 - INFO -   - 根据生成的音频时长(4.55秒)已调整字幕时间戳
2025-07-29 20:05:25,208 - INFO - ========== 新模式：为字幕 #7 生成4套场景方案 ==========
2025-07-29 20:05:25,208 - INFO - 字幕序号列表: [155, 158]
2025-07-29 20:05:25,208 - INFO - 
--- 生成方案 #1：基于字幕序号 #155 ---
2025-07-29 20:05:25,208 - INFO - 开始为单个字幕序号 #155 匹配场景，目标时长: 4.55秒
2025-07-29 20:05:25,208 - INFO - 开始查找字幕序号 [155] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:25,208 - INFO - 找到related_overlap场景: scene_id=178, 字幕#155
2025-07-29 20:05:25,209 - INFO - 字幕 #155 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:25,209 - INFO - 字幕序号 #155 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:25,209 - INFO - 选择第一个overlap场景作为起点: scene_id=178
2025-07-29 20:05:25,209 - INFO - 添加起点场景: scene_id=178, 时长=1.08秒, 累计时长=1.08秒
2025-07-29 20:05:25,209 - INFO - 起点场景时长不足，需要延伸填充 3.47秒
2025-07-29 20:05:25,209 - INFO - 起点场景在原始列表中的索引: 177
2025-07-29 20:05:25,209 - INFO - 延伸添加场景: scene_id=179 (完整时长 1.60秒)
2025-07-29 20:05:25,209 - INFO - 累计时长: 2.68秒
2025-07-29 20:05:25,209 - INFO - 延伸添加场景: scene_id=180 (完整时长 1.16秒)
2025-07-29 20:05:25,209 - INFO - 累计时长: 3.84秒
2025-07-29 20:05:25,209 - INFO - 延伸添加场景: scene_id=181 (完整时长 0.68秒)
2025-07-29 20:05:25,209 - INFO - 累计时长: 4.52秒
2025-07-29 20:05:25,209 - INFO - 延伸添加场景: scene_id=182 (裁剪至 0.03秒)
2025-07-29 20:05:25,209 - INFO - 累计时长: 4.55秒
2025-07-29 20:05:25,209 - INFO - 字幕序号 #155 场景匹配完成，共选择 5 个场景，总时长: 4.55秒
2025-07-29 20:05:25,209 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:25,209 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:25,209 - INFO - 
--- 生成方案 #2：基于字幕序号 #158 ---
2025-07-29 20:05:25,209 - INFO - 开始为单个字幕序号 #158 匹配场景，目标时长: 4.55秒
2025-07-29 20:05:25,209 - INFO - 开始查找字幕序号 [158] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:25,209 - INFO - 找到related_overlap场景: scene_id=180, 字幕#158
2025-07-29 20:05:25,209 - INFO - 找到related_overlap场景: scene_id=181, 字幕#158
2025-07-29 20:05:25,210 - INFO - 字幕 #158 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:25,210 - INFO - 字幕序号 #158 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:25,210 - ERROR - 字幕序号 #158 没有找到任何可用的匹配场景
2025-07-29 20:05:25,210 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:05:25,210 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:05:25,210 - INFO - ========== 当前模式：为字幕 #7 生成 1 套场景方案 ==========
2025-07-29 20:05:25,210 - INFO - 开始查找字幕序号 [155, 158] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:25,210 - INFO - 找到related_overlap场景: scene_id=178, 字幕#155
2025-07-29 20:05:25,210 - INFO - 找到related_overlap场景: scene_id=180, 字幕#158
2025-07-29 20:05:25,210 - INFO - 找到related_overlap场景: scene_id=181, 字幕#158
2025-07-29 20:05:25,211 - INFO - 字幕 #155 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:25,211 - INFO - 字幕 #158 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:25,211 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:25,211 - INFO - 开始生成方案 #1
2025-07-29 20:05:25,211 - INFO - 方案 #1: 为字幕#155选择初始化overlap场景id=178
2025-07-29 20:05:25,211 - INFO - 方案 #1: 为字幕#158选择初始化overlap场景id=181
2025-07-29 20:05:25,211 - INFO - 方案 #1: 初始选择后，当前总时长=1.76秒
2025-07-29 20:05:25,211 - INFO - 方案 #1: 额外添加overlap场景id=180, 当前总时长=2.92秒
2025-07-29 20:05:25,212 - INFO - 方案 #1: 额外between选择后，当前总时长=2.92秒
2025-07-29 20:05:25,212 - INFO - 方案 #1: 场景总时长(2.92秒)小于音频时长(4.55秒)，需要延伸填充
2025-07-29 20:05:25,212 - INFO - 方案 #1: 最后一个场景ID: 180
2025-07-29 20:05:25,212 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 179
2025-07-29 20:05:25,212 - INFO - 方案 #1: 需要填充时长: 1.63秒
2025-07-29 20:05:25,212 - INFO - 方案 #1: 跳过已使用的场景: scene_id=181
2025-07-29 20:05:25,212 - INFO - 方案 #1: 追加场景 scene_id=182 (完整时长 1.28秒)
2025-07-29 20:05:25,212 - INFO - 方案 #1: 追加场景 scene_id=183 (裁剪至 0.36秒)
2025-07-29 20:05:25,212 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:25,212 - INFO - 方案 #1 调整/填充后最终总时长: 4.55秒
2025-07-29 20:05:25,212 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:25,212 - INFO - ========== 当前模式：字幕 #7 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:25,212 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:05:25,212 - INFO - ========== 新模式：字幕 #7 共生成 2 套有效场景方案 ==========
2025-07-29 20:05:25,212 - INFO - 
----- 处理字幕 #7 的方案 #1 -----
2025-07-29 20:05:25,212 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 20:05:25,212 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpty076jj0
2025-07-29 20:05:25,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\178.mp4 (确认存在: True)
2025-07-29 20:05:25,213 - INFO - 添加场景ID=178，时长=1.08秒，累计时长=1.08秒
2025-07-29 20:05:25,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\179.mp4 (确认存在: True)
2025-07-29 20:05:25,213 - INFO - 添加场景ID=179，时长=1.60秒，累计时长=2.68秒
2025-07-29 20:05:25,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\180.mp4 (确认存在: True)
2025-07-29 20:05:25,213 - INFO - 添加场景ID=180，时长=1.16秒，累计时长=3.84秒
2025-07-29 20:05:25,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\181.mp4 (确认存在: True)
2025-07-29 20:05:25,213 - INFO - 添加场景ID=181，时长=0.68秒，累计时长=4.52秒
2025-07-29 20:05:25,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\182.mp4 (确认存在: True)
2025-07-29 20:05:25,213 - INFO - 添加场景ID=182，时长=1.28秒，累计时长=5.80秒
2025-07-29 20:05:25,213 - INFO - 准备合并 5 个场景文件，总时长约 5.80秒
2025-07-29 20:05:25,213 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/178.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/179.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/180.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/181.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/182.mp4'

2025-07-29 20:05:25,213 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpty076jj0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpty076jj0\temp_combined.mp4
2025-07-29 20:05:25,391 - INFO - 合并后的视频时长: 5.92秒，目标音频时长: 4.55秒
2025-07-29 20:05:25,391 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpty076jj0\temp_combined.mp4 -ss 0 -to 4.553 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 20:05:25,717 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:25,717 - INFO - 目标音频时长: 4.55秒
2025-07-29 20:05:25,717 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:05:25,717 - INFO - 时长差异: 0.03秒 (0.66%)
2025-07-29 20:05:25,717 - INFO - ==========================================
2025-07-29 20:05:25,717 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:25,717 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 20:05:25,718 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpty076jj0
2025-07-29 20:05:25,763 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:25,763 - INFO -   - 音频时长: 4.55秒
2025-07-29 20:05:25,763 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:05:25,763 - INFO -   - 时长差异: 0.03秒 (0.66%)
2025-07-29 20:05:25,763 - INFO - 
----- 处理字幕 #7 的方案 #2 -----
2025-07-29 20:05:25,763 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 20:05:25,763 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_3l2hapd
2025-07-29 20:05:25,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\178.mp4 (确认存在: True)
2025-07-29 20:05:25,764 - INFO - 添加场景ID=178，时长=1.08秒，累计时长=1.08秒
2025-07-29 20:05:25,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\181.mp4 (确认存在: True)
2025-07-29 20:05:25,764 - INFO - 添加场景ID=181，时长=0.68秒，累计时长=1.76秒
2025-07-29 20:05:25,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\180.mp4 (确认存在: True)
2025-07-29 20:05:25,764 - INFO - 添加场景ID=180，时长=1.16秒，累计时长=2.92秒
2025-07-29 20:05:25,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\182.mp4 (确认存在: True)
2025-07-29 20:05:25,764 - INFO - 添加场景ID=182，时长=1.28秒，累计时长=4.20秒
2025-07-29 20:05:25,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\183.mp4 (确认存在: True)
2025-07-29 20:05:25,764 - INFO - 添加场景ID=183，时长=3.44秒，累计时长=7.64秒
2025-07-29 20:05:25,764 - INFO - 场景总时长(7.64秒)已达到音频时长(4.55秒)的1.5倍，停止添加场景
2025-07-29 20:05:25,764 - INFO - 准备合并 5 个场景文件，总时长约 7.64秒
2025-07-29 20:05:25,764 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/178.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/181.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/180.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/182.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/183.mp4'

2025-07-29 20:05:25,765 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_3l2hapd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_3l2hapd\temp_combined.mp4
2025-07-29 20:05:25,944 - INFO - 合并后的视频时长: 7.76秒，目标音频时长: 4.55秒
2025-07-29 20:05:25,944 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_3l2hapd\temp_combined.mp4 -ss 0 -to 4.553 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 20:05:26,265 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:26,265 - INFO - 目标音频时长: 4.55秒
2025-07-29 20:05:26,265 - INFO - 实际视频时长: 4.58秒
2025-07-29 20:05:26,265 - INFO - 时长差异: 0.03秒 (0.66%)
2025-07-29 20:05:26,265 - INFO - ==========================================
2025-07-29 20:05:26,265 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:26,265 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 20:05:26,266 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_3l2hapd
2025-07-29 20:05:26,311 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:26,311 - INFO -   - 音频时长: 4.55秒
2025-07-29 20:05:26,311 - INFO -   - 视频时长: 4.58秒
2025-07-29 20:05:26,311 - INFO -   - 时长差异: 0.03秒 (0.66%)
2025-07-29 20:05:26,311 - INFO - 
字幕 #7 处理完成，成功生成 2/2 套方案
2025-07-29 20:05:26,311 - INFO - 生成的视频文件:
2025-07-29 20:05:26,311 - INFO -   1. F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 20:05:26,311 - INFO -   2. F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 20:05:26,311 - INFO - ========== 字幕 #7 处理结束 ==========

