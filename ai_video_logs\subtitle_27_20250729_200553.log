2025-07-29 20:05:53,896 - INFO - ========== 字幕 #27 处理开始 ==========
2025-07-29 20:05:53,896 - INFO - 字幕内容: 他控诉着，七年来，女人视他为攀高枝的凤凰男，没收他全部工资，每个月只给两百块生活费。
2025-07-29 20:05:53,896 - INFO - 字幕序号: [490, 503]
2025-07-29 20:05:53,897 - INFO - 音频文件详情:
2025-07-29 20:05:53,897 - INFO -   - 路径: output\27.wav
2025-07-29 20:05:53,897 - INFO -   - 时长: 6.87秒
2025-07-29 20:05:53,897 - INFO -   - 验证音频时长: 6.87秒
2025-07-29 20:05:53,897 - INFO - 字幕时间戳信息:
2025-07-29 20:05:53,906 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:53,906 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:53,906 - INFO -   - 根据生成的音频时长(6.87秒)已调整字幕时间戳
2025-07-29 20:05:53,906 - INFO - ========== 新模式：为字幕 #27 生成4套场景方案 ==========
2025-07-29 20:05:53,906 - INFO - 字幕序号列表: [490, 503]
2025-07-29 20:05:53,906 - INFO - 
--- 生成方案 #1：基于字幕序号 #490 ---
2025-07-29 20:05:53,906 - INFO - 开始为单个字幕序号 #490 匹配场景，目标时长: 6.87秒
2025-07-29 20:05:53,906 - INFO - 开始查找字幕序号 [490] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:53,907 - INFO - 找到related_overlap场景: scene_id=525, 字幕#490
2025-07-29 20:05:53,907 - INFO - 找到related_between场景: scene_id=522, 字幕#490
2025-07-29 20:05:53,907 - INFO - 找到related_between场景: scene_id=523, 字幕#490
2025-07-29 20:05:53,907 - INFO - 找到related_between场景: scene_id=524, 字幕#490
2025-07-29 20:05:53,908 - INFO - 字幕 #490 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:05:53,908 - INFO - 字幕序号 #490 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 20:05:53,908 - INFO - 选择第一个overlap场景作为起点: scene_id=525
2025-07-29 20:05:53,908 - INFO - 添加起点场景: scene_id=525, 时长=1.16秒, 累计时长=1.16秒
2025-07-29 20:05:53,908 - INFO - 起点场景时长不足，需要延伸填充 5.71秒
2025-07-29 20:05:53,908 - INFO - 起点场景在原始列表中的索引: 524
2025-07-29 20:05:53,908 - INFO - 延伸添加场景: scene_id=526 (完整时长 1.00秒)
2025-07-29 20:05:53,908 - INFO - 累计时长: 2.16秒
2025-07-29 20:05:53,908 - INFO - 延伸添加场景: scene_id=527 (完整时长 1.72秒)
2025-07-29 20:05:53,908 - INFO - 累计时长: 3.88秒
2025-07-29 20:05:53,908 - INFO - 延伸添加场景: scene_id=528 (完整时长 1.60秒)
2025-07-29 20:05:53,908 - INFO - 累计时长: 5.48秒
2025-07-29 20:05:53,908 - INFO - 延伸添加场景: scene_id=529 (裁剪至 1.39秒)
2025-07-29 20:05:53,908 - INFO - 累计时长: 6.87秒
2025-07-29 20:05:53,908 - INFO - 字幕序号 #490 场景匹配完成，共选择 5 个场景，总时长: 6.87秒
2025-07-29 20:05:53,908 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:53,908 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:53,908 - INFO - 
--- 生成方案 #2：基于字幕序号 #503 ---
2025-07-29 20:05:53,908 - INFO - 开始为单个字幕序号 #503 匹配场景，目标时长: 6.87秒
2025-07-29 20:05:53,908 - INFO - 开始查找字幕序号 [503] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:53,908 - INFO - 找到related_overlap场景: scene_id=532, 字幕#503
2025-07-29 20:05:53,909 - INFO - 字幕 #503 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:53,909 - INFO - 字幕序号 #503 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:53,909 - INFO - 选择第一个overlap场景作为起点: scene_id=532
2025-07-29 20:05:53,909 - INFO - 添加起点场景: scene_id=532, 时长=2.44秒, 累计时长=2.44秒
2025-07-29 20:05:53,909 - INFO - 起点场景时长不足，需要延伸填充 4.43秒
2025-07-29 20:05:53,910 - INFO - 起点场景在原始列表中的索引: 531
2025-07-29 20:05:53,910 - INFO - 延伸添加场景: scene_id=533 (完整时长 3.88秒)
2025-07-29 20:05:53,910 - INFO - 累计时长: 6.32秒
2025-07-29 20:05:53,910 - INFO - 延伸添加场景: scene_id=534 (裁剪至 0.55秒)
2025-07-29 20:05:53,910 - INFO - 累计时长: 6.87秒
2025-07-29 20:05:53,910 - INFO - 字幕序号 #503 场景匹配完成，共选择 3 个场景，总时长: 6.87秒
2025-07-29 20:05:53,910 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:53,910 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:53,910 - INFO - ========== 当前模式：为字幕 #27 生成 1 套场景方案 ==========
2025-07-29 20:05:53,910 - INFO - 开始查找字幕序号 [490, 503] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:53,910 - INFO - 找到related_overlap场景: scene_id=525, 字幕#490
2025-07-29 20:05:53,910 - INFO - 找到related_overlap场景: scene_id=532, 字幕#503
2025-07-29 20:05:53,910 - INFO - 找到related_between场景: scene_id=522, 字幕#490
2025-07-29 20:05:53,910 - INFO - 找到related_between场景: scene_id=523, 字幕#490
2025-07-29 20:05:53,910 - INFO - 找到related_between场景: scene_id=524, 字幕#490
2025-07-29 20:05:53,911 - INFO - 字幕 #490 找到 1 个overlap场景, 3 个between场景
2025-07-29 20:05:53,911 - INFO - 字幕 #503 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:53,911 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:05:53,911 - INFO - 开始生成方案 #1
2025-07-29 20:05:53,911 - INFO - 方案 #1: 为字幕#490选择初始化overlap场景id=525
2025-07-29 20:05:53,911 - INFO - 方案 #1: 为字幕#503选择初始化overlap场景id=532
2025-07-29 20:05:53,911 - INFO - 方案 #1: 初始选择后，当前总时长=3.60秒
2025-07-29 20:05:53,911 - INFO - 方案 #1: 额外between选择后，当前总时长=3.60秒
2025-07-29 20:05:53,911 - INFO - 方案 #1: 额外添加between场景id=522, 当前总时长=5.48秒
2025-07-29 20:05:53,911 - INFO - 方案 #1: 额外添加between场景id=523, 当前总时长=6.32秒
2025-07-29 20:05:53,911 - INFO - 方案 #1: 额外添加between场景id=524, 当前总时长=8.12秒
2025-07-29 20:05:53,911 - INFO - 方案 #1: 场景总时长(8.12秒)大于音频时长(6.87秒)，需要裁剪
2025-07-29 20:05:53,911 - INFO - 调整前总时长: 8.12秒, 目标时长: 6.87秒
2025-07-29 20:05:53,911 - INFO - 需要裁剪 1.25秒
2025-07-29 20:05:53,911 - INFO - 裁剪最长场景ID=532：从2.44秒裁剪至1.19秒
2025-07-29 20:05:53,911 - INFO - 调整后总时长: 6.87秒，与目标时长差异: 0.00秒
2025-07-29 20:05:53,911 - INFO - 方案 #1 调整/填充后最终总时长: 6.87秒
2025-07-29 20:05:53,911 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:53,911 - INFO - ========== 当前模式：字幕 #27 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:53,911 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:53,911 - INFO - ========== 新模式：字幕 #27 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:53,911 - INFO - 
----- 处理字幕 #27 的方案 #1 -----
2025-07-29 20:05:53,911 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 20:05:53,912 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuiidaj_m
2025-07-29 20:05:53,912 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\525.mp4 (确认存在: True)
2025-07-29 20:05:53,912 - INFO - 添加场景ID=525，时长=1.16秒，累计时长=1.16秒
2025-07-29 20:05:53,912 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\526.mp4 (确认存在: True)
2025-07-29 20:05:53,912 - INFO - 添加场景ID=526，时长=1.00秒，累计时长=2.16秒
2025-07-29 20:05:53,912 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\527.mp4 (确认存在: True)
2025-07-29 20:05:53,912 - INFO - 添加场景ID=527，时长=1.72秒，累计时长=3.88秒
2025-07-29 20:05:53,913 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\528.mp4 (确认存在: True)
2025-07-29 20:05:53,913 - INFO - 添加场景ID=528，时长=1.60秒，累计时长=5.48秒
2025-07-29 20:05:53,913 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\529.mp4 (确认存在: True)
2025-07-29 20:05:53,913 - INFO - 添加场景ID=529，时长=2.76秒，累计时长=8.24秒
2025-07-29 20:05:53,913 - INFO - 准备合并 5 个场景文件，总时长约 8.24秒
2025-07-29 20:05:53,913 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/525.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/527.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/528.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/529.mp4'

2025-07-29 20:05:53,913 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuiidaj_m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuiidaj_m\temp_combined.mp4
2025-07-29 20:05:54,077 - INFO - 合并后的视频时长: 8.36秒，目标音频时长: 6.87秒
2025-07-29 20:05:54,077 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuiidaj_m\temp_combined.mp4 -ss 0 -to 6.867 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 20:05:54,456 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:54,456 - INFO - 目标音频时长: 6.87秒
2025-07-29 20:05:54,456 - INFO - 实际视频时长: 6.90秒
2025-07-29 20:05:54,456 - INFO - 时长差异: 0.04秒 (0.52%)
2025-07-29 20:05:54,456 - INFO - ==========================================
2025-07-29 20:05:54,456 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:54,456 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 20:05:54,457 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuiidaj_m
2025-07-29 20:05:54,505 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:54,505 - INFO -   - 音频时长: 6.87秒
2025-07-29 20:05:54,505 - INFO -   - 视频时长: 6.90秒
2025-07-29 20:05:54,505 - INFO -   - 时长差异: 0.04秒 (0.52%)
2025-07-29 20:05:54,505 - INFO - 
----- 处理字幕 #27 的方案 #2 -----
2025-07-29 20:05:54,505 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 20:05:54,505 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfsh2nz9x
2025-07-29 20:05:54,506 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\532.mp4 (确认存在: True)
2025-07-29 20:05:54,506 - INFO - 添加场景ID=532，时长=2.44秒，累计时长=2.44秒
2025-07-29 20:05:54,506 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\533.mp4 (确认存在: True)
2025-07-29 20:05:54,506 - INFO - 添加场景ID=533，时长=3.88秒，累计时长=6.32秒
2025-07-29 20:05:54,506 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\534.mp4 (确认存在: True)
2025-07-29 20:05:54,506 - INFO - 添加场景ID=534，时长=0.88秒，累计时长=7.20秒
2025-07-29 20:05:54,506 - INFO - 准备合并 3 个场景文件，总时长约 7.20秒
2025-07-29 20:05:54,506 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/532.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/533.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/534.mp4'

2025-07-29 20:05:54,506 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfsh2nz9x\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfsh2nz9x\temp_combined.mp4
2025-07-29 20:05:54,649 - INFO - 合并后的视频时长: 7.27秒，目标音频时长: 6.87秒
2025-07-29 20:05:54,649 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfsh2nz9x\temp_combined.mp4 -ss 0 -to 6.867 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 20:05:55,015 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:55,015 - INFO - 目标音频时长: 6.87秒
2025-07-29 20:05:55,015 - INFO - 实际视频时长: 6.90秒
2025-07-29 20:05:55,015 - INFO - 时长差异: 0.04秒 (0.52%)
2025-07-29 20:05:55,015 - INFO - ==========================================
2025-07-29 20:05:55,015 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:55,015 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 20:05:55,016 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfsh2nz9x
2025-07-29 20:05:55,061 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:55,061 - INFO -   - 音频时长: 6.87秒
2025-07-29 20:05:55,061 - INFO -   - 视频时长: 6.90秒
2025-07-29 20:05:55,061 - INFO -   - 时长差异: 0.04秒 (0.52%)
2025-07-29 20:05:55,061 - INFO - 
----- 处理字幕 #27 的方案 #3 -----
2025-07-29 20:05:55,061 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 20:05:55,062 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuvhlttso
2025-07-29 20:05:55,062 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\525.mp4 (确认存在: True)
2025-07-29 20:05:55,062 - INFO - 添加场景ID=525，时长=1.16秒，累计时长=1.16秒
2025-07-29 20:05:55,062 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\532.mp4 (确认存在: True)
2025-07-29 20:05:55,062 - INFO - 添加场景ID=532，时长=2.44秒，累计时长=3.60秒
2025-07-29 20:05:55,062 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\522.mp4 (确认存在: True)
2025-07-29 20:05:55,062 - INFO - 添加场景ID=522，时长=1.88秒，累计时长=5.48秒
2025-07-29 20:05:55,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\523.mp4 (确认存在: True)
2025-07-29 20:05:55,063 - INFO - 添加场景ID=523，时长=0.84秒，累计时长=6.32秒
2025-07-29 20:05:55,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\524.mp4 (确认存在: True)
2025-07-29 20:05:55,063 - INFO - 添加场景ID=524，时长=1.80秒，累计时长=8.12秒
2025-07-29 20:05:55,063 - INFO - 准备合并 5 个场景文件，总时长约 8.12秒
2025-07-29 20:05:55,063 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/525.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/532.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/522.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/523.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/524.mp4'

2025-07-29 20:05:55,063 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuvhlttso\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuvhlttso\temp_combined.mp4
2025-07-29 20:05:55,203 - INFO - 合并后的视频时长: 8.24秒，目标音频时长: 6.87秒
2025-07-29 20:05:55,203 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuvhlttso\temp_combined.mp4 -ss 0 -to 6.867 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 20:05:55,579 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:55,579 - INFO - 目标音频时长: 6.87秒
2025-07-29 20:05:55,579 - INFO - 实际视频时长: 6.90秒
2025-07-29 20:05:55,579 - INFO - 时长差异: 0.04秒 (0.52%)
2025-07-29 20:05:55,579 - INFO - ==========================================
2025-07-29 20:05:55,579 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:55,579 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 20:05:55,580 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuvhlttso
2025-07-29 20:05:55,623 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:55,623 - INFO -   - 音频时长: 6.87秒
2025-07-29 20:05:55,623 - INFO -   - 视频时长: 6.90秒
2025-07-29 20:05:55,623 - INFO -   - 时长差异: 0.04秒 (0.52%)
2025-07-29 20:05:55,624 - INFO - 
字幕 #27 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:55,624 - INFO - 生成的视频文件:
2025-07-29 20:05:55,624 - INFO -   1. F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 20:05:55,624 - INFO -   2. F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 20:05:55,624 - INFO -   3. F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 20:05:55,624 - INFO - ========== 字幕 #27 处理结束 ==========

