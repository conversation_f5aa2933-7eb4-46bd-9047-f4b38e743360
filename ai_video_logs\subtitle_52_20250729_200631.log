2025-07-29 20:06:31,197 - INFO - ========== 字幕 #52 处理开始 ==========
2025-07-29 20:06:31,197 - INFO - 字幕内容: 国内互联网巨头林总出面，直言新欢拿出的计划书狗屁不通，An先生绝不可能看上这种垃圾。
2025-07-29 20:06:31,197 - INFO - 字幕序号: [2137, 2142]
2025-07-29 20:06:31,197 - INFO - 音频文件详情:
2025-07-29 20:06:31,197 - INFO -   - 路径: output\52.wav
2025-07-29 20:06:31,197 - INFO -   - 时长: 7.09秒
2025-07-29 20:06:31,197 - INFO -   - 验证音频时长: 7.09秒
2025-07-29 20:06:31,197 - INFO - 字幕时间戳信息:
2025-07-29 20:06:31,208 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:31,208 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:31,208 - INFO -   - 根据生成的音频时长(7.09秒)已调整字幕时间戳
2025-07-29 20:06:31,208 - INFO - ========== 新模式：为字幕 #52 生成4套场景方案 ==========
2025-07-29 20:06:31,208 - INFO - 字幕序号列表: [2137, 2142]
2025-07-29 20:06:31,208 - INFO - 
--- 生成方案 #1：基于字幕序号 #2137 ---
2025-07-29 20:06:31,208 - INFO - 开始为单个字幕序号 #2137 匹配场景，目标时长: 7.09秒
2025-07-29 20:06:31,208 - INFO - 开始查找字幕序号 [2137] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:31,208 - INFO - 找到related_overlap场景: scene_id=2101, 字幕#2137
2025-07-29 20:06:31,209 - INFO - 找到related_between场景: scene_id=2096, 字幕#2137
2025-07-29 20:06:31,209 - INFO - 找到related_between场景: scene_id=2097, 字幕#2137
2025-07-29 20:06:31,209 - INFO - 找到related_between场景: scene_id=2098, 字幕#2137
2025-07-29 20:06:31,209 - INFO - 找到related_between场景: scene_id=2099, 字幕#2137
2025-07-29 20:06:31,209 - INFO - 找到related_between场景: scene_id=2100, 字幕#2137
2025-07-29 20:06:31,209 - INFO - 字幕 #2137 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:06:31,209 - INFO - 字幕序号 #2137 找到 1 个可用overlap场景, 5 个可用between场景
2025-07-29 20:06:31,209 - INFO - 选择第一个overlap场景作为起点: scene_id=2101
2025-07-29 20:06:31,209 - INFO - 添加起点场景: scene_id=2101, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 20:06:31,209 - INFO - 起点场景时长不足，需要延伸填充 5.37秒
2025-07-29 20:06:31,209 - INFO - 起点场景在原始列表中的索引: 2100
2025-07-29 20:06:31,209 - INFO - 延伸添加场景: scene_id=2102 (完整时长 1.24秒)
2025-07-29 20:06:31,209 - INFO - 累计时长: 2.96秒
2025-07-29 20:06:31,209 - INFO - 延伸添加场景: scene_id=2103 (完整时长 2.72秒)
2025-07-29 20:06:31,209 - INFO - 累计时长: 5.68秒
2025-07-29 20:06:31,209 - INFO - 延伸添加场景: scene_id=2104 (完整时长 1.16秒)
2025-07-29 20:06:31,209 - INFO - 累计时长: 6.84秒
2025-07-29 20:06:31,209 - INFO - 延伸添加场景: scene_id=2105 (裁剪至 0.25秒)
2025-07-29 20:06:31,209 - INFO - 累计时长: 7.09秒
2025-07-29 20:06:31,209 - INFO - 字幕序号 #2137 场景匹配完成，共选择 5 个场景，总时长: 7.09秒
2025-07-29 20:06:31,209 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:06:31,209 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:06:31,209 - INFO - 
--- 生成方案 #2：基于字幕序号 #2142 ---
2025-07-29 20:06:31,209 - INFO - 开始为单个字幕序号 #2142 匹配场景，目标时长: 7.09秒
2025-07-29 20:06:31,209 - INFO - 开始查找字幕序号 [2142] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:31,210 - INFO - 找到related_overlap场景: scene_id=2105, 字幕#2142
2025-07-29 20:06:31,210 - INFO - 找到related_overlap场景: scene_id=2106, 字幕#2142
2025-07-29 20:06:31,210 - INFO - 字幕 #2142 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:31,210 - INFO - 字幕序号 #2142 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:31,210 - INFO - 选择第一个overlap场景作为起点: scene_id=2106
2025-07-29 20:06:31,210 - INFO - 添加起点场景: scene_id=2106, 时长=0.96秒, 累计时长=0.96秒
2025-07-29 20:06:31,210 - INFO - 起点场景时长不足，需要延伸填充 6.13秒
2025-07-29 20:06:31,210 - INFO - 起点场景在原始列表中的索引: 2105
2025-07-29 20:06:31,211 - INFO - 延伸添加场景: scene_id=2107 (完整时长 3.32秒)
2025-07-29 20:06:31,211 - INFO - 累计时长: 4.28秒
2025-07-29 20:06:31,211 - INFO - 延伸添加场景: scene_id=2108 (完整时长 1.72秒)
2025-07-29 20:06:31,211 - INFO - 累计时长: 6.00秒
2025-07-29 20:06:31,211 - INFO - 延伸添加场景: scene_id=2109 (裁剪至 1.09秒)
2025-07-29 20:06:31,211 - INFO - 累计时长: 7.09秒
2025-07-29 20:06:31,211 - INFO - 字幕序号 #2142 场景匹配完成，共选择 4 个场景，总时长: 7.09秒
2025-07-29 20:06:31,211 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:31,211 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:31,211 - INFO - ========== 当前模式：为字幕 #52 生成 1 套场景方案 ==========
2025-07-29 20:06:31,211 - INFO - 开始查找字幕序号 [2137, 2142] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:31,211 - INFO - 找到related_overlap场景: scene_id=2101, 字幕#2137
2025-07-29 20:06:31,211 - INFO - 找到related_overlap场景: scene_id=2105, 字幕#2142
2025-07-29 20:06:31,211 - INFO - 找到related_overlap场景: scene_id=2106, 字幕#2142
2025-07-29 20:06:31,212 - INFO - 找到related_between场景: scene_id=2096, 字幕#2137
2025-07-29 20:06:31,212 - INFO - 找到related_between场景: scene_id=2097, 字幕#2137
2025-07-29 20:06:31,212 - INFO - 找到related_between场景: scene_id=2098, 字幕#2137
2025-07-29 20:06:31,212 - INFO - 找到related_between场景: scene_id=2099, 字幕#2137
2025-07-29 20:06:31,212 - INFO - 找到related_between场景: scene_id=2100, 字幕#2137
2025-07-29 20:06:31,212 - INFO - 字幕 #2137 找到 1 个overlap场景, 5 个between场景
2025-07-29 20:06:31,212 - INFO - 字幕 #2142 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:31,212 - INFO - 共收集 3 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 20:06:31,212 - INFO - 开始生成方案 #1
2025-07-29 20:06:31,212 - INFO - 方案 #1: 为字幕#2137选择初始化overlap场景id=2101
2025-07-29 20:06:31,212 - INFO - 方案 #1: 为字幕#2142选择初始化overlap场景id=2105
2025-07-29 20:06:31,212 - INFO - 方案 #1: 初始选择后，当前总时长=3.04秒
2025-07-29 20:06:31,212 - INFO - 方案 #1: 额外添加overlap场景id=2106, 当前总时长=4.00秒
2025-07-29 20:06:31,212 - INFO - 方案 #1: 额外between选择后，当前总时长=4.00秒
2025-07-29 20:06:31,212 - INFO - 方案 #1: 额外添加between场景id=2100, 当前总时长=5.96秒
2025-07-29 20:06:31,212 - INFO - 方案 #1: 额外添加between场景id=2096, 当前总时长=7.52秒
2025-07-29 20:06:31,212 - INFO - 方案 #1: 场景总时长(7.52秒)大于音频时长(7.09秒)，需要裁剪
2025-07-29 20:06:31,212 - INFO - 调整前总时长: 7.52秒, 目标时长: 7.09秒
2025-07-29 20:06:31,212 - INFO - 需要裁剪 0.43秒
2025-07-29 20:06:31,212 - INFO - 裁剪最长场景ID=2100：从1.96秒裁剪至1.53秒
2025-07-29 20:06:31,212 - INFO - 调整后总时长: 7.09秒，与目标时长差异: 0.00秒
2025-07-29 20:06:31,212 - INFO - 方案 #1 调整/填充后最终总时长: 7.09秒
2025-07-29 20:06:31,212 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:31,212 - INFO - ========== 当前模式：字幕 #52 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:31,212 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:31,212 - INFO - ========== 新模式：字幕 #52 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:31,212 - INFO - 
----- 处理字幕 #52 的方案 #1 -----
2025-07-29 20:06:31,212 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 20:06:31,212 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_xpm1af1
2025-07-29 20:06:31,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2101.mp4 (确认存在: True)
2025-07-29 20:06:31,214 - INFO - 添加场景ID=2101，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:06:31,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2102.mp4 (确认存在: True)
2025-07-29 20:06:31,214 - INFO - 添加场景ID=2102，时长=1.24秒，累计时长=2.96秒
2025-07-29 20:06:31,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2103.mp4 (确认存在: True)
2025-07-29 20:06:31,214 - INFO - 添加场景ID=2103，时长=2.72秒，累计时长=5.68秒
2025-07-29 20:06:31,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2104.mp4 (确认存在: True)
2025-07-29 20:06:31,214 - INFO - 添加场景ID=2104，时长=1.16秒，累计时长=6.84秒
2025-07-29 20:06:31,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2105.mp4 (确认存在: True)
2025-07-29 20:06:31,214 - INFO - 添加场景ID=2105，时长=1.32秒，累计时长=8.16秒
2025-07-29 20:06:31,214 - INFO - 准备合并 5 个场景文件，总时长约 8.16秒
2025-07-29 20:06:31,214 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2102.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2103.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2104.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2105.mp4'

2025-07-29 20:06:31,214 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_xpm1af1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_xpm1af1\temp_combined.mp4
2025-07-29 20:06:31,403 - INFO - 合并后的视频时长: 8.28秒，目标音频时长: 7.09秒
2025-07-29 20:06:31,403 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_xpm1af1\temp_combined.mp4 -ss 0 -to 7.088 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 20:06:31,808 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:31,808 - INFO - 目标音频时长: 7.09秒
2025-07-29 20:06:31,808 - INFO - 实际视频时长: 7.14秒
2025-07-29 20:06:31,808 - INFO - 时长差异: 0.05秒 (0.78%)
2025-07-29 20:06:31,808 - INFO - ==========================================
2025-07-29 20:06:31,808 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:31,808 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 20:06:31,809 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_xpm1af1
2025-07-29 20:06:31,853 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:31,853 - INFO -   - 音频时长: 7.09秒
2025-07-29 20:06:31,853 - INFO -   - 视频时长: 7.14秒
2025-07-29 20:06:31,853 - INFO -   - 时长差异: 0.05秒 (0.78%)
2025-07-29 20:06:31,853 - INFO - 
----- 处理字幕 #52 的方案 #2 -----
2025-07-29 20:06:31,853 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 20:06:31,853 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8nni3i7f
2025-07-29 20:06:31,854 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2106.mp4 (确认存在: True)
2025-07-29 20:06:31,854 - INFO - 添加场景ID=2106，时长=0.96秒，累计时长=0.96秒
2025-07-29 20:06:31,854 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2107.mp4 (确认存在: True)
2025-07-29 20:06:31,854 - INFO - 添加场景ID=2107，时长=3.32秒，累计时长=4.28秒
2025-07-29 20:06:31,854 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2108.mp4 (确认存在: True)
2025-07-29 20:06:31,854 - INFO - 添加场景ID=2108，时长=1.72秒，累计时长=6.00秒
2025-07-29 20:06:31,854 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2109.mp4 (确认存在: True)
2025-07-29 20:06:31,854 - INFO - 添加场景ID=2109，时长=1.80秒，累计时长=7.80秒
2025-07-29 20:06:31,854 - INFO - 准备合并 4 个场景文件，总时长约 7.80秒
2025-07-29 20:06:31,854 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2106.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2107.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2108.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2109.mp4'

2025-07-29 20:06:31,854 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8nni3i7f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8nni3i7f\temp_combined.mp4
2025-07-29 20:06:32,024 - INFO - 合并后的视频时长: 7.89秒，目标音频时长: 7.09秒
2025-07-29 20:06:32,024 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8nni3i7f\temp_combined.mp4 -ss 0 -to 7.088 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 20:06:32,399 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:32,399 - INFO - 目标音频时长: 7.09秒
2025-07-29 20:06:32,399 - INFO - 实际视频时长: 7.14秒
2025-07-29 20:06:32,399 - INFO - 时长差异: 0.05秒 (0.78%)
2025-07-29 20:06:32,399 - INFO - ==========================================
2025-07-29 20:06:32,399 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:32,399 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 20:06:32,399 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8nni3i7f
2025-07-29 20:06:32,444 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:32,445 - INFO -   - 音频时长: 7.09秒
2025-07-29 20:06:32,445 - INFO -   - 视频时长: 7.14秒
2025-07-29 20:06:32,445 - INFO -   - 时长差异: 0.05秒 (0.78%)
2025-07-29 20:06:32,445 - INFO - 
----- 处理字幕 #52 的方案 #3 -----
2025-07-29 20:06:32,445 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 20:06:32,445 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuheonzho
2025-07-29 20:06:32,445 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2101.mp4 (确认存在: True)
2025-07-29 20:06:32,446 - INFO - 添加场景ID=2101，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:06:32,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2105.mp4 (确认存在: True)
2025-07-29 20:06:32,446 - INFO - 添加场景ID=2105，时长=1.32秒，累计时长=3.04秒
2025-07-29 20:06:32,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2106.mp4 (确认存在: True)
2025-07-29 20:06:32,446 - INFO - 添加场景ID=2106，时长=0.96秒，累计时长=4.00秒
2025-07-29 20:06:32,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2100.mp4 (确认存在: True)
2025-07-29 20:06:32,446 - INFO - 添加场景ID=2100，时长=1.96秒，累计时长=5.96秒
2025-07-29 20:06:32,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2096.mp4 (确认存在: True)
2025-07-29 20:06:32,446 - INFO - 添加场景ID=2096，时长=1.56秒，累计时长=7.52秒
2025-07-29 20:06:32,446 - INFO - 准备合并 5 个场景文件，总时长约 7.52秒
2025-07-29 20:06:32,446 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2105.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2106.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2100.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2096.mp4'

2025-07-29 20:06:32,446 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuheonzho\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuheonzho\temp_combined.mp4
2025-07-29 20:06:32,634 - INFO - 合并后的视频时长: 7.64秒，目标音频时长: 7.09秒
2025-07-29 20:06:32,634 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuheonzho\temp_combined.mp4 -ss 0 -to 7.088 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 20:06:33,052 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:33,052 - INFO - 目标音频时长: 7.09秒
2025-07-29 20:06:33,052 - INFO - 实际视频时长: 7.14秒
2025-07-29 20:06:33,052 - INFO - 时长差异: 0.05秒 (0.78%)
2025-07-29 20:06:33,053 - INFO - ==========================================
2025-07-29 20:06:33,053 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:33,053 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 20:06:33,053 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuheonzho
2025-07-29 20:06:33,098 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:33,098 - INFO -   - 音频时长: 7.09秒
2025-07-29 20:06:33,098 - INFO -   - 视频时长: 7.14秒
2025-07-29 20:06:33,098 - INFO -   - 时长差异: 0.05秒 (0.78%)
2025-07-29 20:06:33,098 - INFO - 
字幕 #52 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:33,098 - INFO - 生成的视频文件:
2025-07-29 20:06:33,098 - INFO -   1. F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 20:06:33,098 - INFO -   2. F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 20:06:33,098 - INFO -   3. F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 20:06:33,098 - INFO - ========== 字幕 #52 处理结束 ==========

