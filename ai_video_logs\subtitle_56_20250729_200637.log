2025-07-29 20:06:37,432 - INFO - ========== 字幕 #56 处理开始 ==========
2025-07-29 20:06:37,432 - INFO - 字幕内容: 女人百口莫辩，声称计划书七年前就已完成，绝无抄袭，却无人相信。
2025-07-29 20:06:37,432 - INFO - 字幕序号: [2283, 2286]
2025-07-29 20:06:37,432 - INFO - 音频文件详情:
2025-07-29 20:06:37,432 - INFO -   - 路径: output\56.wav
2025-07-29 20:06:37,432 - INFO -   - 时长: 5.06秒
2025-07-29 20:06:37,433 - INFO -   - 验证音频时长: 5.06秒
2025-07-29 20:06:37,433 - INFO - 字幕时间戳信息:
2025-07-29 20:06:37,433 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:37,433 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:37,433 - INFO -   - 根据生成的音频时长(5.06秒)已调整字幕时间戳
2025-07-29 20:06:37,433 - INFO - ========== 新模式：为字幕 #56 生成4套场景方案 ==========
2025-07-29 20:06:37,433 - INFO - 字幕序号列表: [2283, 2286]
2025-07-29 20:06:37,433 - INFO - 
--- 生成方案 #1：基于字幕序号 #2283 ---
2025-07-29 20:06:37,433 - INFO - 开始为单个字幕序号 #2283 匹配场景，目标时长: 5.06秒
2025-07-29 20:06:37,433 - INFO - 开始查找字幕序号 [2283] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:37,434 - INFO - 找到related_overlap场景: scene_id=2219, 字幕#2283
2025-07-29 20:06:37,435 - INFO - 字幕 #2283 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:37,435 - INFO - 字幕序号 #2283 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:37,435 - INFO - 选择第一个overlap场景作为起点: scene_id=2219
2025-07-29 20:06:37,435 - INFO - 添加起点场景: scene_id=2219, 时长=2.04秒, 累计时长=2.04秒
2025-07-29 20:06:37,435 - INFO - 起点场景时长不足，需要延伸填充 3.02秒
2025-07-29 20:06:37,435 - INFO - 起点场景在原始列表中的索引: 2218
2025-07-29 20:06:37,435 - INFO - 延伸添加场景: scene_id=2220 (完整时长 1.24秒)
2025-07-29 20:06:37,435 - INFO - 累计时长: 3.28秒
2025-07-29 20:06:37,435 - INFO - 延伸添加场景: scene_id=2221 (完整时长 1.20秒)
2025-07-29 20:06:37,435 - INFO - 累计时长: 4.48秒
2025-07-29 20:06:37,435 - INFO - 延伸添加场景: scene_id=2222 (裁剪至 0.58秒)
2025-07-29 20:06:37,435 - INFO - 累计时长: 5.06秒
2025-07-29 20:06:37,435 - INFO - 字幕序号 #2283 场景匹配完成，共选择 4 个场景，总时长: 5.06秒
2025-07-29 20:06:37,435 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:37,435 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:37,435 - INFO - 
--- 生成方案 #2：基于字幕序号 #2286 ---
2025-07-29 20:06:37,435 - INFO - 开始为单个字幕序号 #2286 匹配场景，目标时长: 5.06秒
2025-07-29 20:06:37,435 - INFO - 开始查找字幕序号 [2286] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:37,435 - INFO - 找到related_overlap场景: scene_id=2222, 字幕#2286
2025-07-29 20:06:37,436 - INFO - 字幕 #2286 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:37,436 - INFO - 字幕序号 #2286 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:37,436 - ERROR - 字幕序号 #2286 没有找到任何可用的匹配场景
2025-07-29 20:06:37,436 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:37,436 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:37,436 - INFO - ========== 当前模式：为字幕 #56 生成 1 套场景方案 ==========
2025-07-29 20:06:37,436 - INFO - 开始查找字幕序号 [2283, 2286] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:37,436 - INFO - 找到related_overlap场景: scene_id=2219, 字幕#2283
2025-07-29 20:06:37,436 - INFO - 找到related_overlap场景: scene_id=2222, 字幕#2286
2025-07-29 20:06:37,438 - INFO - 字幕 #2283 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:37,438 - INFO - 字幕 #2286 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:37,438 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:37,438 - INFO - 开始生成方案 #1
2025-07-29 20:06:37,438 - INFO - 方案 #1: 为字幕#2283选择初始化overlap场景id=2219
2025-07-29 20:06:37,438 - INFO - 方案 #1: 为字幕#2286选择初始化overlap场景id=2222
2025-07-29 20:06:37,438 - INFO - 方案 #1: 初始选择后，当前总时长=3.52秒
2025-07-29 20:06:37,438 - INFO - 方案 #1: 额外between选择后，当前总时长=3.52秒
2025-07-29 20:06:37,438 - INFO - 方案 #1: 场景总时长(3.52秒)小于音频时长(5.06秒)，需要延伸填充
2025-07-29 20:06:37,438 - INFO - 方案 #1: 最后一个场景ID: 2222
2025-07-29 20:06:37,438 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2221
2025-07-29 20:06:37,438 - INFO - 方案 #1: 需要填充时长: 1.54秒
2025-07-29 20:06:37,438 - INFO - 方案 #1: 追加场景 scene_id=2223 (完整时长 0.96秒)
2025-07-29 20:06:37,438 - INFO - 方案 #1: 追加场景 scene_id=2224 (裁剪至 0.58秒)
2025-07-29 20:06:37,438 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:37,438 - INFO - 方案 #1 调整/填充后最终总时长: 5.06秒
2025-07-29 20:06:37,438 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:37,438 - INFO - ========== 当前模式：字幕 #56 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:37,438 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:37,438 - INFO - ========== 新模式：字幕 #56 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:37,438 - INFO - 
----- 处理字幕 #56 的方案 #1 -----
2025-07-29 20:06:37,438 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 20:06:37,438 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp17t5c09e
2025-07-29 20:06:37,439 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2219.mp4 (确认存在: True)
2025-07-29 20:06:37,439 - INFO - 添加场景ID=2219，时长=2.04秒，累计时长=2.04秒
2025-07-29 20:06:37,439 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2220.mp4 (确认存在: True)
2025-07-29 20:06:37,439 - INFO - 添加场景ID=2220，时长=1.24秒，累计时长=3.28秒
2025-07-29 20:06:37,439 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2221.mp4 (确认存在: True)
2025-07-29 20:06:37,439 - INFO - 添加场景ID=2221，时长=1.20秒，累计时长=4.48秒
2025-07-29 20:06:37,439 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2222.mp4 (确认存在: True)
2025-07-29 20:06:37,439 - INFO - 添加场景ID=2222，时长=1.48秒，累计时长=5.96秒
2025-07-29 20:06:37,439 - INFO - 准备合并 4 个场景文件，总时长约 5.96秒
2025-07-29 20:06:37,439 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2219.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2220.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2221.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2222.mp4'

2025-07-29 20:06:37,439 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp17t5c09e\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp17t5c09e\temp_combined.mp4
2025-07-29 20:06:37,603 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 5.06秒
2025-07-29 20:06:37,603 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp17t5c09e\temp_combined.mp4 -ss 0 -to 5.057 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 20:06:37,933 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:37,933 - INFO - 目标音频时长: 5.06秒
2025-07-29 20:06:37,933 - INFO - 实际视频时长: 5.10秒
2025-07-29 20:06:37,933 - INFO - 时长差异: 0.05秒 (0.91%)
2025-07-29 20:06:37,933 - INFO - ==========================================
2025-07-29 20:06:37,933 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:37,933 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 20:06:37,934 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp17t5c09e
2025-07-29 20:06:37,978 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:37,978 - INFO -   - 音频时长: 5.06秒
2025-07-29 20:06:37,978 - INFO -   - 视频时长: 5.10秒
2025-07-29 20:06:37,978 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-07-29 20:06:37,978 - INFO - 
----- 处理字幕 #56 的方案 #2 -----
2025-07-29 20:06:37,978 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 20:06:37,978 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3apd_qdy
2025-07-29 20:06:37,979 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2219.mp4 (确认存在: True)
2025-07-29 20:06:37,979 - INFO - 添加场景ID=2219，时长=2.04秒，累计时长=2.04秒
2025-07-29 20:06:37,979 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2222.mp4 (确认存在: True)
2025-07-29 20:06:37,979 - INFO - 添加场景ID=2222，时长=1.48秒，累计时长=3.52秒
2025-07-29 20:06:37,979 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2223.mp4 (确认存在: True)
2025-07-29 20:06:37,979 - INFO - 添加场景ID=2223，时长=0.96秒，累计时长=4.48秒
2025-07-29 20:06:37,979 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2224.mp4 (确认存在: True)
2025-07-29 20:06:37,979 - INFO - 添加场景ID=2224，时长=1.36秒，累计时长=5.84秒
2025-07-29 20:06:37,980 - INFO - 准备合并 4 个场景文件，总时长约 5.84秒
2025-07-29 20:06:37,980 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2219.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2222.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2223.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2224.mp4'

2025-07-29 20:06:37,981 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3apd_qdy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3apd_qdy\temp_combined.mp4
2025-07-29 20:06:38,166 - INFO - 合并后的视频时长: 5.93秒，目标音频时长: 5.06秒
2025-07-29 20:06:38,166 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3apd_qdy\temp_combined.mp4 -ss 0 -to 5.057 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 20:06:38,505 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:38,505 - INFO - 目标音频时长: 5.06秒
2025-07-29 20:06:38,505 - INFO - 实际视频时长: 5.10秒
2025-07-29 20:06:38,505 - INFO - 时长差异: 0.05秒 (0.91%)
2025-07-29 20:06:38,505 - INFO - ==========================================
2025-07-29 20:06:38,505 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:38,505 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 20:06:38,506 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3apd_qdy
2025-07-29 20:06:38,552 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:38,552 - INFO -   - 音频时长: 5.06秒
2025-07-29 20:06:38,552 - INFO -   - 视频时长: 5.10秒
2025-07-29 20:06:38,552 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-07-29 20:06:38,552 - INFO - 
字幕 #56 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:38,552 - INFO - 生成的视频文件:
2025-07-29 20:06:38,552 - INFO -   1. F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 20:06:38,553 - INFO -   2. F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 20:06:38,553 - INFO - ========== 字幕 #56 处理结束 ==========

