2025-07-29 20:05:18,187 - INFO - ========== 字幕 #2 处理开始 ==========
2025-07-29 20:05:18,187 - INFO - 字幕内容: 她曾向病重的妹妹承诺，等心脏病好了，就一起环游世界，享受人生。
2025-07-29 20:05:18,187 - INFO - 字幕序号: [6, 10]
2025-07-29 20:05:18,187 - INFO - 音频文件详情:
2025-07-29 20:05:18,187 - INFO -   - 路径: output\2.wav
2025-07-29 20:05:18,187 - INFO -   - 时长: 4.22秒
2025-07-29 20:05:18,188 - INFO -   - 验证音频时长: 4.22秒
2025-07-29 20:05:18,188 - INFO - 字幕时间戳信息:
2025-07-29 20:05:18,188 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:18,188 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:18,188 - INFO -   - 根据生成的音频时长(4.22秒)已调整字幕时间戳
2025-07-29 20:05:18,188 - INFO - ========== 新模式：为字幕 #2 生成4套场景方案 ==========
2025-07-29 20:05:18,188 - INFO - 字幕序号列表: [6, 10]
2025-07-29 20:05:18,188 - INFO - 
--- 生成方案 #1：基于字幕序号 #6 ---
2025-07-29 20:05:18,188 - INFO - 开始为单个字幕序号 #6 匹配场景，目标时长: 4.22秒
2025-07-29 20:05:18,188 - INFO - 开始查找字幕序号 [6] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:18,188 - INFO - 找到related_overlap场景: scene_id=6, 字幕#6
2025-07-29 20:05:18,189 - INFO - 字幕 #6 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:18,189 - INFO - 字幕序号 #6 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:18,189 - INFO - 选择第一个overlap场景作为起点: scene_id=6
2025-07-29 20:05:18,189 - INFO - 添加起点场景: scene_id=6, 时长=5.60秒, 累计时长=5.60秒
2025-07-29 20:05:18,189 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:05:18,189 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 20:05:18,189 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 20:05:18,189 - INFO - 
--- 生成方案 #2：基于字幕序号 #10 ---
2025-07-29 20:05:18,189 - INFO - 开始为单个字幕序号 #10 匹配场景，目标时长: 4.22秒
2025-07-29 20:05:18,189 - INFO - 开始查找字幕序号 [10] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:18,189 - INFO - 找到related_overlap场景: scene_id=7, 字幕#10
2025-07-29 20:05:18,190 - INFO - 字幕 #10 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:18,190 - INFO - 字幕序号 #10 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:18,190 - INFO - 选择第一个overlap场景作为起点: scene_id=7
2025-07-29 20:05:18,190 - INFO - 添加起点场景: scene_id=7, 时长=0.88秒, 累计时长=0.88秒
2025-07-29 20:05:18,190 - INFO - 起点场景时长不足，需要延伸填充 3.35秒
2025-07-29 20:05:18,190 - INFO - 起点场景在原始列表中的索引: 6
2025-07-29 20:05:18,190 - INFO - 延伸添加场景: scene_id=8 (完整时长 0.80秒)
2025-07-29 20:05:18,190 - INFO - 累计时长: 1.68秒
2025-07-29 20:05:18,190 - INFO - 延伸添加场景: scene_id=9 (完整时长 1.76秒)
2025-07-29 20:05:18,190 - INFO - 累计时长: 3.44秒
2025-07-29 20:05:18,190 - INFO - 延伸添加场景: scene_id=10 (裁剪至 0.79秒)
2025-07-29 20:05:18,190 - INFO - 累计时长: 4.22秒
2025-07-29 20:05:18,190 - INFO - 字幕序号 #10 场景匹配完成，共选择 4 个场景，总时长: 4.22秒
2025-07-29 20:05:18,190 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:05:18,190 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:18,190 - INFO - ========== 当前模式：为字幕 #2 生成 1 套场景方案 ==========
2025-07-29 20:05:18,190 - INFO - 开始查找字幕序号 [6, 10] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:18,190 - INFO - 找到related_overlap场景: scene_id=6, 字幕#6
2025-07-29 20:05:18,190 - INFO - 找到related_overlap场景: scene_id=7, 字幕#10
2025-07-29 20:05:18,192 - INFO - 字幕 #6 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:18,192 - INFO - 字幕 #10 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:18,192 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:18,192 - INFO - 开始生成方案 #1
2025-07-29 20:05:18,192 - INFO - 方案 #1: 为字幕#6选择初始化overlap场景id=6
2025-07-29 20:05:18,192 - INFO - 方案 #1: 为字幕#10选择初始化overlap场景id=7
2025-07-29 20:05:18,192 - INFO - 方案 #1: 初始选择后，当前总时长=6.48秒
2025-07-29 20:05:18,192 - INFO - 方案 #1: 额外between选择后，当前总时长=6.48秒
2025-07-29 20:05:18,192 - INFO - 方案 #1: 场景总时长(6.48秒)大于音频时长(4.22秒)，需要裁剪
2025-07-29 20:05:18,192 - INFO - 调整前总时长: 6.48秒, 目标时长: 4.22秒
2025-07-29 20:05:18,192 - INFO - 需要裁剪 2.25秒
2025-07-29 20:05:18,192 - INFO - 裁剪最长场景ID=6：从5.60秒裁剪至3.35秒
2025-07-29 20:05:18,192 - INFO - 调整后总时长: 4.22秒，与目标时长差异: 0.00秒
2025-07-29 20:05:18,192 - INFO - 方案 #1 调整/填充后最终总时长: 4.22秒
2025-07-29 20:05:18,192 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:18,192 - INFO - ========== 当前模式：字幕 #2 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:18,192 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:18,192 - INFO - ========== 新模式：字幕 #2 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:18,192 - INFO - 
----- 处理字幕 #2 的方案 #1 -----
2025-07-29 20:05:18,192 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 20:05:18,202 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfu5szvp1
2025-07-29 20:05:18,202 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\6.mp4 (确认存在: True)
2025-07-29 20:05:18,203 - INFO - 添加场景ID=6，时长=5.60秒，累计时长=5.60秒
2025-07-29 20:05:18,203 - INFO - 准备合并 1 个场景文件，总时长约 5.60秒
2025-07-29 20:05:18,203 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/6.mp4'

2025-07-29 20:05:18,203 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfu5szvp1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfu5szvp1\temp_combined.mp4
2025-07-29 20:05:18,307 - INFO - 合并后的视频时长: 5.62秒，目标音频时长: 4.22秒
2025-07-29 20:05:18,307 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfu5szvp1\temp_combined.mp4 -ss 0 -to 4.224 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 20:05:18,566 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:18,566 - INFO - 目标音频时长: 4.22秒
2025-07-29 20:05:18,566 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:18,566 - INFO - 时长差异: 0.04秒 (0.92%)
2025-07-29 20:05:18,566 - INFO - ==========================================
2025-07-29 20:05:18,566 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:18,566 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 20:05:18,567 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfu5szvp1
2025-07-29 20:05:18,610 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:18,610 - INFO -   - 音频时长: 4.22秒
2025-07-29 20:05:18,610 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:18,610 - INFO -   - 时长差异: 0.04秒 (0.92%)
2025-07-29 20:05:18,610 - INFO - 
----- 处理字幕 #2 的方案 #2 -----
2025-07-29 20:05:18,610 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 20:05:18,610 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcdz1cuug
2025-07-29 20:05:18,612 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\7.mp4 (确认存在: True)
2025-07-29 20:05:18,612 - INFO - 添加场景ID=7，时长=0.88秒，累计时长=0.88秒
2025-07-29 20:05:18,612 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\8.mp4 (确认存在: True)
2025-07-29 20:05:18,612 - INFO - 添加场景ID=8，时长=0.80秒，累计时长=1.68秒
2025-07-29 20:05:18,612 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\9.mp4 (确认存在: True)
2025-07-29 20:05:18,612 - INFO - 添加场景ID=9，时长=1.76秒，累计时长=3.44秒
2025-07-29 20:05:18,612 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\10.mp4 (确认存在: True)
2025-07-29 20:05:18,612 - INFO - 添加场景ID=10，时长=2.40秒，累计时长=5.84秒
2025-07-29 20:05:18,612 - INFO - 准备合并 4 个场景文件，总时长约 5.84秒
2025-07-29 20:05:18,612 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/7.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/8.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/9.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/10.mp4'

2025-07-29 20:05:18,612 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcdz1cuug\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcdz1cuug\temp_combined.mp4
2025-07-29 20:05:18,769 - INFO - 合并后的视频时长: 5.93秒，目标音频时长: 4.22秒
2025-07-29 20:05:18,769 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcdz1cuug\temp_combined.mp4 -ss 0 -to 4.224 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 20:05:19,058 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:19,058 - INFO - 目标音频时长: 4.22秒
2025-07-29 20:05:19,058 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:19,058 - INFO - 时长差异: 0.04秒 (0.92%)
2025-07-29 20:05:19,058 - INFO - ==========================================
2025-07-29 20:05:19,058 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:19,058 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 20:05:19,059 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcdz1cuug
2025-07-29 20:05:19,108 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:19,108 - INFO -   - 音频时长: 4.22秒
2025-07-29 20:05:19,108 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:19,108 - INFO -   - 时长差异: 0.04秒 (0.92%)
2025-07-29 20:05:19,108 - INFO - 
----- 处理字幕 #2 的方案 #3 -----
2025-07-29 20:05:19,108 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 20:05:19,108 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiqvjauu0
2025-07-29 20:05:19,109 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\6.mp4 (确认存在: True)
2025-07-29 20:05:19,109 - INFO - 添加场景ID=6，时长=5.60秒，累计时长=5.60秒
2025-07-29 20:05:19,109 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\7.mp4 (确认存在: True)
2025-07-29 20:05:19,109 - INFO - 添加场景ID=7，时长=0.88秒，累计时长=6.48秒
2025-07-29 20:05:19,109 - INFO - 场景总时长(6.48秒)已达到音频时长(4.22秒)的1.5倍，停止添加场景
2025-07-29 20:05:19,109 - INFO - 准备合并 2 个场景文件，总时长约 6.48秒
2025-07-29 20:05:19,109 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/6.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/7.mp4'

2025-07-29 20:05:19,109 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiqvjauu0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiqvjauu0\temp_combined.mp4
2025-07-29 20:05:19,229 - INFO - 合并后的视频时长: 6.53秒，目标音频时长: 4.22秒
2025-07-29 20:05:19,229 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiqvjauu0\temp_combined.mp4 -ss 0 -to 4.224 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 20:05:19,508 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:19,508 - INFO - 目标音频时长: 4.22秒
2025-07-29 20:05:19,508 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:19,508 - INFO - 时长差异: 0.04秒 (0.92%)
2025-07-29 20:05:19,508 - INFO - ==========================================
2025-07-29 20:05:19,508 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:19,508 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 20:05:19,508 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiqvjauu0
2025-07-29 20:05:19,551 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:19,551 - INFO -   - 音频时长: 4.22秒
2025-07-29 20:05:19,551 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:19,551 - INFO -   - 时长差异: 0.04秒 (0.92%)
2025-07-29 20:05:19,551 - INFO - 
字幕 #2 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:19,551 - INFO - 生成的视频文件:
2025-07-29 20:05:19,551 - INFO -   1. F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 20:05:19,551 - INFO -   2. F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 20:05:19,551 - INFO -   3. F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 20:05:19,551 - INFO - ========== 字幕 #2 处理结束 ==========

