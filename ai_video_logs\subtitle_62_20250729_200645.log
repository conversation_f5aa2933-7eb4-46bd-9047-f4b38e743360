2025-07-29 20:06:45,131 - INFO - ========== 字幕 #62 处理开始 ==========
2025-07-29 20:06:45,131 - INFO - 字幕内容: 女人内心天人交战，一边是家族的存亡，一边是心爱之人的清白，她最终还是选择了沉默。
2025-07-29 20:06:45,131 - INFO - 字幕序号: [2450, 2452]
2025-07-29 20:06:45,131 - INFO - 音频文件详情:
2025-07-29 20:06:45,131 - INFO -   - 路径: output\62.wav
2025-07-29 20:06:45,131 - INFO -   - 时长: 5.02秒
2025-07-29 20:06:45,131 - INFO -   - 验证音频时长: 5.02秒
2025-07-29 20:06:45,131 - INFO - 字幕时间戳信息:
2025-07-29 20:06:45,132 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:45,132 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:45,132 - INFO -   - 根据生成的音频时长(5.02秒)已调整字幕时间戳
2025-07-29 20:06:45,132 - INFO - ========== 新模式：为字幕 #62 生成4套场景方案 ==========
2025-07-29 20:06:45,132 - INFO - 字幕序号列表: [2450, 2452]
2025-07-29 20:06:45,132 - INFO - 
--- 生成方案 #1：基于字幕序号 #2450 ---
2025-07-29 20:06:45,132 - INFO - 开始为单个字幕序号 #2450 匹配场景，目标时长: 5.02秒
2025-07-29 20:06:45,132 - INFO - 开始查找字幕序号 [2450] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:45,132 - INFO - 找到related_overlap场景: scene_id=2338, 字幕#2450
2025-07-29 20:06:45,133 - INFO - 字幕 #2450 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:45,133 - INFO - 字幕序号 #2450 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:45,133 - INFO - 选择第一个overlap场景作为起点: scene_id=2338
2025-07-29 20:06:45,133 - INFO - 添加起点场景: scene_id=2338, 时长=4.52秒, 累计时长=4.52秒
2025-07-29 20:06:45,133 - INFO - 起点场景时长不足，需要延伸填充 0.50秒
2025-07-29 20:06:45,133 - INFO - 起点场景在原始列表中的索引: 2337
2025-07-29 20:06:45,133 - INFO - 延伸添加场景: scene_id=2339 (裁剪至 0.50秒)
2025-07-29 20:06:45,133 - INFO - 累计时长: 5.02秒
2025-07-29 20:06:45,133 - INFO - 字幕序号 #2450 场景匹配完成，共选择 2 个场景，总时长: 5.02秒
2025-07-29 20:06:45,133 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:06:45,134 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:06:45,134 - INFO - 
--- 生成方案 #2：基于字幕序号 #2452 ---
2025-07-29 20:06:45,134 - INFO - 开始为单个字幕序号 #2452 匹配场景，目标时长: 5.02秒
2025-07-29 20:06:45,134 - INFO - 开始查找字幕序号 [2452] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:45,134 - INFO - 找到related_overlap场景: scene_id=2339, 字幕#2452
2025-07-29 20:06:45,135 - INFO - 字幕 #2452 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:45,135 - INFO - 字幕序号 #2452 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:45,135 - ERROR - 字幕序号 #2452 没有找到任何可用的匹配场景
2025-07-29 20:06:45,135 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:45,135 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:45,135 - INFO - ========== 当前模式：为字幕 #62 生成 1 套场景方案 ==========
2025-07-29 20:06:45,135 - INFO - 开始查找字幕序号 [2450, 2452] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:45,135 - INFO - 找到related_overlap场景: scene_id=2338, 字幕#2450
2025-07-29 20:06:45,135 - INFO - 找到related_overlap场景: scene_id=2339, 字幕#2452
2025-07-29 20:06:45,136 - INFO - 字幕 #2450 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:45,136 - INFO - 字幕 #2452 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:45,136 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:45,136 - INFO - 开始生成方案 #1
2025-07-29 20:06:45,136 - INFO - 方案 #1: 为字幕#2450选择初始化overlap场景id=2338
2025-07-29 20:06:45,136 - INFO - 方案 #1: 为字幕#2452选择初始化overlap场景id=2339
2025-07-29 20:06:45,136 - INFO - 方案 #1: 初始选择后，当前总时长=7.48秒
2025-07-29 20:06:45,136 - INFO - 方案 #1: 额外between选择后，当前总时长=7.48秒
2025-07-29 20:06:45,136 - INFO - 方案 #1: 场景总时长(7.48秒)大于音频时长(5.02秒)，需要裁剪
2025-07-29 20:06:45,136 - INFO - 调整前总时长: 7.48秒, 目标时长: 5.02秒
2025-07-29 20:06:45,136 - INFO - 需要裁剪 2.46秒
2025-07-29 20:06:45,136 - INFO - 裁剪最长场景ID=2338：从4.52秒裁剪至2.06秒
2025-07-29 20:06:45,136 - INFO - 调整后总时长: 5.02秒，与目标时长差异: 0.00秒
2025-07-29 20:06:45,136 - INFO - 方案 #1 调整/填充后最终总时长: 5.02秒
2025-07-29 20:06:45,136 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:45,136 - INFO - ========== 当前模式：字幕 #62 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:45,136 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:45,136 - INFO - ========== 新模式：字幕 #62 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:45,137 - INFO - 
----- 处理字幕 #62 的方案 #1 -----
2025-07-29 20:06:45,137 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 20:06:45,137 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbq2bwq5i
2025-07-29 20:06:45,137 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2338.mp4 (确认存在: True)
2025-07-29 20:06:45,137 - INFO - 添加场景ID=2338，时长=4.52秒，累计时长=4.52秒
2025-07-29 20:06:45,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2339.mp4 (确认存在: True)
2025-07-29 20:06:45,138 - INFO - 添加场景ID=2339，时长=2.96秒，累计时长=7.48秒
2025-07-29 20:06:45,138 - INFO - 准备合并 2 个场景文件，总时长约 7.48秒
2025-07-29 20:06:45,138 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2338.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2339.mp4'

2025-07-29 20:06:45,138 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbq2bwq5i\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbq2bwq5i\temp_combined.mp4
2025-07-29 20:06:45,261 - INFO - 合并后的视频时长: 7.53秒，目标音频时长: 5.02秒
2025-07-29 20:06:45,262 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbq2bwq5i\temp_combined.mp4 -ss 0 -to 5.024 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 20:06:45,558 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:45,558 - INFO - 目标音频时长: 5.02秒
2025-07-29 20:06:45,558 - INFO - 实际视频时长: 5.06秒
2025-07-29 20:06:45,558 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:45,558 - INFO - ==========================================
2025-07-29 20:06:45,558 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:45,558 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 20:06:45,559 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbq2bwq5i
2025-07-29 20:06:45,602 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:45,602 - INFO -   - 音频时长: 5.02秒
2025-07-29 20:06:45,602 - INFO -   - 视频时长: 5.06秒
2025-07-29 20:06:45,602 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:45,602 - INFO - 
----- 处理字幕 #62 的方案 #2 -----
2025-07-29 20:06:45,602 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 20:06:45,603 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7sl1ddhc
2025-07-29 20:06:45,603 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2338.mp4 (确认存在: True)
2025-07-29 20:06:45,603 - INFO - 添加场景ID=2338，时长=4.52秒，累计时长=4.52秒
2025-07-29 20:06:45,603 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2339.mp4 (确认存在: True)
2025-07-29 20:06:45,603 - INFO - 添加场景ID=2339，时长=2.96秒，累计时长=7.48秒
2025-07-29 20:06:45,603 - INFO - 准备合并 2 个场景文件，总时长约 7.48秒
2025-07-29 20:06:45,603 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2338.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2339.mp4'

2025-07-29 20:06:45,604 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7sl1ddhc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7sl1ddhc\temp_combined.mp4
2025-07-29 20:06:45,729 - INFO - 合并后的视频时长: 7.53秒，目标音频时长: 5.02秒
2025-07-29 20:06:45,729 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7sl1ddhc\temp_combined.mp4 -ss 0 -to 5.024 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 20:06:46,043 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:46,043 - INFO - 目标音频时长: 5.02秒
2025-07-29 20:06:46,043 - INFO - 实际视频时长: 5.06秒
2025-07-29 20:06:46,043 - INFO - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:46,043 - INFO - ==========================================
2025-07-29 20:06:46,043 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:46,043 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 20:06:46,044 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7sl1ddhc
2025-07-29 20:06:46,089 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:46,089 - INFO -   - 音频时长: 5.02秒
2025-07-29 20:06:46,089 - INFO -   - 视频时长: 5.06秒
2025-07-29 20:06:46,089 - INFO -   - 时长差异: 0.04秒 (0.78%)
2025-07-29 20:06:46,089 - INFO - 
字幕 #62 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:46,089 - INFO - 生成的视频文件:
2025-07-29 20:06:46,089 - INFO -   1. F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 20:06:46,089 - INFO -   2. F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 20:06:46,089 - INFO - ========== 字幕 #62 处理结束 ==========

