2025-07-29 20:06:13,627 - INFO - ========== 字幕 #40 处理开始 ==========
2025-07-29 20:06:13,627 - INFO - 字幕内容: 心急如焚的女人赶回公司，发现男人已经寄来了辞职信，根本不等她批准。
2025-07-29 20:06:13,627 - INFO - 字幕序号: [666, 670]
2025-07-29 20:06:13,627 - INFO - 音频文件详情:
2025-07-29 20:06:13,627 - INFO -   - 路径: output\40.wav
2025-07-29 20:06:13,627 - INFO -   - 时长: 5.74秒
2025-07-29 20:06:13,627 - INFO -   - 验证音频时长: 5.74秒
2025-07-29 20:06:13,627 - INFO - 字幕时间戳信息:
2025-07-29 20:06:13,627 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:13,627 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:13,627 - INFO -   - 根据生成的音频时长(5.74秒)已调整字幕时间戳
2025-07-29 20:06:13,627 - INFO - ========== 新模式：为字幕 #40 生成4套场景方案 ==========
2025-07-29 20:06:13,627 - INFO - 字幕序号列表: [666, 670]
2025-07-29 20:06:13,627 - INFO - 
--- 生成方案 #1：基于字幕序号 #666 ---
2025-07-29 20:06:13,627 - INFO - 开始为单个字幕序号 #666 匹配场景，目标时长: 5.74秒
2025-07-29 20:06:13,627 - INFO - 开始查找字幕序号 [666] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:13,629 - INFO - 找到related_overlap场景: scene_id=698, 字幕#666
2025-07-29 20:06:13,629 - INFO - 找到related_overlap场景: scene_id=699, 字幕#666
2025-07-29 20:06:13,630 - INFO - 字幕 #666 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:13,630 - INFO - 字幕序号 #666 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:13,630 - INFO - 选择第一个overlap场景作为起点: scene_id=698
2025-07-29 20:06:13,630 - INFO - 添加起点场景: scene_id=698, 时长=1.36秒, 累计时长=1.36秒
2025-07-29 20:06:13,630 - INFO - 起点场景时长不足，需要延伸填充 4.38秒
2025-07-29 20:06:13,630 - INFO - 起点场景在原始列表中的索引: 697
2025-07-29 20:06:13,630 - INFO - 延伸添加场景: scene_id=699 (完整时长 0.88秒)
2025-07-29 20:06:13,630 - INFO - 累计时长: 2.24秒
2025-07-29 20:06:13,630 - INFO - 延伸添加场景: scene_id=700 (完整时长 0.68秒)
2025-07-29 20:06:13,630 - INFO - 累计时长: 2.92秒
2025-07-29 20:06:13,630 - INFO - 延伸添加场景: scene_id=701 (完整时长 0.76秒)
2025-07-29 20:06:13,630 - INFO - 累计时长: 3.68秒
2025-07-29 20:06:13,630 - INFO - 延伸添加场景: scene_id=702 (完整时长 1.00秒)
2025-07-29 20:06:13,630 - INFO - 累计时长: 4.68秒
2025-07-29 20:06:13,630 - INFO - 延伸添加场景: scene_id=703 (完整时长 0.84秒)
2025-07-29 20:06:13,630 - INFO - 累计时长: 5.52秒
2025-07-29 20:06:13,630 - INFO - 延伸添加场景: scene_id=704 (裁剪至 0.23秒)
2025-07-29 20:06:13,630 - INFO - 累计时长: 5.74秒
2025-07-29 20:06:13,630 - INFO - 字幕序号 #666 场景匹配完成，共选择 7 个场景，总时长: 5.74秒
2025-07-29 20:06:13,630 - INFO - 方案 #1 生成成功，包含 7 个场景
2025-07-29 20:06:13,630 - INFO - 新模式：第1套方案的 7 个场景已加入全局已使用集合
2025-07-29 20:06:13,630 - INFO - 
--- 生成方案 #2：基于字幕序号 #670 ---
2025-07-29 20:06:13,630 - INFO - 开始为单个字幕序号 #670 匹配场景，目标时长: 5.74秒
2025-07-29 20:06:13,630 - INFO - 开始查找字幕序号 [670] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:13,630 - INFO - 找到related_overlap场景: scene_id=708, 字幕#670
2025-07-29 20:06:13,630 - INFO - 找到related_overlap场景: scene_id=709, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=704, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=705, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=706, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=707, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=710, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=711, 字幕#670
2025-07-29 20:06:13,631 - INFO - 找到related_between场景: scene_id=712, 字幕#670
2025-07-29 20:06:13,631 - INFO - 字幕 #670 找到 2 个overlap场景, 7 个between场景
2025-07-29 20:06:13,631 - INFO - 字幕序号 #670 找到 2 个可用overlap场景, 6 个可用between场景
2025-07-29 20:06:13,631 - INFO - 选择第一个overlap场景作为起点: scene_id=708
2025-07-29 20:06:13,631 - INFO - 添加起点场景: scene_id=708, 时长=1.48秒, 累计时长=1.48秒
2025-07-29 20:06:13,632 - INFO - 起点场景时长不足，需要延伸填充 4.26秒
2025-07-29 20:06:13,632 - INFO - 起点场景在原始列表中的索引: 707
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=709 (完整时长 0.84秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 2.32秒
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=710 (完整时长 0.72秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 3.04秒
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=711 (完整时长 0.72秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 3.76秒
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=712 (完整时长 0.68秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 4.44秒
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=713 (完整时长 0.64秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 5.08秒
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=714 (完整时长 0.60秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 5.68秒
2025-07-29 20:06:13,632 - INFO - 延伸添加场景: scene_id=715 (裁剪至 0.07秒)
2025-07-29 20:06:13,632 - INFO - 累计时长: 5.74秒
2025-07-29 20:06:13,632 - INFO - 字幕序号 #670 场景匹配完成，共选择 8 个场景，总时长: 5.74秒
2025-07-29 20:06:13,632 - INFO - 方案 #2 生成成功，包含 8 个场景
2025-07-29 20:06:13,632 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:13,632 - INFO - ========== 当前模式：为字幕 #40 生成 1 套场景方案 ==========
2025-07-29 20:06:13,632 - INFO - 开始查找字幕序号 [666, 670] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:13,632 - INFO - 找到related_overlap场景: scene_id=698, 字幕#666
2025-07-29 20:06:13,632 - INFO - 找到related_overlap场景: scene_id=699, 字幕#666
2025-07-29 20:06:13,632 - INFO - 找到related_overlap场景: scene_id=708, 字幕#670
2025-07-29 20:06:13,632 - INFO - 找到related_overlap场景: scene_id=709, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=704, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=705, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=706, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=707, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=710, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=711, 字幕#670
2025-07-29 20:06:13,633 - INFO - 找到related_between场景: scene_id=712, 字幕#670
2025-07-29 20:06:13,633 - INFO - 字幕 #666 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:13,633 - INFO - 字幕 #670 找到 2 个overlap场景, 7 个between场景
2025-07-29 20:06:13,633 - INFO - 共收集 4 个未使用的overlap场景和 7 个未使用的between场景
2025-07-29 20:06:13,633 - INFO - 开始生成方案 #1
2025-07-29 20:06:13,633 - INFO - 方案 #1: 为字幕#666选择初始化overlap场景id=699
2025-07-29 20:06:13,633 - INFO - 方案 #1: 为字幕#670选择初始化overlap场景id=709
2025-07-29 20:06:13,633 - INFO - 方案 #1: 初始选择后，当前总时长=1.72秒
2025-07-29 20:06:13,633 - INFO - 方案 #1: 额外添加overlap场景id=698, 当前总时长=3.08秒
2025-07-29 20:06:13,633 - INFO - 方案 #1: 额外添加overlap场景id=708, 当前总时长=4.56秒
2025-07-29 20:06:13,633 - INFO - 方案 #1: 额外between选择后，当前总时长=4.56秒
2025-07-29 20:06:13,633 - INFO - 方案 #1: 额外添加between场景id=705, 当前总时长=5.84秒
2025-07-29 20:06:13,634 - INFO - 方案 #1: 场景总时长(5.84秒)大于音频时长(5.74秒)，需要裁剪
2025-07-29 20:06:13,634 - INFO - 调整前总时长: 5.84秒, 目标时长: 5.74秒
2025-07-29 20:06:13,634 - INFO - 需要裁剪 0.09秒
2025-07-29 20:06:13,634 - INFO - 裁剪最长场景ID=708：从1.48秒裁剪至1.39秒
2025-07-29 20:06:13,634 - INFO - 调整后总时长: 5.74秒，与目标时长差异: 0.00秒
2025-07-29 20:06:13,634 - INFO - 方案 #1 调整/填充后最终总时长: 5.74秒
2025-07-29 20:06:13,634 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:13,634 - INFO - ========== 当前模式：字幕 #40 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:13,634 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:13,634 - INFO - ========== 新模式：字幕 #40 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:13,634 - INFO - 
----- 处理字幕 #40 的方案 #1 -----
2025-07-29 20:06:13,634 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 20:06:13,634 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm50mok47
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\698.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=698，时长=1.36秒，累计时长=1.36秒
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\699.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=699，时长=0.88秒，累计时长=2.24秒
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\700.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=700，时长=0.68秒，累计时长=2.92秒
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\701.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=701，时长=0.76秒，累计时长=3.68秒
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\702.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=702，时长=1.00秒，累计时长=4.68秒
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\703.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=703，时长=0.84秒，累计时长=5.52秒
2025-07-29 20:06:13,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\704.mp4 (确认存在: True)
2025-07-29 20:06:13,635 - INFO - 添加场景ID=704，时长=1.12秒，累计时长=6.64秒
2025-07-29 20:06:13,635 - INFO - 准备合并 7 个场景文件，总时长约 6.64秒
2025-07-29 20:06:13,635 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/698.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/699.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/700.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/701.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/702.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/703.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/704.mp4'

2025-07-29 20:06:13,635 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpm50mok47\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpm50mok47\temp_combined.mp4
2025-07-29 20:06:13,828 - INFO - 合并后的视频时长: 6.80秒，目标音频时长: 5.74秒
2025-07-29 20:06:13,828 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpm50mok47\temp_combined.mp4 -ss 0 -to 5.743 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 20:06:14,188 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:14,188 - INFO - 目标音频时长: 5.74秒
2025-07-29 20:06:14,188 - INFO - 实际视频时长: 5.78秒
2025-07-29 20:06:14,188 - INFO - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:06:14,188 - INFO - ==========================================
2025-07-29 20:06:14,188 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:14,188 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 20:06:14,189 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm50mok47
2025-07-29 20:06:14,235 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:14,235 - INFO -   - 音频时长: 5.74秒
2025-07-29 20:06:14,235 - INFO -   - 视频时长: 5.78秒
2025-07-29 20:06:14,235 - INFO -   - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:06:14,235 - INFO - 
----- 处理字幕 #40 的方案 #2 -----
2025-07-29 20:06:14,235 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 20:06:14,236 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps5o46i43
2025-07-29 20:06:14,236 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\708.mp4 (确认存在: True)
2025-07-29 20:06:14,236 - INFO - 添加场景ID=708，时长=1.48秒，累计时长=1.48秒
2025-07-29 20:06:14,236 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\709.mp4 (确认存在: True)
2025-07-29 20:06:14,236 - INFO - 添加场景ID=709，时长=0.84秒，累计时长=2.32秒
2025-07-29 20:06:14,237 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\710.mp4 (确认存在: True)
2025-07-29 20:06:14,237 - INFO - 添加场景ID=710，时长=0.72秒，累计时长=3.04秒
2025-07-29 20:06:14,237 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\711.mp4 (确认存在: True)
2025-07-29 20:06:14,237 - INFO - 添加场景ID=711，时长=0.72秒，累计时长=3.76秒
2025-07-29 20:06:14,237 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\712.mp4 (确认存在: True)
2025-07-29 20:06:14,237 - INFO - 添加场景ID=712，时长=0.68秒，累计时长=4.44秒
2025-07-29 20:06:14,237 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\713.mp4 (确认存在: True)
2025-07-29 20:06:14,237 - INFO - 添加场景ID=713，时长=0.64秒，累计时长=5.08秒
2025-07-29 20:06:14,237 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\714.mp4 (确认存在: True)
2025-07-29 20:06:14,237 - INFO - 添加场景ID=714，时长=0.60秒，累计时长=5.68秒
2025-07-29 20:06:14,237 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\715.mp4 (确认存在: True)
2025-07-29 20:06:14,237 - INFO - 添加场景ID=715，时长=1.04秒，累计时长=6.72秒
2025-07-29 20:06:14,237 - INFO - 准备合并 8 个场景文件，总时长约 6.72秒
2025-07-29 20:06:14,237 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/708.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/709.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/710.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/711.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/712.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/713.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/714.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/715.mp4'

2025-07-29 20:06:14,237 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps5o46i43\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps5o46i43\temp_combined.mp4
2025-07-29 20:06:14,434 - INFO - 合并后的视频时长: 6.91秒，目标音频时长: 5.74秒
2025-07-29 20:06:14,434 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps5o46i43\temp_combined.mp4 -ss 0 -to 5.743 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 20:06:14,812 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:14,812 - INFO - 目标音频时长: 5.74秒
2025-07-29 20:06:14,812 - INFO - 实际视频时长: 5.78秒
2025-07-29 20:06:14,812 - INFO - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:06:14,812 - INFO - ==========================================
2025-07-29 20:06:14,812 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:14,812 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 20:06:14,813 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps5o46i43
2025-07-29 20:06:14,858 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:14,858 - INFO -   - 音频时长: 5.74秒
2025-07-29 20:06:14,858 - INFO -   - 视频时长: 5.78秒
2025-07-29 20:06:14,858 - INFO -   - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:06:14,858 - INFO - 
----- 处理字幕 #40 的方案 #3 -----
2025-07-29 20:06:14,858 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-29 20:06:14,858 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr5egso5t
2025-07-29 20:06:14,859 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\699.mp4 (确认存在: True)
2025-07-29 20:06:14,859 - INFO - 添加场景ID=699，时长=0.88秒，累计时长=0.88秒
2025-07-29 20:06:14,859 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\709.mp4 (确认存在: True)
2025-07-29 20:06:14,859 - INFO - 添加场景ID=709，时长=0.84秒，累计时长=1.72秒
2025-07-29 20:06:14,859 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\698.mp4 (确认存在: True)
2025-07-29 20:06:14,859 - INFO - 添加场景ID=698，时长=1.36秒，累计时长=3.08秒
2025-07-29 20:06:14,859 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\708.mp4 (确认存在: True)
2025-07-29 20:06:14,859 - INFO - 添加场景ID=708，时长=1.48秒，累计时长=4.56秒
2025-07-29 20:06:14,859 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\705.mp4 (确认存在: True)
2025-07-29 20:06:14,859 - INFO - 添加场景ID=705，时长=1.28秒，累计时长=5.84秒
2025-07-29 20:06:14,859 - INFO - 准备合并 5 个场景文件，总时长约 5.84秒
2025-07-29 20:06:14,859 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/699.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/709.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/698.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/708.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/705.mp4'

2025-07-29 20:06:14,859 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr5egso5t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr5egso5t\temp_combined.mp4
2025-07-29 20:06:15,031 - INFO - 合并后的视频时长: 5.96秒，目标音频时长: 5.74秒
2025-07-29 20:06:15,031 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr5egso5t\temp_combined.mp4 -ss 0 -to 5.743 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-29 20:06:15,390 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:15,390 - INFO - 目标音频时长: 5.74秒
2025-07-29 20:06:15,390 - INFO - 实际视频时长: 5.78秒
2025-07-29 20:06:15,390 - INFO - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:06:15,390 - INFO - ==========================================
2025-07-29 20:06:15,390 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:15,390 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-29 20:06:15,391 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr5egso5t
2025-07-29 20:06:15,437 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:15,437 - INFO -   - 音频时长: 5.74秒
2025-07-29 20:06:15,437 - INFO -   - 视频时长: 5.78秒
2025-07-29 20:06:15,437 - INFO -   - 时长差异: 0.04秒 (0.70%)
2025-07-29 20:06:15,437 - INFO - 
字幕 #40 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:15,437 - INFO - 生成的视频文件:
2025-07-29 20:06:15,437 - INFO -   1. F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 20:06:15,437 - INFO -   2. F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 20:06:15,437 - INFO -   3. F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-29 20:06:15,437 - INFO - ========== 字幕 #40 处理结束 ==========

