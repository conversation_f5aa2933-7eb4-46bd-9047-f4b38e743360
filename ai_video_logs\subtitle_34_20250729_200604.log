2025-07-29 20:06:04,471 - INFO - ========== 字幕 #34 处理开始 ==========
2025-07-29 20:06:04,471 - INFO - 字幕内容: 离开这个伤心之地，男人接到了七年前就向他抛出橄榄枝的威尔集团的电话，正式入职，成为欧洲区副总裁！
2025-07-29 20:06:04,471 - INFO - 字幕序号: [235, 244]
2025-07-29 20:06:04,471 - INFO - 音频文件详情:
2025-07-29 20:06:04,471 - INFO -   - 路径: output\34.wav
2025-07-29 20:06:04,471 - INFO -   - 时长: 7.77秒
2025-07-29 20:06:04,471 - INFO -   - 验证音频时长: 7.77秒
2025-07-29 20:06:04,471 - INFO - 字幕时间戳信息:
2025-07-29 20:06:04,471 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:04,471 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:04,471 - INFO -   - 根据生成的音频时长(7.77秒)已调整字幕时间戳
2025-07-29 20:06:04,471 - INFO - ========== 新模式：为字幕 #34 生成4套场景方案 ==========
2025-07-29 20:06:04,471 - INFO - 字幕序号列表: [235, 244]
2025-07-29 20:06:04,471 - INFO - 
--- 生成方案 #1：基于字幕序号 #235 ---
2025-07-29 20:06:04,472 - INFO - 开始为单个字幕序号 #235 匹配场景，目标时长: 7.77秒
2025-07-29 20:06:04,472 - INFO - 开始查找字幕序号 [235] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:04,472 - INFO - 找到related_overlap场景: scene_id=276, 字幕#235
2025-07-29 20:06:04,473 - INFO - 字幕 #235 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:04,473 - INFO - 字幕序号 #235 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:04,473 - INFO - 选择第一个overlap场景作为起点: scene_id=276
2025-07-29 20:06:04,473 - INFO - 添加起点场景: scene_id=276, 时长=3.56秒, 累计时长=3.56秒
2025-07-29 20:06:04,473 - INFO - 起点场景时长不足，需要延伸填充 4.21秒
2025-07-29 20:06:04,473 - INFO - 起点场景在原始列表中的索引: 275
2025-07-29 20:06:04,473 - INFO - 延伸添加场景: scene_id=277 (裁剪至 4.21秒)
2025-07-29 20:06:04,473 - INFO - 累计时长: 7.77秒
2025-07-29 20:06:04,473 - INFO - 字幕序号 #235 场景匹配完成，共选择 2 个场景，总时长: 7.77秒
2025-07-29 20:06:04,473 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:06:04,473 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:06:04,473 - INFO - 
--- 生成方案 #2：基于字幕序号 #244 ---
2025-07-29 20:06:04,473 - INFO - 开始为单个字幕序号 #244 匹配场景，目标时长: 7.77秒
2025-07-29 20:06:04,473 - INFO - 开始查找字幕序号 [244] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:04,473 - INFO - 找到related_overlap场景: scene_id=283, 字幕#244
2025-07-29 20:06:04,474 - INFO - 字幕 #244 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:04,474 - INFO - 字幕序号 #244 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:04,474 - INFO - 选择第一个overlap场景作为起点: scene_id=283
2025-07-29 20:06:04,474 - INFO - 添加起点场景: scene_id=283, 时长=2.36秒, 累计时长=2.36秒
2025-07-29 20:06:04,474 - INFO - 起点场景时长不足，需要延伸填充 5.41秒
2025-07-29 20:06:04,474 - INFO - 起点场景在原始列表中的索引: 282
2025-07-29 20:06:04,474 - INFO - 延伸添加场景: scene_id=284 (完整时长 1.08秒)
2025-07-29 20:06:04,474 - INFO - 累计时长: 3.44秒
2025-07-29 20:06:04,474 - INFO - 延伸添加场景: scene_id=285 (完整时长 1.24秒)
2025-07-29 20:06:04,474 - INFO - 累计时长: 4.68秒
2025-07-29 20:06:04,474 - INFO - 延伸添加场景: scene_id=286 (完整时长 1.24秒)
2025-07-29 20:06:04,474 - INFO - 累计时长: 5.92秒
2025-07-29 20:06:04,474 - INFO - 延伸添加场景: scene_id=287 (完整时长 1.60秒)
2025-07-29 20:06:04,475 - INFO - 累计时长: 7.52秒
2025-07-29 20:06:04,475 - INFO - 延伸添加场景: scene_id=288 (裁剪至 0.25秒)
2025-07-29 20:06:04,475 - INFO - 累计时长: 7.77秒
2025-07-29 20:06:04,475 - INFO - 字幕序号 #244 场景匹配完成，共选择 6 个场景，总时长: 7.77秒
2025-07-29 20:06:04,475 - INFO - 方案 #2 生成成功，包含 6 个场景
2025-07-29 20:06:04,475 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:04,475 - INFO - ========== 当前模式：为字幕 #34 生成 1 套场景方案 ==========
2025-07-29 20:06:04,475 - INFO - 开始查找字幕序号 [235, 244] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:04,475 - INFO - 找到related_overlap场景: scene_id=276, 字幕#235
2025-07-29 20:06:04,475 - INFO - 找到related_overlap场景: scene_id=283, 字幕#244
2025-07-29 20:06:04,476 - INFO - 字幕 #235 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:04,476 - INFO - 字幕 #244 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:04,476 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:04,476 - INFO - 开始生成方案 #1
2025-07-29 20:06:04,476 - INFO - 方案 #1: 为字幕#235选择初始化overlap场景id=276
2025-07-29 20:06:04,476 - INFO - 方案 #1: 为字幕#244选择初始化overlap场景id=283
2025-07-29 20:06:04,476 - INFO - 方案 #1: 初始选择后，当前总时长=5.92秒
2025-07-29 20:06:04,476 - INFO - 方案 #1: 额外between选择后，当前总时长=5.92秒
2025-07-29 20:06:04,476 - INFO - 方案 #1: 场景总时长(5.92秒)小于音频时长(7.77秒)，需要延伸填充
2025-07-29 20:06:04,476 - INFO - 方案 #1: 最后一个场景ID: 283
2025-07-29 20:06:04,476 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 282
2025-07-29 20:06:04,476 - INFO - 方案 #1: 需要填充时长: 1.85秒
2025-07-29 20:06:04,476 - INFO - 方案 #1: 追加场景 scene_id=284 (完整时长 1.08秒)
2025-07-29 20:06:04,476 - INFO - 方案 #1: 追加场景 scene_id=285 (裁剪至 0.77秒)
2025-07-29 20:06:04,476 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:04,476 - INFO - 方案 #1 调整/填充后最终总时长: 7.77秒
2025-07-29 20:06:04,476 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:04,476 - INFO - ========== 当前模式：字幕 #34 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:04,476 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:04,476 - INFO - ========== 新模式：字幕 #34 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:04,476 - INFO - 
----- 处理字幕 #34 的方案 #1 -----
2025-07-29 20:06:04,476 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 20:06:04,477 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpncvwsi3_
2025-07-29 20:06:04,477 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\276.mp4 (确认存在: True)
2025-07-29 20:06:04,477 - INFO - 添加场景ID=276，时长=3.56秒，累计时长=3.56秒
2025-07-29 20:06:04,477 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\277.mp4 (确认存在: True)
2025-07-29 20:06:04,477 - INFO - 添加场景ID=277，时长=4.52秒，累计时长=8.08秒
2025-07-29 20:06:04,477 - INFO - 准备合并 2 个场景文件，总时长约 8.08秒
2025-07-29 20:06:04,477 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/276.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/277.mp4'

2025-07-29 20:06:04,478 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpncvwsi3_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpncvwsi3_\temp_combined.mp4
2025-07-29 20:06:04,611 - INFO - 合并后的视频时长: 8.13秒，目标音频时长: 7.77秒
2025-07-29 20:06:04,611 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpncvwsi3_\temp_combined.mp4 -ss 0 -to 7.767 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 20:06:05,002 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:05,003 - INFO - 目标音频时长: 7.77秒
2025-07-29 20:06:05,003 - INFO - 实际视频时长: 7.82秒
2025-07-29 20:06:05,003 - INFO - 时长差异: 0.06秒 (0.72%)
2025-07-29 20:06:05,003 - INFO - ==========================================
2025-07-29 20:06:05,003 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:05,003 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 20:06:05,003 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpncvwsi3_
2025-07-29 20:06:05,053 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:05,053 - INFO -   - 音频时长: 7.77秒
2025-07-29 20:06:05,053 - INFO -   - 视频时长: 7.82秒
2025-07-29 20:06:05,053 - INFO -   - 时长差异: 0.06秒 (0.72%)
2025-07-29 20:06:05,053 - INFO - 
----- 处理字幕 #34 的方案 #2 -----
2025-07-29 20:06:05,053 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 20:06:05,054 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppbumq1pa
2025-07-29 20:06:05,054 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\283.mp4 (确认存在: True)
2025-07-29 20:06:05,054 - INFO - 添加场景ID=283，时长=2.36秒，累计时长=2.36秒
2025-07-29 20:06:05,054 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\284.mp4 (确认存在: True)
2025-07-29 20:06:05,054 - INFO - 添加场景ID=284，时长=1.08秒，累计时长=3.44秒
2025-07-29 20:06:05,054 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\285.mp4 (确认存在: True)
2025-07-29 20:06:05,054 - INFO - 添加场景ID=285，时长=1.24秒，累计时长=4.68秒
2025-07-29 20:06:05,054 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\286.mp4 (确认存在: True)
2025-07-29 20:06:05,054 - INFO - 添加场景ID=286，时长=1.24秒，累计时长=5.92秒
2025-07-29 20:06:05,054 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\287.mp4 (确认存在: True)
2025-07-29 20:06:05,054 - INFO - 添加场景ID=287，时长=1.60秒，累计时长=7.52秒
2025-07-29 20:06:05,055 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\288.mp4 (确认存在: True)
2025-07-29 20:06:05,055 - INFO - 添加场景ID=288，时长=2.92秒，累计时长=10.44秒
2025-07-29 20:06:05,055 - INFO - 准备合并 6 个场景文件，总时长约 10.44秒
2025-07-29 20:06:05,055 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/283.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/284.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/285.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/286.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/287.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/288.mp4'

2025-07-29 20:06:05,055 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppbumq1pa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppbumq1pa\temp_combined.mp4
2025-07-29 20:06:05,224 - INFO - 合并后的视频时长: 10.58秒，目标音频时长: 7.77秒
2025-07-29 20:06:05,224 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppbumq1pa\temp_combined.mp4 -ss 0 -to 7.767 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 20:06:05,632 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:05,632 - INFO - 目标音频时长: 7.77秒
2025-07-29 20:06:05,632 - INFO - 实际视频时长: 7.82秒
2025-07-29 20:06:05,632 - INFO - 时长差异: 0.06秒 (0.72%)
2025-07-29 20:06:05,632 - INFO - ==========================================
2025-07-29 20:06:05,632 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:05,632 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 20:06:05,633 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppbumq1pa
2025-07-29 20:06:05,680 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:05,680 - INFO -   - 音频时长: 7.77秒
2025-07-29 20:06:05,680 - INFO -   - 视频时长: 7.82秒
2025-07-29 20:06:05,680 - INFO -   - 时长差异: 0.06秒 (0.72%)
2025-07-29 20:06:05,680 - INFO - 
----- 处理字幕 #34 的方案 #3 -----
2025-07-29 20:06:05,680 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 20:06:05,681 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1f01sjyx
2025-07-29 20:06:05,681 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\276.mp4 (确认存在: True)
2025-07-29 20:06:05,681 - INFO - 添加场景ID=276，时长=3.56秒，累计时长=3.56秒
2025-07-29 20:06:05,681 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\283.mp4 (确认存在: True)
2025-07-29 20:06:05,681 - INFO - 添加场景ID=283，时长=2.36秒，累计时长=5.92秒
2025-07-29 20:06:05,681 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\284.mp4 (确认存在: True)
2025-07-29 20:06:05,681 - INFO - 添加场景ID=284，时长=1.08秒，累计时长=7.00秒
2025-07-29 20:06:05,682 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\285.mp4 (确认存在: True)
2025-07-29 20:06:05,682 - INFO - 添加场景ID=285，时长=1.24秒，累计时长=8.24秒
2025-07-29 20:06:05,682 - INFO - 准备合并 4 个场景文件，总时长约 8.24秒
2025-07-29 20:06:05,682 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/276.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/283.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/284.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/285.mp4'

2025-07-29 20:06:05,682 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1f01sjyx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1f01sjyx\temp_combined.mp4
2025-07-29 20:06:05,812 - INFO - 合并后的视频时长: 8.33秒，目标音频时长: 7.77秒
2025-07-29 20:06:05,812 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1f01sjyx\temp_combined.mp4 -ss 0 -to 7.767 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 20:06:06,215 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:06,215 - INFO - 目标音频时长: 7.77秒
2025-07-29 20:06:06,215 - INFO - 实际视频时长: 7.82秒
2025-07-29 20:06:06,215 - INFO - 时长差异: 0.06秒 (0.72%)
2025-07-29 20:06:06,215 - INFO - ==========================================
2025-07-29 20:06:06,215 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:06,215 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 20:06:06,216 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1f01sjyx
2025-07-29 20:06:06,262 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:06,262 - INFO -   - 音频时长: 7.77秒
2025-07-29 20:06:06,262 - INFO -   - 视频时长: 7.82秒
2025-07-29 20:06:06,262 - INFO -   - 时长差异: 0.06秒 (0.72%)
2025-07-29 20:06:06,262 - INFO - 
字幕 #34 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:06,262 - INFO - 生成的视频文件:
2025-07-29 20:06:06,262 - INFO -   1. F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 20:06:06,262 - INFO -   2. F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 20:06:06,262 - INFO -   3. F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 20:06:06,262 - INFO - ========== 字幕 #34 处理结束 ==========

