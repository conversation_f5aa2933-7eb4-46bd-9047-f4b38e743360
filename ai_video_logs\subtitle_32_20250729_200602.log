2025-07-29 20:06:02,062 - INFO - ========== 字幕 #32 处理开始 ==========
2025-07-29 20:06:02,062 - INFO - 字幕内容: 而他呢？想抽一包五块钱的烟都要写欠条，最后甚至无奈戒了烟，这是何等的讽刺！
2025-07-29 20:06:02,062 - INFO - 字幕序号: [554, 558]
2025-07-29 20:06:02,062 - INFO - 音频文件详情:
2025-07-29 20:06:02,062 - INFO -   - 路径: output\32.wav
2025-07-29 20:06:02,062 - INFO -   - 时长: 5.33秒
2025-07-29 20:06:02,062 - INFO -   - 验证音频时长: 5.33秒
2025-07-29 20:06:02,062 - INFO - 字幕时间戳信息:
2025-07-29 20:06:02,062 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:02,062 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:02,062 - INFO -   - 根据生成的音频时长(5.33秒)已调整字幕时间戳
2025-07-29 20:06:02,062 - INFO - ========== 新模式：为字幕 #32 生成4套场景方案 ==========
2025-07-29 20:06:02,062 - INFO - 字幕序号列表: [554, 558]
2025-07-29 20:06:02,062 - INFO - 
--- 生成方案 #1：基于字幕序号 #554 ---
2025-07-29 20:06:02,062 - INFO - 开始为单个字幕序号 #554 匹配场景，目标时长: 5.33秒
2025-07-29 20:06:02,062 - INFO - 开始查找字幕序号 [554] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:02,063 - INFO - 找到related_overlap场景: scene_id=581, 字幕#554
2025-07-29 20:06:02,064 - INFO - 字幕 #554 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:02,064 - INFO - 字幕序号 #554 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:02,064 - INFO - 选择第一个overlap场景作为起点: scene_id=581
2025-07-29 20:06:02,064 - INFO - 添加起点场景: scene_id=581, 时长=2.08秒, 累计时长=2.08秒
2025-07-29 20:06:02,064 - INFO - 起点场景时长不足，需要延伸填充 3.25秒
2025-07-29 20:06:02,064 - INFO - 起点场景在原始列表中的索引: 580
2025-07-29 20:06:02,064 - INFO - 延伸添加场景: scene_id=582 (完整时长 1.44秒)
2025-07-29 20:06:02,064 - INFO - 累计时长: 3.52秒
2025-07-29 20:06:02,064 - INFO - 延伸添加场景: scene_id=583 (完整时长 1.60秒)
2025-07-29 20:06:02,064 - INFO - 累计时长: 5.12秒
2025-07-29 20:06:02,064 - INFO - 延伸添加场景: scene_id=584 (裁剪至 0.21秒)
2025-07-29 20:06:02,064 - INFO - 累计时长: 5.33秒
2025-07-29 20:06:02,064 - INFO - 字幕序号 #554 场景匹配完成，共选择 4 个场景，总时长: 5.33秒
2025-07-29 20:06:02,064 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:02,064 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:02,064 - INFO - 
--- 生成方案 #2：基于字幕序号 #558 ---
2025-07-29 20:06:02,064 - INFO - 开始为单个字幕序号 #558 匹配场景，目标时长: 5.33秒
2025-07-29 20:06:02,064 - INFO - 开始查找字幕序号 [558] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:02,064 - INFO - 找到related_overlap场景: scene_id=585, 字幕#558
2025-07-29 20:06:02,065 - INFO - 字幕 #558 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:02,065 - INFO - 字幕序号 #558 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:02,065 - INFO - 选择第一个overlap场景作为起点: scene_id=585
2025-07-29 20:06:02,065 - INFO - 添加起点场景: scene_id=585, 时长=1.20秒, 累计时长=1.20秒
2025-07-29 20:06:02,065 - INFO - 起点场景时长不足，需要延伸填充 4.12秒
2025-07-29 20:06:02,065 - INFO - 起点场景在原始列表中的索引: 584
2025-07-29 20:06:02,065 - INFO - 延伸添加场景: scene_id=586 (完整时长 1.36秒)
2025-07-29 20:06:02,065 - INFO - 累计时长: 2.56秒
2025-07-29 20:06:02,065 - INFO - 延伸添加场景: scene_id=587 (完整时长 1.72秒)
2025-07-29 20:06:02,065 - INFO - 累计时长: 4.28秒
2025-07-29 20:06:02,065 - INFO - 延伸添加场景: scene_id=588 (裁剪至 1.05秒)
2025-07-29 20:06:02,065 - INFO - 累计时长: 5.33秒
2025-07-29 20:06:02,065 - INFO - 字幕序号 #558 场景匹配完成，共选择 4 个场景，总时长: 5.33秒
2025-07-29 20:06:02,065 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:02,066 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:02,066 - INFO - ========== 当前模式：为字幕 #32 生成 1 套场景方案 ==========
2025-07-29 20:06:02,066 - INFO - 开始查找字幕序号 [554, 558] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:02,066 - INFO - 找到related_overlap场景: scene_id=581, 字幕#554
2025-07-29 20:06:02,066 - INFO - 找到related_overlap场景: scene_id=585, 字幕#558
2025-07-29 20:06:02,066 - INFO - 字幕 #554 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:02,066 - INFO - 字幕 #558 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:02,066 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:02,066 - INFO - 开始生成方案 #1
2025-07-29 20:06:02,066 - INFO - 方案 #1: 为字幕#554选择初始化overlap场景id=581
2025-07-29 20:06:02,066 - INFO - 方案 #1: 为字幕#558选择初始化overlap场景id=585
2025-07-29 20:06:02,066 - INFO - 方案 #1: 初始选择后，当前总时长=3.28秒
2025-07-29 20:06:02,066 - INFO - 方案 #1: 额外between选择后，当前总时长=3.28秒
2025-07-29 20:06:02,066 - INFO - 方案 #1: 场景总时长(3.28秒)小于音频时长(5.33秒)，需要延伸填充
2025-07-29 20:06:02,066 - INFO - 方案 #1: 最后一个场景ID: 585
2025-07-29 20:06:02,066 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 584
2025-07-29 20:06:02,066 - INFO - 方案 #1: 需要填充时长: 2.05秒
2025-07-29 20:06:02,066 - INFO - 方案 #1: 追加场景 scene_id=586 (完整时长 1.36秒)
2025-07-29 20:06:02,066 - INFO - 方案 #1: 追加场景 scene_id=587 (裁剪至 0.69秒)
2025-07-29 20:06:02,067 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:02,067 - INFO - 方案 #1 调整/填充后最终总时长: 5.33秒
2025-07-29 20:06:02,067 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:02,067 - INFO - ========== 当前模式：字幕 #32 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:02,067 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:02,067 - INFO - ========== 新模式：字幕 #32 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:02,067 - INFO - 
----- 处理字幕 #32 的方案 #1 -----
2025-07-29 20:06:02,067 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 20:06:02,067 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgoe_h_qm
2025-07-29 20:06:02,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\581.mp4 (确认存在: True)
2025-07-29 20:06:02,068 - INFO - 添加场景ID=581，时长=2.08秒，累计时长=2.08秒
2025-07-29 20:06:02,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\582.mp4 (确认存在: True)
2025-07-29 20:06:02,068 - INFO - 添加场景ID=582，时长=1.44秒，累计时长=3.52秒
2025-07-29 20:06:02,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\583.mp4 (确认存在: True)
2025-07-29 20:06:02,068 - INFO - 添加场景ID=583，时长=1.60秒，累计时长=5.12秒
2025-07-29 20:06:02,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\584.mp4 (确认存在: True)
2025-07-29 20:06:02,068 - INFO - 添加场景ID=584，时长=2.20秒，累计时长=7.32秒
2025-07-29 20:06:02,068 - INFO - 准备合并 4 个场景文件，总时长约 7.32秒
2025-07-29 20:06:02,068 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/581.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/582.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/583.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/584.mp4'

2025-07-29 20:06:02,068 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgoe_h_qm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgoe_h_qm\temp_combined.mp4
2025-07-29 20:06:02,223 - INFO - 合并后的视频时长: 7.41秒，目标音频时长: 5.33秒
2025-07-29 20:06:02,223 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgoe_h_qm\temp_combined.mp4 -ss 0 -to 5.325 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 20:06:02,567 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:02,567 - INFO - 目标音频时长: 5.33秒
2025-07-29 20:06:02,567 - INFO - 实际视频时长: 5.38秒
2025-07-29 20:06:02,567 - INFO - 时长差异: 0.06秒 (1.09%)
2025-07-29 20:06:02,567 - INFO - ==========================================
2025-07-29 20:06:02,567 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:02,567 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 20:06:02,568 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgoe_h_qm
2025-07-29 20:06:02,621 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:02,621 - INFO -   - 音频时长: 5.33秒
2025-07-29 20:06:02,621 - INFO -   - 视频时长: 5.38秒
2025-07-29 20:06:02,621 - INFO -   - 时长差异: 0.06秒 (1.09%)
2025-07-29 20:06:02,621 - INFO - 
----- 处理字幕 #32 的方案 #2 -----
2025-07-29 20:06:02,621 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 20:06:02,621 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpah9uvh5d
2025-07-29 20:06:02,622 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\585.mp4 (确认存在: True)
2025-07-29 20:06:02,622 - INFO - 添加场景ID=585，时长=1.20秒，累计时长=1.20秒
2025-07-29 20:06:02,622 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\586.mp4 (确认存在: True)
2025-07-29 20:06:02,622 - INFO - 添加场景ID=586，时长=1.36秒，累计时长=2.56秒
2025-07-29 20:06:02,622 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\587.mp4 (确认存在: True)
2025-07-29 20:06:02,622 - INFO - 添加场景ID=587，时长=1.72秒，累计时长=4.28秒
2025-07-29 20:06:02,622 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\588.mp4 (确认存在: True)
2025-07-29 20:06:02,622 - INFO - 添加场景ID=588，时长=1.88秒，累计时长=6.16秒
2025-07-29 20:06:02,622 - INFO - 准备合并 4 个场景文件，总时长约 6.16秒
2025-07-29 20:06:02,622 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/585.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/586.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/587.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/588.mp4'

2025-07-29 20:06:02,622 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpah9uvh5d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpah9uvh5d\temp_combined.mp4
2025-07-29 20:06:02,779 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 5.33秒
2025-07-29 20:06:02,779 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpah9uvh5d\temp_combined.mp4 -ss 0 -to 5.325 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 20:06:03,130 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:03,130 - INFO - 目标音频时长: 5.33秒
2025-07-29 20:06:03,130 - INFO - 实际视频时长: 5.38秒
2025-07-29 20:06:03,130 - INFO - 时长差异: 0.06秒 (1.09%)
2025-07-29 20:06:03,130 - INFO - ==========================================
2025-07-29 20:06:03,130 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:03,130 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 20:06:03,131 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpah9uvh5d
2025-07-29 20:06:03,176 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:03,176 - INFO -   - 音频时长: 5.33秒
2025-07-29 20:06:03,176 - INFO -   - 视频时长: 5.38秒
2025-07-29 20:06:03,176 - INFO -   - 时长差异: 0.06秒 (1.09%)
2025-07-29 20:06:03,176 - INFO - 
----- 处理字幕 #32 的方案 #3 -----
2025-07-29 20:06:03,176 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 20:06:03,177 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpozk6681r
2025-07-29 20:06:03,177 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\581.mp4 (确认存在: True)
2025-07-29 20:06:03,177 - INFO - 添加场景ID=581，时长=2.08秒，累计时长=2.08秒
2025-07-29 20:06:03,177 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\585.mp4 (确认存在: True)
2025-07-29 20:06:03,177 - INFO - 添加场景ID=585，时长=1.20秒，累计时长=3.28秒
2025-07-29 20:06:03,177 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\586.mp4 (确认存在: True)
2025-07-29 20:06:03,177 - INFO - 添加场景ID=586，时长=1.36秒，累计时长=4.64秒
2025-07-29 20:06:03,177 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\587.mp4 (确认存在: True)
2025-07-29 20:06:03,177 - INFO - 添加场景ID=587，时长=1.72秒，累计时长=6.36秒
2025-07-29 20:06:03,177 - INFO - 准备合并 4 个场景文件，总时长约 6.36秒
2025-07-29 20:06:03,178 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/581.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/585.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/586.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/587.mp4'

2025-07-29 20:06:03,178 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpozk6681r\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpozk6681r\temp_combined.mp4
2025-07-29 20:06:03,322 - INFO - 合并后的视频时长: 6.45秒，目标音频时长: 5.33秒
2025-07-29 20:06:03,322 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpozk6681r\temp_combined.mp4 -ss 0 -to 5.325 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 20:06:03,665 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:03,665 - INFO - 目标音频时长: 5.33秒
2025-07-29 20:06:03,665 - INFO - 实际视频时长: 5.38秒
2025-07-29 20:06:03,665 - INFO - 时长差异: 0.06秒 (1.09%)
2025-07-29 20:06:03,665 - INFO - ==========================================
2025-07-29 20:06:03,665 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:03,665 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 20:06:03,666 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpozk6681r
2025-07-29 20:06:03,712 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:03,712 - INFO -   - 音频时长: 5.33秒
2025-07-29 20:06:03,712 - INFO -   - 视频时长: 5.38秒
2025-07-29 20:06:03,712 - INFO -   - 时长差异: 0.06秒 (1.09%)
2025-07-29 20:06:03,712 - INFO - 
字幕 #32 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:03,712 - INFO - 生成的视频文件:
2025-07-29 20:06:03,712 - INFO -   1. F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 20:06:03,712 - INFO -   2. F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 20:06:03,712 - INFO -   3. F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 20:06:03,712 - INFO - ========== 字幕 #32 处理结束 ==========

