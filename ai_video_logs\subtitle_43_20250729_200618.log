2025-07-29 20:06:18,332 - INFO - ========== 字幕 #43 处理开始 ==========
2025-07-29 20:06:18,332 - INFO - 字幕内容: 她甚至还在感叹，如果男人还在，国内的AI行业也不会被外人制裁，言语间充满了对过去的追忆。
2025-07-29 20:06:18,332 - INFO - 字幕序号: [911, 915]
2025-07-29 20:06:18,332 - INFO - 音频文件详情:
2025-07-29 20:06:18,332 - INFO -   - 路径: output\43.wav
2025-07-29 20:06:18,332 - INFO -   - 时长: 6.65秒
2025-07-29 20:06:18,332 - INFO -   - 验证音频时长: 6.65秒
2025-07-29 20:06:18,332 - INFO - 字幕时间戳信息:
2025-07-29 20:06:18,342 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:18,342 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:18,342 - INFO -   - 根据生成的音频时长(6.65秒)已调整字幕时间戳
2025-07-29 20:06:18,342 - INFO - ========== 新模式：为字幕 #43 生成4套场景方案 ==========
2025-07-29 20:06:18,342 - INFO - 字幕序号列表: [911, 915]
2025-07-29 20:06:18,342 - INFO - 
--- 生成方案 #1：基于字幕序号 #911 ---
2025-07-29 20:06:18,342 - INFO - 开始为单个字幕序号 #911 匹配场景，目标时长: 6.65秒
2025-07-29 20:06:18,342 - INFO - 开始查找字幕序号 [911] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:18,342 - INFO - 找到related_overlap场景: scene_id=942, 字幕#911
2025-07-29 20:06:18,343 - INFO - 字幕 #911 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:18,343 - INFO - 字幕序号 #911 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:18,343 - INFO - 选择第一个overlap场景作为起点: scene_id=942
2025-07-29 20:06:18,343 - INFO - 添加起点场景: scene_id=942, 时长=13.36秒, 累计时长=13.36秒
2025-07-29 20:06:18,343 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:18,343 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 20:06:18,343 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 20:06:18,343 - INFO - 
--- 生成方案 #2：基于字幕序号 #915 ---
2025-07-29 20:06:18,343 - INFO - 开始为单个字幕序号 #915 匹配场景，目标时长: 6.65秒
2025-07-29 20:06:18,343 - INFO - 开始查找字幕序号 [915] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:18,343 - INFO - 找到related_overlap场景: scene_id=942, 字幕#915
2025-07-29 20:06:18,344 - INFO - 字幕 #915 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:18,344 - INFO - 字幕序号 #915 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:18,344 - ERROR - 字幕序号 #915 没有找到任何可用的匹配场景
2025-07-29 20:06:18,344 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:18,344 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:18,344 - INFO - ========== 当前模式：为字幕 #43 生成 1 套场景方案 ==========
2025-07-29 20:06:18,344 - INFO - 开始查找字幕序号 [911, 915] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:18,345 - INFO - 找到related_overlap场景: scene_id=942, 字幕#911
2025-07-29 20:06:18,346 - INFO - 字幕 #911 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:18,346 - INFO - 字幕 #915 找到 0 个overlap场景, 0 个between场景
2025-07-29 20:06:18,346 - WARNING - 字幕 #915 没有找到任何匹配场景!
2025-07-29 20:06:18,346 - INFO - 共收集 1 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:18,346 - INFO - 开始生成方案 #1
2025-07-29 20:06:18,346 - INFO - 方案 #1: 为字幕#911选择初始化overlap场景id=942
2025-07-29 20:06:18,346 - INFO - 方案 #1: 初始选择后，当前总时长=13.36秒
2025-07-29 20:06:18,346 - INFO - 方案 #1: 额外between选择后，当前总时长=13.36秒
2025-07-29 20:06:18,346 - INFO - 方案 #1: 场景总时长(13.36秒)大于音频时长(6.65秒)，需要裁剪
2025-07-29 20:06:18,346 - INFO - 调整前总时长: 13.36秒, 目标时长: 6.65秒
2025-07-29 20:06:18,346 - INFO - 需要裁剪 6.71秒
2025-07-29 20:06:18,346 - INFO - 裁剪最长场景ID=942：从13.36秒裁剪至6.65秒
2025-07-29 20:06:18,346 - INFO - 调整后总时长: 6.65秒，与目标时长差异: 0.00秒
2025-07-29 20:06:18,346 - INFO - 方案 #1 调整/填充后最终总时长: 6.65秒
2025-07-29 20:06:18,346 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:18,346 - INFO - ========== 当前模式：字幕 #43 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:18,346 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:18,346 - INFO - ========== 新模式：字幕 #43 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:18,346 - INFO - 
----- 处理字幕 #43 的方案 #1 -----
2025-07-29 20:06:18,346 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 20:06:18,346 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaiqs2x5j
2025-07-29 20:06:18,347 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\942.mp4 (确认存在: True)
2025-07-29 20:06:18,347 - INFO - 添加场景ID=942，时长=13.36秒，累计时长=13.36秒
2025-07-29 20:06:18,347 - INFO - 场景总时长(13.36秒)已达到音频时长(6.65秒)的1.5倍，停止添加场景
2025-07-29 20:06:18,347 - INFO - 准备合并 1 个场景文件，总时长约 13.36秒
2025-07-29 20:06:18,347 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/942.mp4'

2025-07-29 20:06:18,347 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpaiqs2x5j\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpaiqs2x5j\temp_combined.mp4
2025-07-29 20:06:18,475 - INFO - 合并后的视频时长: 13.38秒，目标音频时长: 6.65秒
2025-07-29 20:06:18,475 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpaiqs2x5j\temp_combined.mp4 -ss 0 -to 6.646 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 20:06:18,829 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:18,829 - INFO - 目标音频时长: 6.65秒
2025-07-29 20:06:18,829 - INFO - 实际视频时长: 6.70秒
2025-07-29 20:06:18,829 - INFO - 时长差异: 0.06秒 (0.86%)
2025-07-29 20:06:18,829 - INFO - ==========================================
2025-07-29 20:06:18,829 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:18,829 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 20:06:18,831 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaiqs2x5j
2025-07-29 20:06:18,873 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:18,873 - INFO -   - 音频时长: 6.65秒
2025-07-29 20:06:18,873 - INFO -   - 视频时长: 6.70秒
2025-07-29 20:06:18,873 - INFO -   - 时长差异: 0.06秒 (0.86%)
2025-07-29 20:06:18,873 - INFO - 
----- 处理字幕 #43 的方案 #2 -----
2025-07-29 20:06:18,873 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 20:06:18,873 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp89fg7qrz
2025-07-29 20:06:18,874 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\942.mp4 (确认存在: True)
2025-07-29 20:06:18,874 - INFO - 添加场景ID=942，时长=13.36秒，累计时长=13.36秒
2025-07-29 20:06:18,874 - INFO - 场景总时长(13.36秒)已达到音频时长(6.65秒)的1.5倍，停止添加场景
2025-07-29 20:06:18,874 - INFO - 准备合并 1 个场景文件，总时长约 13.36秒
2025-07-29 20:06:18,874 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/942.mp4'

2025-07-29 20:06:18,874 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp89fg7qrz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp89fg7qrz\temp_combined.mp4
2025-07-29 20:06:18,984 - INFO - 合并后的视频时长: 13.38秒，目标音频时长: 6.65秒
2025-07-29 20:06:18,984 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp89fg7qrz\temp_combined.mp4 -ss 0 -to 6.646 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 20:06:19,339 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:19,339 - INFO - 目标音频时长: 6.65秒
2025-07-29 20:06:19,339 - INFO - 实际视频时长: 6.70秒
2025-07-29 20:06:19,339 - INFO - 时长差异: 0.06秒 (0.86%)
2025-07-29 20:06:19,339 - INFO - ==========================================
2025-07-29 20:06:19,339 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:19,339 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 20:06:19,340 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp89fg7qrz
2025-07-29 20:06:19,383 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:19,383 - INFO -   - 音频时长: 6.65秒
2025-07-29 20:06:19,383 - INFO -   - 视频时长: 6.70秒
2025-07-29 20:06:19,383 - INFO -   - 时长差异: 0.06秒 (0.86%)
2025-07-29 20:06:19,383 - INFO - 
字幕 #43 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:19,383 - INFO - 生成的视频文件:
2025-07-29 20:06:19,383 - INFO -   1. F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 20:06:19,383 - INFO -   2. F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 20:06:19,383 - INFO - ========== 字幕 #43 处理结束 ==========

