2025-07-29 20:06:24,338 - INFO - ========== 字幕 #48 处理开始 ==========
2025-07-29 20:06:24,338 - INFO - 字幕内容: 女人如遭电击，原来他早已和别人组建了家庭，就连“安安”也认了新的嫂子，她的世界彻底崩塌。
2025-07-29 20:06:24,338 - INFO - 字幕序号: [1274, 1279]
2025-07-29 20:06:24,338 - INFO - 音频文件详情:
2025-07-29 20:06:24,338 - INFO -   - 路径: output\48.wav
2025-07-29 20:06:24,338 - INFO -   - 时长: 5.68秒
2025-07-29 20:06:24,339 - INFO -   - 验证音频时长: 5.68秒
2025-07-29 20:06:24,339 - INFO - 字幕时间戳信息:
2025-07-29 20:06:24,340 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:24,340 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:24,340 - INFO -   - 根据生成的音频时长(5.68秒)已调整字幕时间戳
2025-07-29 20:06:24,340 - INFO - ========== 新模式：为字幕 #48 生成4套场景方案 ==========
2025-07-29 20:06:24,340 - INFO - 字幕序号列表: [1274, 1279]
2025-07-29 20:06:24,340 - INFO - 
--- 生成方案 #1：基于字幕序号 #1274 ---
2025-07-29 20:06:24,340 - INFO - 开始为单个字幕序号 #1274 匹配场景，目标时长: 5.68秒
2025-07-29 20:06:24,340 - INFO - 开始查找字幕序号 [1274] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:24,340 - INFO - 找到related_overlap场景: scene_id=1360, 字幕#1274
2025-07-29 20:06:24,341 - INFO - 字幕 #1274 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:24,341 - INFO - 字幕序号 #1274 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:24,341 - INFO - 选择第一个overlap场景作为起点: scene_id=1360
2025-07-29 20:06:24,341 - INFO - 添加起点场景: scene_id=1360, 时长=9.08秒, 累计时长=9.08秒
2025-07-29 20:06:24,341 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:24,341 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 20:06:24,341 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 20:06:24,341 - INFO - 
--- 生成方案 #2：基于字幕序号 #1279 ---
2025-07-29 20:06:24,341 - INFO - 开始为单个字幕序号 #1279 匹配场景，目标时长: 5.68秒
2025-07-29 20:06:24,341 - INFO - 开始查找字幕序号 [1279] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:24,342 - INFO - 找到related_overlap场景: scene_id=1362, 字幕#1279
2025-07-29 20:06:24,342 - INFO - 字幕 #1279 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:24,342 - INFO - 字幕序号 #1279 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:24,343 - INFO - 选择第一个overlap场景作为起点: scene_id=1362
2025-07-29 20:06:24,343 - INFO - 添加起点场景: scene_id=1362, 时长=7.96秒, 累计时长=7.96秒
2025-07-29 20:06:24,343 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:24,343 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 20:06:24,343 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:24,343 - INFO - ========== 当前模式：为字幕 #48 生成 1 套场景方案 ==========
2025-07-29 20:06:24,343 - INFO - 开始查找字幕序号 [1274, 1279] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:24,343 - INFO - 找到related_overlap场景: scene_id=1360, 字幕#1274
2025-07-29 20:06:24,343 - INFO - 找到related_overlap场景: scene_id=1362, 字幕#1279
2025-07-29 20:06:24,344 - INFO - 字幕 #1274 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:24,344 - INFO - 字幕 #1279 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:24,344 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:24,344 - INFO - 开始生成方案 #1
2025-07-29 20:06:24,344 - INFO - 方案 #1: 为字幕#1274选择初始化overlap场景id=1360
2025-07-29 20:06:24,344 - INFO - 方案 #1: 为字幕#1279选择初始化overlap场景id=1362
2025-07-29 20:06:24,344 - INFO - 方案 #1: 初始选择后，当前总时长=17.04秒
2025-07-29 20:06:24,344 - INFO - 方案 #1: 额外between选择后，当前总时长=17.04秒
2025-07-29 20:06:24,344 - INFO - 方案 #1: 场景总时长(17.04秒)大于音频时长(5.68秒)，需要裁剪
2025-07-29 20:06:24,344 - INFO - 调整前总时长: 17.04秒, 目标时长: 5.68秒
2025-07-29 20:06:24,344 - INFO - 需要裁剪 11.36秒
2025-07-29 20:06:24,344 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:24,344 - INFO - 裁剪场景ID=1360：从9.08秒裁剪至2.72秒
2025-07-29 20:06:24,344 - INFO - 裁剪场景ID=1362：从7.96秒裁剪至2.95秒
2025-07-29 20:06:24,344 - INFO - 调整后总时长: 5.68秒，与目标时长差异: 0.00秒
2025-07-29 20:06:24,344 - INFO - 方案 #1 调整/填充后最终总时长: 5.68秒
2025-07-29 20:06:24,344 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:24,344 - INFO - ========== 当前模式：字幕 #48 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:24,344 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:24,344 - INFO - ========== 新模式：字幕 #48 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:24,344 - INFO - 
----- 处理字幕 #48 的方案 #1 -----
2025-07-29 20:06:24,344 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 20:06:24,345 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpahvrbo4s
2025-07-29 20:06:24,345 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1360.mp4 (确认存在: True)
2025-07-29 20:06:24,345 - INFO - 添加场景ID=1360，时长=9.08秒，累计时长=9.08秒
2025-07-29 20:06:24,345 - INFO - 场景总时长(9.08秒)已达到音频时长(5.68秒)的1.5倍，停止添加场景
2025-07-29 20:06:24,345 - INFO - 准备合并 1 个场景文件，总时长约 9.08秒
2025-07-29 20:06:24,345 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1360.mp4'

2025-07-29 20:06:24,346 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpahvrbo4s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpahvrbo4s\temp_combined.mp4
2025-07-29 20:06:24,459 - INFO - 合并后的视频时长: 9.10秒，目标音频时长: 5.68秒
2025-07-29 20:06:24,459 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpahvrbo4s\temp_combined.mp4 -ss 0 -to 5.679 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 20:06:24,797 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:24,797 - INFO - 目标音频时长: 5.68秒
2025-07-29 20:06:24,797 - INFO - 实际视频时长: 5.70秒
2025-07-29 20:06:24,797 - INFO - 时长差异: 0.02秒 (0.42%)
2025-07-29 20:06:24,797 - INFO - ==========================================
2025-07-29 20:06:24,797 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:24,797 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 20:06:24,799 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpahvrbo4s
2025-07-29 20:06:24,845 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:24,845 - INFO -   - 音频时长: 5.68秒
2025-07-29 20:06:24,845 - INFO -   - 视频时长: 5.70秒
2025-07-29 20:06:24,845 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-07-29 20:06:24,845 - INFO - 
----- 处理字幕 #48 的方案 #2 -----
2025-07-29 20:06:24,845 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 20:06:24,845 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpx4_bbic0
2025-07-29 20:06:24,846 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1362.mp4 (确认存在: True)
2025-07-29 20:06:24,846 - INFO - 添加场景ID=1362，时长=7.96秒，累计时长=7.96秒
2025-07-29 20:06:24,846 - INFO - 准备合并 1 个场景文件，总时长约 7.96秒
2025-07-29 20:06:24,846 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1362.mp4'

2025-07-29 20:06:24,846 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpx4_bbic0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpx4_bbic0\temp_combined.mp4
2025-07-29 20:06:24,963 - INFO - 合并后的视频时长: 7.98秒，目标音频时长: 5.68秒
2025-07-29 20:06:24,964 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpx4_bbic0\temp_combined.mp4 -ss 0 -to 5.679 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 20:06:25,257 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:25,257 - INFO - 目标音频时长: 5.68秒
2025-07-29 20:06:25,257 - INFO - 实际视频时长: 5.70秒
2025-07-29 20:06:25,257 - INFO - 时长差异: 0.02秒 (0.42%)
2025-07-29 20:06:25,257 - INFO - ==========================================
2025-07-29 20:06:25,259 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:25,259 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 20:06:25,259 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpx4_bbic0
2025-07-29 20:06:25,304 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:25,304 - INFO -   - 音频时长: 5.68秒
2025-07-29 20:06:25,304 - INFO -   - 视频时长: 5.70秒
2025-07-29 20:06:25,304 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-07-29 20:06:25,304 - INFO - 
----- 处理字幕 #48 的方案 #3 -----
2025-07-29 20:06:25,305 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_3.mp4
2025-07-29 20:06:25,305 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkp5qqdls
2025-07-29 20:06:25,305 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1360.mp4 (确认存在: True)
2025-07-29 20:06:25,305 - INFO - 添加场景ID=1360，时长=9.08秒，累计时长=9.08秒
2025-07-29 20:06:25,305 - INFO - 场景总时长(9.08秒)已达到音频时长(5.68秒)的1.5倍，停止添加场景
2025-07-29 20:06:25,306 - INFO - 准备合并 1 个场景文件，总时长约 9.08秒
2025-07-29 20:06:25,306 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1360.mp4'

2025-07-29 20:06:25,306 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkp5qqdls\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkp5qqdls\temp_combined.mp4
2025-07-29 20:06:25,421 - INFO - 合并后的视频时长: 9.10秒，目标音频时长: 5.68秒
2025-07-29 20:06:25,421 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkp5qqdls\temp_combined.mp4 -ss 0 -to 5.679 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_3.mp4
2025-07-29 20:06:25,759 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:25,759 - INFO - 目标音频时长: 5.68秒
2025-07-29 20:06:25,759 - INFO - 实际视频时长: 5.70秒
2025-07-29 20:06:25,759 - INFO - 时长差异: 0.02秒 (0.42%)
2025-07-29 20:06:25,759 - INFO - ==========================================
2025-07-29 20:06:25,759 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:25,759 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_3.mp4
2025-07-29 20:06:25,760 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkp5qqdls
2025-07-29 20:06:25,806 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:25,806 - INFO -   - 音频时长: 5.68秒
2025-07-29 20:06:25,806 - INFO -   - 视频时长: 5.70秒
2025-07-29 20:06:25,806 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-07-29 20:06:25,806 - INFO - 
字幕 #48 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:25,806 - INFO - 生成的视频文件:
2025-07-29 20:06:25,806 - INFO -   1. F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 20:06:25,806 - INFO -   2. F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 20:06:25,806 - INFO -   3. F:/github/aicut_auto/newcut_ai\48_3.mp4
2025-07-29 20:06:25,806 - INFO - ========== 字幕 #48 处理结束 ==========

