2025-07-29 20:05:22,499 - INFO - ========== 字幕 #5 处理开始 ==========
2025-07-29 20:05:22,499 - INFO - 字幕内容: 此时，女人的正牌男友，那个被妹妹叫做哥哥的男人，正焦急地安慰着她，丝毫不知女友心中早已另有盘算。
2025-07-29 20:05:22,499 - INFO - 字幕序号: [28, 32]
2025-07-29 20:05:22,500 - INFO - 音频文件详情:
2025-07-29 20:05:22,500 - INFO -   - 路径: output\5.wav
2025-07-29 20:05:22,500 - INFO -   - 时长: 6.12秒
2025-07-29 20:05:22,500 - INFO -   - 验证音频时长: 6.12秒
2025-07-29 20:05:22,501 - INFO - 字幕时间戳信息:
2025-07-29 20:05:22,501 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:22,501 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:22,501 - INFO -   - 根据生成的音频时长(6.12秒)已调整字幕时间戳
2025-07-29 20:05:22,501 - INFO - ========== 新模式：为字幕 #5 生成4套场景方案 ==========
2025-07-29 20:05:22,501 - INFO - 字幕序号列表: [28, 32]
2025-07-29 20:05:22,501 - INFO - 
--- 生成方案 #1：基于字幕序号 #28 ---
2025-07-29 20:05:22,501 - INFO - 开始为单个字幕序号 #28 匹配场景，目标时长: 6.12秒
2025-07-29 20:05:22,501 - INFO - 开始查找字幕序号 [28] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:22,501 - INFO - 找到related_overlap场景: scene_id=26, 字幕#28
2025-07-29 20:05:22,502 - INFO - 找到related_between场景: scene_id=25, 字幕#28
2025-07-29 20:05:22,502 - INFO - 找到related_between场景: scene_id=27, 字幕#28
2025-07-29 20:05:22,503 - INFO - 字幕 #28 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:22,503 - INFO - 字幕序号 #28 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:05:22,503 - INFO - 选择第一个overlap场景作为起点: scene_id=26
2025-07-29 20:05:22,503 - INFO - 添加起点场景: scene_id=26, 时长=2.44秒, 累计时长=2.44秒
2025-07-29 20:05:22,503 - INFO - 起点场景时长不足，需要延伸填充 3.68秒
2025-07-29 20:05:22,503 - INFO - 起点场景在原始列表中的索引: 25
2025-07-29 20:05:22,503 - INFO - 延伸添加场景: scene_id=27 (完整时长 1.40秒)
2025-07-29 20:05:22,503 - INFO - 累计时长: 3.84秒
2025-07-29 20:05:22,503 - INFO - 延伸添加场景: scene_id=28 (裁剪至 2.28秒)
2025-07-29 20:05:22,503 - INFO - 累计时长: 6.12秒
2025-07-29 20:05:22,503 - INFO - 字幕序号 #28 场景匹配完成，共选择 3 个场景，总时长: 6.12秒
2025-07-29 20:05:22,503 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:22,503 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:22,503 - INFO - 
--- 生成方案 #2：基于字幕序号 #32 ---
2025-07-29 20:05:22,503 - INFO - 开始为单个字幕序号 #32 匹配场景，目标时长: 6.12秒
2025-07-29 20:05:22,503 - INFO - 开始查找字幕序号 [32] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:22,503 - INFO - 找到related_overlap场景: scene_id=30, 字幕#32
2025-07-29 20:05:22,505 - INFO - 字幕 #32 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:22,505 - INFO - 字幕序号 #32 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:22,505 - INFO - 选择第一个overlap场景作为起点: scene_id=30
2025-07-29 20:05:22,505 - INFO - 添加起点场景: scene_id=30, 时长=4.00秒, 累计时长=4.00秒
2025-07-29 20:05:22,505 - INFO - 起点场景时长不足，需要延伸填充 2.12秒
2025-07-29 20:05:22,505 - INFO - 起点场景在原始列表中的索引: 29
2025-07-29 20:05:22,505 - INFO - 延伸添加场景: scene_id=31 (完整时长 2.12秒)
2025-07-29 20:05:22,505 - INFO - 累计时长: 6.12秒
2025-07-29 20:05:22,505 - INFO - 字幕序号 #32 场景匹配完成，共选择 2 个场景，总时长: 6.12秒
2025-07-29 20:05:22,505 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:05:22,505 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:22,505 - INFO - ========== 当前模式：为字幕 #5 生成 1 套场景方案 ==========
2025-07-29 20:05:22,505 - INFO - 开始查找字幕序号 [28, 32] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:22,505 - INFO - 找到related_overlap场景: scene_id=26, 字幕#28
2025-07-29 20:05:22,505 - INFO - 找到related_overlap场景: scene_id=30, 字幕#32
2025-07-29 20:05:22,506 - INFO - 找到related_between场景: scene_id=25, 字幕#28
2025-07-29 20:05:22,506 - INFO - 找到related_between场景: scene_id=27, 字幕#28
2025-07-29 20:05:22,506 - INFO - 字幕 #28 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:05:22,506 - INFO - 字幕 #32 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:22,506 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 20:05:22,506 - INFO - 开始生成方案 #1
2025-07-29 20:05:22,506 - INFO - 方案 #1: 为字幕#28选择初始化overlap场景id=26
2025-07-29 20:05:22,506 - INFO - 方案 #1: 为字幕#32选择初始化overlap场景id=30
2025-07-29 20:05:22,506 - INFO - 方案 #1: 初始选择后，当前总时长=6.44秒
2025-07-29 20:05:22,506 - INFO - 方案 #1: 额外between选择后，当前总时长=6.44秒
2025-07-29 20:05:22,507 - INFO - 方案 #1: 场景总时长(6.44秒)大于音频时长(6.12秒)，需要裁剪
2025-07-29 20:05:22,507 - INFO - 调整前总时长: 6.44秒, 目标时长: 6.12秒
2025-07-29 20:05:22,507 - INFO - 需要裁剪 0.32秒
2025-07-29 20:05:22,507 - INFO - 裁剪最长场景ID=30：从4.00秒裁剪至3.68秒
2025-07-29 20:05:22,507 - INFO - 调整后总时长: 6.12秒，与目标时长差异: 0.00秒
2025-07-29 20:05:22,507 - INFO - 方案 #1 调整/填充后最终总时长: 6.12秒
2025-07-29 20:05:22,507 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:22,507 - INFO - ========== 当前模式：字幕 #5 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:22,507 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:22,507 - INFO - ========== 新模式：字幕 #5 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:22,507 - INFO - 
----- 处理字幕 #5 的方案 #1 -----
2025-07-29 20:05:22,507 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 20:05:22,507 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbwzq9wmx
2025-07-29 20:05:22,508 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\26.mp4 (确认存在: True)
2025-07-29 20:05:22,508 - INFO - 添加场景ID=26，时长=2.44秒，累计时长=2.44秒
2025-07-29 20:05:22,508 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\27.mp4 (确认存在: True)
2025-07-29 20:05:22,508 - INFO - 添加场景ID=27，时长=1.40秒，累计时长=3.84秒
2025-07-29 20:05:22,508 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\28.mp4 (确认存在: True)
2025-07-29 20:05:22,508 - INFO - 添加场景ID=28，时长=2.84秒，累计时长=6.68秒
2025-07-29 20:05:22,508 - INFO - 准备合并 3 个场景文件，总时长约 6.68秒
2025-07-29 20:05:22,508 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/26.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/27.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/28.mp4'

2025-07-29 20:05:22,508 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbwzq9wmx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbwzq9wmx\temp_combined.mp4
2025-07-29 20:05:22,634 - INFO - 合并后的视频时长: 6.75秒，目标音频时长: 6.12秒
2025-07-29 20:05:22,634 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbwzq9wmx\temp_combined.mp4 -ss 0 -to 6.119 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 20:05:22,985 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:22,985 - INFO - 目标音频时长: 6.12秒
2025-07-29 20:05:22,985 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:05:22,985 - INFO - 时长差异: 0.02秒 (0.39%)
2025-07-29 20:05:22,985 - INFO - ==========================================
2025-07-29 20:05:22,985 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:22,985 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 20:05:22,985 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbwzq9wmx
2025-07-29 20:05:23,028 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:23,028 - INFO -   - 音频时长: 6.12秒
2025-07-29 20:05:23,028 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:05:23,028 - INFO -   - 时长差异: 0.02秒 (0.39%)
2025-07-29 20:05:23,028 - INFO - 
----- 处理字幕 #5 的方案 #2 -----
2025-07-29 20:05:23,028 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 20:05:23,029 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7sn_l385
2025-07-29 20:05:23,029 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\30.mp4 (确认存在: True)
2025-07-29 20:05:23,029 - INFO - 添加场景ID=30，时长=4.00秒，累计时长=4.00秒
2025-07-29 20:05:23,029 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\31.mp4 (确认存在: True)
2025-07-29 20:05:23,029 - INFO - 添加场景ID=31，时长=2.12秒，累计时长=6.12秒
2025-07-29 20:05:23,029 - INFO - 准备合并 2 个场景文件，总时长约 6.12秒
2025-07-29 20:05:23,029 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/30.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/31.mp4'

2025-07-29 20:05:23,029 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7sn_l385\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7sn_l385\temp_combined.mp4
2025-07-29 20:05:23,171 - INFO - 合并后的视频时长: 6.17秒，目标音频时长: 6.12秒
2025-07-29 20:05:23,171 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7sn_l385\temp_combined.mp4 -ss 0 -to 6.119 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 20:05:23,541 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:23,541 - INFO - 目标音频时长: 6.12秒
2025-07-29 20:05:23,541 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:05:23,541 - INFO - 时长差异: 0.02秒 (0.39%)
2025-07-29 20:05:23,541 - INFO - ==========================================
2025-07-29 20:05:23,541 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:23,541 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 20:05:23,542 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7sn_l385
2025-07-29 20:05:23,588 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:23,588 - INFO -   - 音频时长: 6.12秒
2025-07-29 20:05:23,588 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:05:23,588 - INFO -   - 时长差异: 0.02秒 (0.39%)
2025-07-29 20:05:23,588 - INFO - 
----- 处理字幕 #5 的方案 #3 -----
2025-07-29 20:05:23,588 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 20:05:23,588 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpon4mbuaz
2025-07-29 20:05:23,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\26.mp4 (确认存在: True)
2025-07-29 20:05:23,589 - INFO - 添加场景ID=26，时长=2.44秒，累计时长=2.44秒
2025-07-29 20:05:23,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\30.mp4 (确认存在: True)
2025-07-29 20:05:23,589 - INFO - 添加场景ID=30，时长=4.00秒，累计时长=6.44秒
2025-07-29 20:05:23,589 - INFO - 准备合并 2 个场景文件，总时长约 6.44秒
2025-07-29 20:05:23,589 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/26.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/30.mp4'

2025-07-29 20:05:23,589 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpon4mbuaz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpon4mbuaz\temp_combined.mp4
2025-07-29 20:05:23,711 - INFO - 合并后的视频时长: 6.49秒，目标音频时长: 6.12秒
2025-07-29 20:05:23,711 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpon4mbuaz\temp_combined.mp4 -ss 0 -to 6.119 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 20:05:24,044 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:24,044 - INFO - 目标音频时长: 6.12秒
2025-07-29 20:05:24,044 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:05:24,044 - INFO - 时长差异: 0.02秒 (0.39%)
2025-07-29 20:05:24,044 - INFO - ==========================================
2025-07-29 20:05:24,044 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:24,044 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 20:05:24,045 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpon4mbuaz
2025-07-29 20:05:24,087 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:24,087 - INFO -   - 音频时长: 6.12秒
2025-07-29 20:05:24,087 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:05:24,087 - INFO -   - 时长差异: 0.02秒 (0.39%)
2025-07-29 20:05:24,087 - INFO - 
字幕 #5 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:24,087 - INFO - 生成的视频文件:
2025-07-29 20:05:24,087 - INFO -   1. F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 20:05:24,087 - INFO -   2. F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 20:05:24,087 - INFO -   3. F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 20:05:24,087 - INFO - ========== 字幕 #5 处理结束 ==========

