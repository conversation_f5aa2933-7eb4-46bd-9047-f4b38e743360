2025-07-29 20:05:39,619 - INFO - ========== 字幕 #17 处理开始 ==========
2025-07-29 20:05:39,619 - INFO - 字幕内容: 病床前，弥留之际的妹妹看着电视上姐姐风光订婚的新闻，眼中充满了失望与痛苦。
2025-07-29 20:05:39,619 - INFO - 字幕序号: [129, 135]
2025-07-29 20:05:39,619 - INFO - 音频文件详情:
2025-07-29 20:05:39,619 - INFO -   - 路径: output\17.wav
2025-07-29 20:05:39,619 - INFO -   - 时长: 6.48秒
2025-07-29 20:05:39,619 - INFO -   - 验证音频时长: 6.48秒
2025-07-29 20:05:39,619 - INFO - 字幕时间戳信息:
2025-07-29 20:05:39,619 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:39,619 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:39,620 - INFO -   - 根据生成的音频时长(6.48秒)已调整字幕时间戳
2025-07-29 20:05:39,620 - INFO - ========== 新模式：为字幕 #17 生成4套场景方案 ==========
2025-07-29 20:05:39,620 - INFO - 字幕序号列表: [129, 135]
2025-07-29 20:05:39,620 - INFO - 
--- 生成方案 #1：基于字幕序号 #129 ---
2025-07-29 20:05:39,620 - INFO - 开始为单个字幕序号 #129 匹配场景，目标时长: 6.48秒
2025-07-29 20:05:39,620 - INFO - 开始查找字幕序号 [129] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:39,620 - INFO - 找到related_overlap场景: scene_id=149, 字幕#129
2025-07-29 20:05:39,621 - INFO - 字幕 #129 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:39,621 - INFO - 字幕序号 #129 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:39,621 - INFO - 选择第一个overlap场景作为起点: scene_id=149
2025-07-29 20:05:39,621 - INFO - 添加起点场景: scene_id=149, 时长=3.36秒, 累计时长=3.36秒
2025-07-29 20:05:39,621 - INFO - 起点场景时长不足，需要延伸填充 3.12秒
2025-07-29 20:05:39,621 - INFO - 起点场景在原始列表中的索引: 148
2025-07-29 20:05:39,621 - INFO - 延伸添加场景: scene_id=150 (完整时长 2.80秒)
2025-07-29 20:05:39,621 - INFO - 累计时长: 6.16秒
2025-07-29 20:05:39,621 - INFO - 延伸添加场景: scene_id=151 (裁剪至 0.32秒)
2025-07-29 20:05:39,621 - INFO - 累计时长: 6.48秒
2025-07-29 20:05:39,621 - INFO - 字幕序号 #129 场景匹配完成，共选择 3 个场景，总时长: 6.48秒
2025-07-29 20:05:39,621 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:05:39,621 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:05:39,621 - INFO - 
--- 生成方案 #2：基于字幕序号 #135 ---
2025-07-29 20:05:39,621 - INFO - 开始为单个字幕序号 #135 匹配场景，目标时长: 6.48秒
2025-07-29 20:05:39,621 - INFO - 开始查找字幕序号 [135] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:39,621 - INFO - 找到related_overlap场景: scene_id=152, 字幕#135
2025-07-29 20:05:39,621 - INFO - 找到related_between场景: scene_id=153, 字幕#135
2025-07-29 20:05:39,622 - INFO - 字幕 #135 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:39,622 - INFO - 字幕序号 #135 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:39,622 - INFO - 选择第一个overlap场景作为起点: scene_id=152
2025-07-29 20:05:39,622 - INFO - 添加起点场景: scene_id=152, 时长=5.48秒, 累计时长=5.48秒
2025-07-29 20:05:39,622 - INFO - 起点场景时长不足，需要延伸填充 1.00秒
2025-07-29 20:05:39,622 - INFO - 起点场景在原始列表中的索引: 151
2025-07-29 20:05:39,622 - INFO - 延伸添加场景: scene_id=153 (完整时长 0.92秒)
2025-07-29 20:05:39,622 - INFO - 累计时长: 6.40秒
2025-07-29 20:05:39,622 - INFO - 延伸添加场景: scene_id=154 (裁剪至 0.08秒)
2025-07-29 20:05:39,622 - INFO - 累计时长: 6.48秒
2025-07-29 20:05:39,622 - INFO - 字幕序号 #135 场景匹配完成，共选择 3 个场景，总时长: 6.48秒
2025-07-29 20:05:39,622 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:39,622 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:39,622 - INFO - ========== 当前模式：为字幕 #17 生成 1 套场景方案 ==========
2025-07-29 20:05:39,622 - INFO - 开始查找字幕序号 [129, 135] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:39,622 - INFO - 找到related_overlap场景: scene_id=149, 字幕#129
2025-07-29 20:05:39,622 - INFO - 找到related_overlap场景: scene_id=152, 字幕#135
2025-07-29 20:05:39,623 - INFO - 找到related_between场景: scene_id=153, 字幕#135
2025-07-29 20:05:39,623 - INFO - 字幕 #129 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:39,623 - INFO - 字幕 #135 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:39,623 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:05:39,623 - INFO - 开始生成方案 #1
2025-07-29 20:05:39,623 - INFO - 方案 #1: 为字幕#129选择初始化overlap场景id=149
2025-07-29 20:05:39,623 - INFO - 方案 #1: 为字幕#135选择初始化overlap场景id=152
2025-07-29 20:05:39,623 - INFO - 方案 #1: 初始选择后，当前总时长=8.84秒
2025-07-29 20:05:39,623 - INFO - 方案 #1: 额外between选择后，当前总时长=8.84秒
2025-07-29 20:05:39,624 - INFO - 方案 #1: 场景总时长(8.84秒)大于音频时长(6.48秒)，需要裁剪
2025-07-29 20:05:39,624 - INFO - 调整前总时长: 8.84秒, 目标时长: 6.48秒
2025-07-29 20:05:39,624 - INFO - 需要裁剪 2.36秒
2025-07-29 20:05:39,624 - INFO - 裁剪最长场景ID=152：从5.48秒裁剪至3.12秒
2025-07-29 20:05:39,624 - INFO - 调整后总时长: 6.48秒，与目标时长差异: 0.00秒
2025-07-29 20:05:39,624 - INFO - 方案 #1 调整/填充后最终总时长: 6.48秒
2025-07-29 20:05:39,624 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:39,624 - INFO - ========== 当前模式：字幕 #17 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:39,624 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:39,624 - INFO - ========== 新模式：字幕 #17 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:39,624 - INFO - 
----- 处理字幕 #17 的方案 #1 -----
2025-07-29 20:05:39,624 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 20:05:39,624 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5s9cqgsn
2025-07-29 20:05:39,625 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\149.mp4 (确认存在: True)
2025-07-29 20:05:39,625 - INFO - 添加场景ID=149，时长=3.36秒，累计时长=3.36秒
2025-07-29 20:05:39,625 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\150.mp4 (确认存在: True)
2025-07-29 20:05:39,625 - INFO - 添加场景ID=150，时长=2.80秒，累计时长=6.16秒
2025-07-29 20:05:39,625 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\151.mp4 (确认存在: True)
2025-07-29 20:05:39,625 - INFO - 添加场景ID=151，时长=1.96秒，累计时长=8.12秒
2025-07-29 20:05:39,625 - INFO - 准备合并 3 个场景文件，总时长约 8.12秒
2025-07-29 20:05:39,625 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/149.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/150.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/151.mp4'

2025-07-29 20:05:39,625 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5s9cqgsn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5s9cqgsn\temp_combined.mp4
2025-07-29 20:05:39,753 - INFO - 合并后的视频时长: 8.19秒，目标音频时长: 6.48秒
2025-07-29 20:05:39,753 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5s9cqgsn\temp_combined.mp4 -ss 0 -to 6.479 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 20:05:40,120 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:40,120 - INFO - 目标音频时长: 6.48秒
2025-07-29 20:05:40,120 - INFO - 实际视频时长: 6.50秒
2025-07-29 20:05:40,120 - INFO - 时长差异: 0.02秒 (0.37%)
2025-07-29 20:05:40,121 - INFO - ==========================================
2025-07-29 20:05:40,121 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:40,121 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 20:05:40,121 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5s9cqgsn
2025-07-29 20:05:40,167 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:40,167 - INFO -   - 音频时长: 6.48秒
2025-07-29 20:05:40,167 - INFO -   - 视频时长: 6.50秒
2025-07-29 20:05:40,167 - INFO -   - 时长差异: 0.02秒 (0.37%)
2025-07-29 20:05:40,167 - INFO - 
----- 处理字幕 #17 的方案 #2 -----
2025-07-29 20:05:40,167 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 20:05:40,167 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo8waicgb
2025-07-29 20:05:40,167 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\152.mp4 (确认存在: True)
2025-07-29 20:05:40,167 - INFO - 添加场景ID=152，时长=5.48秒，累计时长=5.48秒
2025-07-29 20:05:40,168 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\153.mp4 (确认存在: True)
2025-07-29 20:05:40,168 - INFO - 添加场景ID=153，时长=0.92秒，累计时长=6.40秒
2025-07-29 20:05:40,168 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\154.mp4 (确认存在: True)
2025-07-29 20:05:40,168 - INFO - 添加场景ID=154，时长=1.84秒，累计时长=8.24秒
2025-07-29 20:05:40,168 - INFO - 准备合并 3 个场景文件，总时长约 8.24秒
2025-07-29 20:05:40,168 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/152.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/153.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/154.mp4'

2025-07-29 20:05:40,168 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpo8waicgb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpo8waicgb\temp_combined.mp4
2025-07-29 20:05:40,307 - INFO - 合并后的视频时长: 8.31秒，目标音频时长: 6.48秒
2025-07-29 20:05:40,307 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpo8waicgb\temp_combined.mp4 -ss 0 -to 6.479 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 20:05:40,670 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:40,670 - INFO - 目标音频时长: 6.48秒
2025-07-29 20:05:40,670 - INFO - 实际视频时长: 6.50秒
2025-07-29 20:05:40,670 - INFO - 时长差异: 0.02秒 (0.37%)
2025-07-29 20:05:40,670 - INFO - ==========================================
2025-07-29 20:05:40,670 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:40,670 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 20:05:40,671 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo8waicgb
2025-07-29 20:05:40,717 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:40,717 - INFO -   - 音频时长: 6.48秒
2025-07-29 20:05:40,717 - INFO -   - 视频时长: 6.50秒
2025-07-29 20:05:40,717 - INFO -   - 时长差异: 0.02秒 (0.37%)
2025-07-29 20:05:40,717 - INFO - 
----- 处理字幕 #17 的方案 #3 -----
2025-07-29 20:05:40,717 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-07-29 20:05:40,718 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3vk3lfxy
2025-07-29 20:05:40,718 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\149.mp4 (确认存在: True)
2025-07-29 20:05:40,718 - INFO - 添加场景ID=149，时长=3.36秒，累计时长=3.36秒
2025-07-29 20:05:40,718 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\152.mp4 (确认存在: True)
2025-07-29 20:05:40,718 - INFO - 添加场景ID=152，时长=5.48秒，累计时长=8.84秒
2025-07-29 20:05:40,718 - INFO - 准备合并 2 个场景文件，总时长约 8.84秒
2025-07-29 20:05:40,718 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/149.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/152.mp4'

2025-07-29 20:05:40,718 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3vk3lfxy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3vk3lfxy\temp_combined.mp4
2025-07-29 20:05:40,844 - INFO - 合并后的视频时长: 8.89秒，目标音频时长: 6.48秒
2025-07-29 20:05:40,844 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3vk3lfxy\temp_combined.mp4 -ss 0 -to 6.479 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-07-29 20:05:41,195 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:41,195 - INFO - 目标音频时长: 6.48秒
2025-07-29 20:05:41,195 - INFO - 实际视频时长: 6.50秒
2025-07-29 20:05:41,195 - INFO - 时长差异: 0.02秒 (0.37%)
2025-07-29 20:05:41,195 - INFO - ==========================================
2025-07-29 20:05:41,195 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:41,195 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-07-29 20:05:41,196 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3vk3lfxy
2025-07-29 20:05:41,241 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:41,241 - INFO -   - 音频时长: 6.48秒
2025-07-29 20:05:41,241 - INFO -   - 视频时长: 6.50秒
2025-07-29 20:05:41,241 - INFO -   - 时长差异: 0.02秒 (0.37%)
2025-07-29 20:05:41,241 - INFO - 
字幕 #17 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:41,241 - INFO - 生成的视频文件:
2025-07-29 20:05:41,241 - INFO -   1. F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 20:05:41,241 - INFO -   2. F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 20:05:41,241 - INFO -   3. F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-07-29 20:05:41,241 - INFO - ========== 字幕 #17 处理结束 ==========

