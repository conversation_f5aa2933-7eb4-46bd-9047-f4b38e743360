2025-07-29 20:06:12,677 - INFO - ========== 字幕 #39 处理开始 ==========
2025-07-29 20:06:12,677 - INFO - 字幕内容: 她终于明白，那天男人没有骗她，是她，亲手害死了那个最喜欢她的妹妹。
2025-07-29 20:06:12,677 - INFO - 字幕序号: [631, 636]
2025-07-29 20:06:12,677 - INFO - 音频文件详情:
2025-07-29 20:06:12,677 - INFO -   - 路径: output\39.wav
2025-07-29 20:06:12,677 - INFO -   - 时长: 5.10秒
2025-07-29 20:06:12,679 - INFO -   - 验证音频时长: 5.10秒
2025-07-29 20:06:12,679 - INFO - 字幕时间戳信息:
2025-07-29 20:06:12,688 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:12,688 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:12,688 - INFO -   - 根据生成的音频时长(5.10秒)已调整字幕时间戳
2025-07-29 20:06:12,688 - INFO - ========== 新模式：为字幕 #39 生成4套场景方案 ==========
2025-07-29 20:06:12,688 - INFO - 字幕序号列表: [631, 636]
2025-07-29 20:06:12,688 - INFO - 
--- 生成方案 #1：基于字幕序号 #631 ---
2025-07-29 20:06:12,688 - INFO - 开始为单个字幕序号 #631 匹配场景，目标时长: 5.10秒
2025-07-29 20:06:12,688 - INFO - 开始查找字幕序号 [631] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:12,689 - INFO - 找到related_overlap场景: scene_id=679, 字幕#631
2025-07-29 20:06:12,689 - INFO - 找到related_between场景: scene_id=678, 字幕#631
2025-07-29 20:06:12,690 - INFO - 字幕 #631 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:12,690 - INFO - 字幕序号 #631 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:12,690 - INFO - 选择第一个overlap场景作为起点: scene_id=679
2025-07-29 20:06:12,690 - INFO - 添加起点场景: scene_id=679, 时长=10.64秒, 累计时长=10.64秒
2025-07-29 20:06:12,690 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:12,690 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 20:06:12,690 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 20:06:12,690 - INFO - 
--- 生成方案 #2：基于字幕序号 #636 ---
2025-07-29 20:06:12,690 - INFO - 开始为单个字幕序号 #636 匹配场景，目标时长: 5.10秒
2025-07-29 20:06:12,690 - INFO - 开始查找字幕序号 [636] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:12,690 - INFO - 找到related_overlap场景: scene_id=679, 字幕#636
2025-07-29 20:06:12,691 - INFO - 字幕 #636 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:12,691 - INFO - 字幕序号 #636 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:12,691 - ERROR - 字幕序号 #636 没有找到任何可用的匹配场景
2025-07-29 20:06:12,691 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:12,691 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:12,691 - INFO - ========== 当前模式：为字幕 #39 生成 1 套场景方案 ==========
2025-07-29 20:06:12,691 - INFO - 开始查找字幕序号 [631, 636] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:12,691 - INFO - 找到related_overlap场景: scene_id=679, 字幕#631
2025-07-29 20:06:12,693 - INFO - 找到related_between场景: scene_id=678, 字幕#631
2025-07-29 20:06:12,693 - INFO - 字幕 #631 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:12,693 - INFO - 字幕 #636 找到 0 个overlap场景, 0 个between场景
2025-07-29 20:06:12,693 - WARNING - 字幕 #636 没有找到任何匹配场景!
2025-07-29 20:06:12,693 - INFO - 共收集 1 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:06:12,693 - INFO - 开始生成方案 #1
2025-07-29 20:06:12,693 - INFO - 方案 #1: 为字幕#631选择初始化overlap场景id=679
2025-07-29 20:06:12,693 - INFO - 方案 #1: 初始选择后，当前总时长=10.64秒
2025-07-29 20:06:12,693 - INFO - 方案 #1: 额外between选择后，当前总时长=10.64秒
2025-07-29 20:06:12,693 - INFO - 方案 #1: 场景总时长(10.64秒)大于音频时长(5.10秒)，需要裁剪
2025-07-29 20:06:12,693 - INFO - 调整前总时长: 10.64秒, 目标时长: 5.10秒
2025-07-29 20:06:12,693 - INFO - 需要裁剪 5.54秒
2025-07-29 20:06:12,693 - INFO - 裁剪最长场景ID=679：从10.64秒裁剪至5.10秒
2025-07-29 20:06:12,693 - INFO - 调整后总时长: 5.10秒，与目标时长差异: 0.00秒
2025-07-29 20:06:12,693 - INFO - 方案 #1 调整/填充后最终总时长: 5.10秒
2025-07-29 20:06:12,693 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:12,693 - INFO - ========== 当前模式：字幕 #39 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:12,693 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:12,693 - INFO - ========== 新模式：字幕 #39 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:12,693 - INFO - 
----- 处理字幕 #39 的方案 #1 -----
2025-07-29 20:06:12,693 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 20:06:12,694 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_wydpc24
2025-07-29 20:06:12,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\679.mp4 (确认存在: True)
2025-07-29 20:06:12,694 - INFO - 添加场景ID=679，时长=10.64秒，累计时长=10.64秒
2025-07-29 20:06:12,694 - INFO - 场景总时长(10.64秒)已达到音频时长(5.10秒)的1.5倍，停止添加场景
2025-07-29 20:06:12,695 - INFO - 准备合并 1 个场景文件，总时长约 10.64秒
2025-07-29 20:06:12,695 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/679.mp4'

2025-07-29 20:06:12,695 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_wydpc24\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_wydpc24\temp_combined.mp4
2025-07-29 20:06:12,820 - INFO - 合并后的视频时长: 10.66秒，目标音频时长: 5.10秒
2025-07-29 20:06:12,820 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_wydpc24\temp_combined.mp4 -ss 0 -to 5.102 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 20:06:13,107 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:13,107 - INFO - 目标音频时长: 5.10秒
2025-07-29 20:06:13,107 - INFO - 实际视频时长: 5.14秒
2025-07-29 20:06:13,107 - INFO - 时长差异: 0.04秒 (0.80%)
2025-07-29 20:06:13,107 - INFO - ==========================================
2025-07-29 20:06:13,107 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:13,107 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 20:06:13,109 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_wydpc24
2025-07-29 20:06:13,156 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:13,156 - INFO -   - 音频时长: 5.10秒
2025-07-29 20:06:13,156 - INFO -   - 视频时长: 5.14秒
2025-07-29 20:06:13,156 - INFO -   - 时长差异: 0.04秒 (0.80%)
2025-07-29 20:06:13,156 - INFO - 
----- 处理字幕 #39 的方案 #2 -----
2025-07-29 20:06:13,156 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 20:06:13,156 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph1j512fg
2025-07-29 20:06:13,156 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\679.mp4 (确认存在: True)
2025-07-29 20:06:13,156 - INFO - 添加场景ID=679，时长=10.64秒，累计时长=10.64秒
2025-07-29 20:06:13,156 - INFO - 场景总时长(10.64秒)已达到音频时长(5.10秒)的1.5倍，停止添加场景
2025-07-29 20:06:13,156 - INFO - 准备合并 1 个场景文件，总时长约 10.64秒
2025-07-29 20:06:13,156 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/679.mp4'

2025-07-29 20:06:13,156 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmph1j512fg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmph1j512fg\temp_combined.mp4
2025-07-29 20:06:13,274 - INFO - 合并后的视频时长: 10.66秒，目标音频时长: 5.10秒
2025-07-29 20:06:13,274 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmph1j512fg\temp_combined.mp4 -ss 0 -to 5.102 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 20:06:13,580 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:13,580 - INFO - 目标音频时长: 5.10秒
2025-07-29 20:06:13,580 - INFO - 实际视频时长: 5.14秒
2025-07-29 20:06:13,580 - INFO - 时长差异: 0.04秒 (0.80%)
2025-07-29 20:06:13,580 - INFO - ==========================================
2025-07-29 20:06:13,580 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:13,580 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 20:06:13,581 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph1j512fg
2025-07-29 20:06:13,626 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:13,626 - INFO -   - 音频时长: 5.10秒
2025-07-29 20:06:13,626 - INFO -   - 视频时长: 5.14秒
2025-07-29 20:06:13,626 - INFO -   - 时长差异: 0.04秒 (0.80%)
2025-07-29 20:06:13,626 - INFO - 
字幕 #39 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:13,626 - INFO - 生成的视频文件:
2025-07-29 20:06:13,626 - INFO -   1. F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 20:06:13,626 - INFO -   2. F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 20:06:13,626 - INFO - ========== 字幕 #39 处理结束 ==========

