2025-07-29 20:06:42,395 - INFO - ========== 字幕 #60 处理开始 ==========
2025-07-29 20:06:42,395 - INFO - 字幕内容: 男人面对质疑，只是淡淡地说，应该说，是威尔集团跟他像才对。
2025-07-29 20:06:42,395 - INFO - 字幕序号: [2382, 2389]
2025-07-29 20:06:42,396 - INFO - 音频文件详情:
2025-07-29 20:06:42,396 - INFO -   - 路径: output\60.wav
2025-07-29 20:06:42,396 - INFO -   - 时长: 3.45秒
2025-07-29 20:06:42,396 - INFO -   - 验证音频时长: 3.45秒
2025-07-29 20:06:42,396 - INFO - 字幕时间戳信息:
2025-07-29 20:06:42,396 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:42,396 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:42,396 - INFO -   - 根据生成的音频时长(3.45秒)已调整字幕时间戳
2025-07-29 20:06:42,396 - INFO - ========== 新模式：为字幕 #60 生成4套场景方案 ==========
2025-07-29 20:06:42,396 - INFO - 字幕序号列表: [2382, 2389]
2025-07-29 20:06:42,396 - INFO - 
--- 生成方案 #1：基于字幕序号 #2382 ---
2025-07-29 20:06:42,396 - INFO - 开始为单个字幕序号 #2382 匹配场景，目标时长: 3.45秒
2025-07-29 20:06:42,396 - INFO - 开始查找字幕序号 [2382] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:42,397 - INFO - 找到related_overlap场景: scene_id=2299, 字幕#2382
2025-07-29 20:06:42,398 - INFO - 字幕 #2382 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:42,398 - INFO - 字幕序号 #2382 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:42,398 - INFO - 选择第一个overlap场景作为起点: scene_id=2299
2025-07-29 20:06:42,399 - INFO - 添加起点场景: scene_id=2299, 时长=1.00秒, 累计时长=1.00秒
2025-07-29 20:06:42,399 - INFO - 起点场景时长不足，需要延伸填充 2.45秒
2025-07-29 20:06:42,399 - INFO - 起点场景在原始列表中的索引: 2298
2025-07-29 20:06:42,399 - INFO - 延伸添加场景: scene_id=2300 (完整时长 1.96秒)
2025-07-29 20:06:42,399 - INFO - 累计时长: 2.96秒
2025-07-29 20:06:42,399 - INFO - 延伸添加场景: scene_id=2301 (裁剪至 0.49秒)
2025-07-29 20:06:42,399 - INFO - 累计时长: 3.45秒
2025-07-29 20:06:42,399 - INFO - 字幕序号 #2382 场景匹配完成，共选择 3 个场景，总时长: 3.45秒
2025-07-29 20:06:42,399 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:42,399 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:42,399 - INFO - 
--- 生成方案 #2：基于字幕序号 #2389 ---
2025-07-29 20:06:42,399 - INFO - 开始为单个字幕序号 #2389 匹配场景，目标时长: 3.45秒
2025-07-29 20:06:42,399 - INFO - 开始查找字幕序号 [2389] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:42,399 - INFO - 找到related_overlap场景: scene_id=2302, 字幕#2389
2025-07-29 20:06:42,400 - INFO - 字幕 #2389 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:42,400 - INFO - 字幕序号 #2389 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:42,400 - INFO - 选择第一个overlap场景作为起点: scene_id=2302
2025-07-29 20:06:42,400 - INFO - 添加起点场景: scene_id=2302, 时长=3.68秒, 累计时长=3.68秒
2025-07-29 20:06:42,400 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:42,400 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 20:06:42,400 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:42,400 - INFO - ========== 当前模式：为字幕 #60 生成 1 套场景方案 ==========
2025-07-29 20:06:42,400 - INFO - 开始查找字幕序号 [2382, 2389] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:42,401 - INFO - 找到related_overlap场景: scene_id=2299, 字幕#2382
2025-07-29 20:06:42,401 - INFO - 找到related_overlap场景: scene_id=2302, 字幕#2389
2025-07-29 20:06:42,401 - INFO - 字幕 #2382 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:42,402 - INFO - 字幕 #2389 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:42,402 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:42,402 - INFO - 开始生成方案 #1
2025-07-29 20:06:42,402 - INFO - 方案 #1: 为字幕#2382选择初始化overlap场景id=2299
2025-07-29 20:06:42,402 - INFO - 方案 #1: 为字幕#2389选择初始化overlap场景id=2302
2025-07-29 20:06:42,402 - INFO - 方案 #1: 初始选择后，当前总时长=4.68秒
2025-07-29 20:06:42,402 - INFO - 方案 #1: 额外between选择后，当前总时长=4.68秒
2025-07-29 20:06:42,402 - INFO - 方案 #1: 场景总时长(4.68秒)大于音频时长(3.45秒)，需要裁剪
2025-07-29 20:06:42,402 - INFO - 调整前总时长: 4.68秒, 目标时长: 3.45秒
2025-07-29 20:06:42,402 - INFO - 需要裁剪 1.23秒
2025-07-29 20:06:42,402 - INFO - 裁剪最长场景ID=2302：从3.68秒裁剪至2.45秒
2025-07-29 20:06:42,402 - INFO - 调整后总时长: 3.45秒，与目标时长差异: 0.00秒
2025-07-29 20:06:42,402 - INFO - 方案 #1 调整/填充后最终总时长: 3.45秒
2025-07-29 20:06:42,402 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:42,402 - INFO - ========== 当前模式：字幕 #60 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:42,402 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:42,402 - INFO - ========== 新模式：字幕 #60 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:42,402 - INFO - 
----- 处理字幕 #60 的方案 #1 -----
2025-07-29 20:06:42,402 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 20:06:42,402 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy88wc4_b
2025-07-29 20:06:42,403 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2299.mp4 (确认存在: True)
2025-07-29 20:06:42,403 - INFO - 添加场景ID=2299，时长=1.00秒，累计时长=1.00秒
2025-07-29 20:06:42,403 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2300.mp4 (确认存在: True)
2025-07-29 20:06:42,403 - INFO - 添加场景ID=2300，时长=1.96秒，累计时长=2.96秒
2025-07-29 20:06:42,403 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2301.mp4 (确认存在: True)
2025-07-29 20:06:42,403 - INFO - 添加场景ID=2301，时长=2.56秒，累计时长=5.52秒
2025-07-29 20:06:42,403 - INFO - 场景总时长(5.52秒)已达到音频时长(3.45秒)的1.5倍，停止添加场景
2025-07-29 20:06:42,403 - INFO - 准备合并 3 个场景文件，总时长约 5.52秒
2025-07-29 20:06:42,404 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2299.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2300.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2301.mp4'

2025-07-29 20:06:42,404 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpy88wc4_b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpy88wc4_b\temp_combined.mp4
2025-07-29 20:06:42,549 - INFO - 合并后的视频时长: 5.59秒，目标音频时长: 3.45秒
2025-07-29 20:06:42,549 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpy88wc4_b\temp_combined.mp4 -ss 0 -to 3.45 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 20:06:42,808 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:42,808 - INFO - 目标音频时长: 3.45秒
2025-07-29 20:06:42,808 - INFO - 实际视频时长: 3.50秒
2025-07-29 20:06:42,808 - INFO - 时长差异: 0.05秒 (1.54%)
2025-07-29 20:06:42,808 - INFO - ==========================================
2025-07-29 20:06:42,808 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:42,808 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 20:06:42,809 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy88wc4_b
2025-07-29 20:06:42,855 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:42,855 - INFO -   - 音频时长: 3.45秒
2025-07-29 20:06:42,855 - INFO -   - 视频时长: 3.50秒
2025-07-29 20:06:42,855 - INFO -   - 时长差异: 0.05秒 (1.54%)
2025-07-29 20:06:42,855 - INFO - 
----- 处理字幕 #60 的方案 #2 -----
2025-07-29 20:06:42,855 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 20:06:42,856 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptu8ue28o
2025-07-29 20:06:42,856 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2302.mp4 (确认存在: True)
2025-07-29 20:06:42,856 - INFO - 添加场景ID=2302，时长=3.68秒，累计时长=3.68秒
2025-07-29 20:06:42,856 - INFO - 准备合并 1 个场景文件，总时长约 3.68秒
2025-07-29 20:06:42,856 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2302.mp4'

2025-07-29 20:06:42,856 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptu8ue28o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptu8ue28o\temp_combined.mp4
2025-07-29 20:06:42,967 - INFO - 合并后的视频时长: 3.70秒，目标音频时长: 3.45秒
2025-07-29 20:06:42,967 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptu8ue28o\temp_combined.mp4 -ss 0 -to 3.45 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 20:06:43,225 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:43,225 - INFO - 目标音频时长: 3.45秒
2025-07-29 20:06:43,225 - INFO - 实际视频时长: 3.50秒
2025-07-29 20:06:43,225 - INFO - 时长差异: 0.05秒 (1.54%)
2025-07-29 20:06:43,225 - INFO - ==========================================
2025-07-29 20:06:43,225 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:43,225 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 20:06:43,225 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptu8ue28o
2025-07-29 20:06:43,269 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:43,269 - INFO -   - 音频时长: 3.45秒
2025-07-29 20:06:43,269 - INFO -   - 视频时长: 3.50秒
2025-07-29 20:06:43,269 - INFO -   - 时长差异: 0.05秒 (1.54%)
2025-07-29 20:06:43,269 - INFO - 
----- 处理字幕 #60 的方案 #3 -----
2025-07-29 20:06:43,269 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 20:06:43,270 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ofy9xpn
2025-07-29 20:06:43,270 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2299.mp4 (确认存在: True)
2025-07-29 20:06:43,270 - INFO - 添加场景ID=2299，时长=1.00秒，累计时长=1.00秒
2025-07-29 20:06:43,270 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2302.mp4 (确认存在: True)
2025-07-29 20:06:43,270 - INFO - 添加场景ID=2302，时长=3.68秒，累计时长=4.68秒
2025-07-29 20:06:43,270 - INFO - 准备合并 2 个场景文件，总时长约 4.68秒
2025-07-29 20:06:43,271 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2299.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2302.mp4'

2025-07-29 20:06:43,271 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8ofy9xpn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8ofy9xpn\temp_combined.mp4
2025-07-29 20:06:43,401 - INFO - 合并后的视频时长: 4.73秒，目标音频时长: 3.45秒
2025-07-29 20:06:43,401 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8ofy9xpn\temp_combined.mp4 -ss 0 -to 3.45 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 20:06:43,676 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:43,677 - INFO - 目标音频时长: 3.45秒
2025-07-29 20:06:43,677 - INFO - 实际视频时长: 3.50秒
2025-07-29 20:06:43,677 - INFO - 时长差异: 0.05秒 (1.54%)
2025-07-29 20:06:43,677 - INFO - ==========================================
2025-07-29 20:06:43,677 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:43,677 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 20:06:43,677 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ofy9xpn
2025-07-29 20:06:43,722 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:43,722 - INFO -   - 音频时长: 3.45秒
2025-07-29 20:06:43,722 - INFO -   - 视频时长: 3.50秒
2025-07-29 20:06:43,722 - INFO -   - 时长差异: 0.05秒 (1.54%)
2025-07-29 20:06:43,722 - INFO - 
字幕 #60 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:43,722 - INFO - 生成的视频文件:
2025-07-29 20:06:43,722 - INFO -   1. F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 20:06:43,722 - INFO -   2. F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 20:06:43,722 - INFO -   3. F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 20:06:43,722 - INFO - ========== 字幕 #60 处理结束 ==========

