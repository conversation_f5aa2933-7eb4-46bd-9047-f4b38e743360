2025-07-29 20:05:30,455 - INFO - ========== 字幕 #11 处理开始 ==========
2025-07-29 20:05:30,455 - INFO - 字幕内容: 男人绝望地解释，安安真的在手术室里等着钱，但他句句属实的话语，在女人听来都成了谎言。
2025-07-29 20:05:30,455 - INFO - 字幕序号: [172, 176]
2025-07-29 20:05:30,456 - INFO - 音频文件详情:
2025-07-29 20:05:30,456 - INFO -   - 路径: output\11.wav
2025-07-29 20:05:30,456 - INFO -   - 时长: 6.10秒
2025-07-29 20:05:30,456 - INFO -   - 验证音频时长: 6.10秒
2025-07-29 20:05:30,456 - INFO - 字幕时间戳信息:
2025-07-29 20:05:30,465 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:30,465 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:30,465 - INFO -   - 根据生成的音频时长(6.10秒)已调整字幕时间戳
2025-07-29 20:05:30,465 - INFO - ========== 新模式：为字幕 #11 生成4套场景方案 ==========
2025-07-29 20:05:30,465 - INFO - 字幕序号列表: [172, 176]
2025-07-29 20:05:30,465 - INFO - 
--- 生成方案 #1：基于字幕序号 #172 ---
2025-07-29 20:05:30,465 - INFO - 开始为单个字幕序号 #172 匹配场景，目标时长: 6.10秒
2025-07-29 20:05:30,465 - INFO - 开始查找字幕序号 [172] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:30,465 - INFO - 找到related_overlap场景: scene_id=191, 字幕#172
2025-07-29 20:05:30,465 - INFO - 找到related_overlap场景: scene_id=192, 字幕#172
2025-07-29 20:05:30,467 - INFO - 字幕 #172 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:30,467 - INFO - 字幕序号 #172 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:30,467 - INFO - 选择第一个overlap场景作为起点: scene_id=191
2025-07-29 20:05:30,467 - INFO - 添加起点场景: scene_id=191, 时长=1.96秒, 累计时长=1.96秒
2025-07-29 20:05:30,467 - INFO - 起点场景时长不足，需要延伸填充 4.14秒
2025-07-29 20:05:30,467 - INFO - 起点场景在原始列表中的索引: 190
2025-07-29 20:05:30,467 - INFO - 延伸添加场景: scene_id=192 (完整时长 1.28秒)
2025-07-29 20:05:30,467 - INFO - 累计时长: 3.24秒
2025-07-29 20:05:30,467 - INFO - 延伸添加场景: scene_id=193 (完整时长 1.44秒)
2025-07-29 20:05:30,467 - INFO - 累计时长: 4.68秒
2025-07-29 20:05:30,467 - INFO - 延伸添加场景: scene_id=194 (完整时长 1.20秒)
2025-07-29 20:05:30,467 - INFO - 累计时长: 5.88秒
2025-07-29 20:05:30,467 - INFO - 延伸添加场景: scene_id=195 (裁剪至 0.23秒)
2025-07-29 20:05:30,467 - INFO - 累计时长: 6.10秒
2025-07-29 20:05:30,467 - INFO - 字幕序号 #172 场景匹配完成，共选择 5 个场景，总时长: 6.10秒
2025-07-29 20:05:30,467 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:30,467 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:30,467 - INFO - 
--- 生成方案 #2：基于字幕序号 #176 ---
2025-07-29 20:05:30,467 - INFO - 开始为单个字幕序号 #176 匹配场景，目标时长: 6.10秒
2025-07-29 20:05:30,467 - INFO - 开始查找字幕序号 [176] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:30,467 - INFO - 找到related_overlap场景: scene_id=193, 字幕#176
2025-07-29 20:05:30,468 - INFO - 找到related_between场景: scene_id=195, 字幕#176
2025-07-29 20:05:30,468 - INFO - 字幕 #176 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:30,468 - INFO - 字幕序号 #176 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:30,468 - ERROR - 字幕序号 #176 没有找到任何可用的匹配场景
2025-07-29 20:05:30,468 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:05:30,468 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:05:30,468 - INFO - ========== 当前模式：为字幕 #11 生成 1 套场景方案 ==========
2025-07-29 20:05:30,468 - INFO - 开始查找字幕序号 [172, 176] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:30,468 - INFO - 找到related_overlap场景: scene_id=191, 字幕#172
2025-07-29 20:05:30,468 - INFO - 找到related_overlap场景: scene_id=192, 字幕#172
2025-07-29 20:05:30,468 - INFO - 找到related_overlap场景: scene_id=193, 字幕#176
2025-07-29 20:05:30,470 - INFO - 找到related_between场景: scene_id=195, 字幕#176
2025-07-29 20:05:30,470 - INFO - 字幕 #172 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:30,470 - INFO - 字幕 #176 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:30,470 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:05:30,470 - INFO - 开始生成方案 #1
2025-07-29 20:05:30,470 - INFO - 方案 #1: 为字幕#172选择初始化overlap场景id=191
2025-07-29 20:05:30,470 - INFO - 方案 #1: 为字幕#176选择初始化overlap场景id=193
2025-07-29 20:05:30,470 - INFO - 方案 #1: 初始选择后，当前总时长=3.40秒
2025-07-29 20:05:30,470 - INFO - 方案 #1: 额外添加overlap场景id=192, 当前总时长=4.68秒
2025-07-29 20:05:30,470 - INFO - 方案 #1: 额外between选择后，当前总时长=4.68秒
2025-07-29 20:05:30,470 - INFO - 方案 #1: 额外添加between场景id=195, 当前总时长=5.20秒
2025-07-29 20:05:30,471 - INFO - 方案 #1: 场景总时长(5.20秒)小于音频时长(6.10秒)，需要延伸填充
2025-07-29 20:05:30,471 - INFO - 方案 #1: 最后一个场景ID: 195
2025-07-29 20:05:30,471 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 194
2025-07-29 20:05:30,471 - INFO - 方案 #1: 需要填充时长: 0.91秒
2025-07-29 20:05:30,471 - INFO - 方案 #1: 追加场景 scene_id=196 (裁剪至 0.91秒)
2025-07-29 20:05:30,471 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:30,471 - INFO - 方案 #1 调整/填充后最终总时长: 6.10秒
2025-07-29 20:05:30,471 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:30,471 - INFO - ========== 当前模式：字幕 #11 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:30,471 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:05:30,471 - INFO - ========== 新模式：字幕 #11 共生成 2 套有效场景方案 ==========
2025-07-29 20:05:30,471 - INFO - 
----- 处理字幕 #11 的方案 #1 -----
2025-07-29 20:05:30,471 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 20:05:30,471 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp88defl72
2025-07-29 20:05:30,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\191.mp4 (确认存在: True)
2025-07-29 20:05:30,472 - INFO - 添加场景ID=191，时长=1.96秒，累计时长=1.96秒
2025-07-29 20:05:30,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\192.mp4 (确认存在: True)
2025-07-29 20:05:30,472 - INFO - 添加场景ID=192，时长=1.28秒，累计时长=3.24秒
2025-07-29 20:05:30,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\193.mp4 (确认存在: True)
2025-07-29 20:05:30,472 - INFO - 添加场景ID=193，时长=1.44秒，累计时长=4.68秒
2025-07-29 20:05:30,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\194.mp4 (确认存在: True)
2025-07-29 20:05:30,472 - INFO - 添加场景ID=194，时长=1.20秒，累计时长=5.88秒
2025-07-29 20:05:30,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\195.mp4 (确认存在: True)
2025-07-29 20:05:30,472 - INFO - 添加场景ID=195，时长=0.52秒，累计时长=6.40秒
2025-07-29 20:05:30,472 - INFO - 准备合并 5 个场景文件，总时长约 6.40秒
2025-07-29 20:05:30,472 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/191.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/192.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/193.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/194.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/195.mp4'

2025-07-29 20:05:30,472 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp88defl72\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp88defl72\temp_combined.mp4
2025-07-29 20:05:30,631 - INFO - 合并后的视频时长: 6.52秒，目标音频时长: 6.10秒
2025-07-29 20:05:30,631 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp88defl72\temp_combined.mp4 -ss 0 -to 6.102 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 20:05:30,988 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:30,988 - INFO - 目标音频时长: 6.10秒
2025-07-29 20:05:30,988 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:05:30,989 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:30,989 - INFO - ==========================================
2025-07-29 20:05:30,989 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:30,989 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 20:05:30,989 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp88defl72
2025-07-29 20:05:31,032 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:31,032 - INFO -   - 音频时长: 6.10秒
2025-07-29 20:05:31,032 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:05:31,032 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:31,033 - INFO - 
----- 处理字幕 #11 的方案 #2 -----
2025-07-29 20:05:31,033 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 20:05:31,033 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpad04uchy
2025-07-29 20:05:31,033 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\191.mp4 (确认存在: True)
2025-07-29 20:05:31,034 - INFO - 添加场景ID=191，时长=1.96秒，累计时长=1.96秒
2025-07-29 20:05:31,034 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\193.mp4 (确认存在: True)
2025-07-29 20:05:31,034 - INFO - 添加场景ID=193，时长=1.44秒，累计时长=3.40秒
2025-07-29 20:05:31,034 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\192.mp4 (确认存在: True)
2025-07-29 20:05:31,034 - INFO - 添加场景ID=192，时长=1.28秒，累计时长=4.68秒
2025-07-29 20:05:31,034 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\195.mp4 (确认存在: True)
2025-07-29 20:05:31,034 - INFO - 添加场景ID=195，时长=0.52秒，累计时长=5.20秒
2025-07-29 20:05:31,034 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\196.mp4 (确认存在: True)
2025-07-29 20:05:31,034 - INFO - 添加场景ID=196，时长=1.56秒，累计时长=6.76秒
2025-07-29 20:05:31,034 - INFO - 准备合并 5 个场景文件，总时长约 6.76秒
2025-07-29 20:05:31,034 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/191.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/193.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/192.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/195.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/196.mp4'

2025-07-29 20:05:31,034 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpad04uchy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpad04uchy\temp_combined.mp4
2025-07-29 20:05:31,189 - INFO - 合并后的视频时长: 6.88秒，目标音频时长: 6.10秒
2025-07-29 20:05:31,189 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpad04uchy\temp_combined.mp4 -ss 0 -to 6.102 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 20:05:31,564 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:31,564 - INFO - 目标音频时长: 6.10秒
2025-07-29 20:05:31,564 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:05:31,564 - INFO - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:31,564 - INFO - ==========================================
2025-07-29 20:05:31,564 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:31,564 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 20:05:31,565 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpad04uchy
2025-07-29 20:05:31,611 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:31,611 - INFO -   - 音频时长: 6.10秒
2025-07-29 20:05:31,611 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:05:31,611 - INFO -   - 时长差异: 0.04秒 (0.67%)
2025-07-29 20:05:31,611 - INFO - 
字幕 #11 处理完成，成功生成 2/2 套方案
2025-07-29 20:05:31,611 - INFO - 生成的视频文件:
2025-07-29 20:05:31,611 - INFO -   1. F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 20:05:31,611 - INFO -   2. F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 20:05:31,611 - INFO - ========== 字幕 #11 处理结束 ==========

