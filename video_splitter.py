#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频分割程序
根据SRT字幕文件的时间戳分割视频，确保每个片段不超过1分钟且保持字幕完整性
"""

import os
import re
import subprocess
import sys
import shutil
import glob
from datetime import datetime, timedelta
from typing import List, Tuple, Dict


class SubtitleEntry:
    """字幕条目类"""
    def __init__(self, index: int, start_time: float, end_time: float, text: str):
        self.index = index
        self.start_time = start_time  # 秒
        self.end_time = end_time      # 秒
        self.text = text.strip()

    def __repr__(self):
        return f"SubtitleEntry({self.index}, {self.start_time:.3f}s-{self.end_time:.3f}s, '{self.text[:30]}...')"


def parse_srt_time(time_str: str) -> float:
    """
    解析SRT时间格式 (HH:MM:SS,mmm) 转换为秒数

    Args:
        time_str: SRT时间字符串，格式如 "00:01:23,456"

    Returns:
        float: 对应的秒数
    """
    # 替换逗号为点号以便解析毫秒
    time_str = time_str.replace(',', '.')

    # 解析时间格式 HH:MM:SS.mmm
    time_parts = time_str.split(':')
    hours = int(time_parts[0])
    minutes = int(time_parts[1])
    seconds_and_ms = float(time_parts[2])

    total_seconds = hours * 3600 + minutes * 60 + seconds_and_ms
    return total_seconds


def parse_srt_file(srt_path: str) -> List[SubtitleEntry]:
    """
    解析SRT字幕文件

    Args:
        srt_path: SRT文件路径

    Returns:
        List[SubtitleEntry]: 字幕条目列表
    """
    if not os.path.exists(srt_path):
        raise FileNotFoundError(f"SRT文件不存在: {srt_path}")

    subtitles = []

    with open(srt_path, 'r', encoding='utf-8') as file:
        content = file.read().strip()

    # 按空行分割字幕块
    subtitle_blocks = re.split(r'\n\s*\n', content)

    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue

        try:
            # 第一行是序号
            index = int(lines[0])

            # 第二行是时间范围
            time_line = lines[1]
            time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
            if not time_match:
                print(f"警告: 无法解析时间行: {time_line}")
                continue

            start_time = parse_srt_time(time_match.group(1))
            end_time = parse_srt_time(time_match.group(2))

            # 剩余行是字幕文本
            text = '\n'.join(lines[2:])

            subtitle = SubtitleEntry(index, start_time, end_time, text)
            subtitles.append(subtitle)

        except (ValueError, IndexError) as e:
            print(f"警告: 解析字幕块失败: {block[:50]}... 错误: {e}")
            continue

    print(f"成功解析 {len(subtitles)} 条字幕")
    return subtitles


def calculate_split_points(subtitles: List[SubtitleEntry], max_duration: float = 60.0, min_remaining: float = 30.0) -> List[Tuple[float, float]]:
    """
    计算视频分割点

    Args:
        subtitles: 字幕列表
        max_duration: 每段视频最大时长（秒）
        min_remaining: 最小剩余时长（秒），如果剩余时长小于此值则合并到上一段

    Returns:
        List[Tuple[float, float]]: 分割时间段列表 [(start1, end1), (start2, end2), ...]
    """
    if not subtitles:
        return []

    segments = []
    segment_start = subtitles[0].start_time  # 从第一个字幕开始

    i = 0
    while i < len(subtitles):
        subtitle = subtitles[i]

        # 计算如果包含当前字幕，这一段的总时长
        potential_duration = subtitle.end_time - segment_start

        # 检查是否需要分割
        if potential_duration > max_duration:
            # 超过最大时长，在上一个字幕结束处分割
            if i > 0:
                prev_subtitle = subtitles[i-1]
                segment_end = prev_subtitle.end_time

                # 检查剩余部分是否太短
                remaining_duration = subtitles[-1].end_time - segment_end

                if remaining_duration < min_remaining:
                    # 剩余部分太短，继续添加字幕直到结束
                    segments.append((segment_start, subtitles[-1].end_time))
                    i = len(subtitles)  # 标记所有字幕已处理
                    break
                else:
                    # 正常分割
                    segments.append((segment_start, segment_end))
                    segment_start = segment_end  # 下一段从这里开始
                    print(f"分割点: 字幕{prev_subtitle.index}结束时间 {format_time_for_ffmpeg(segment_end)}")
                    continue  # 不增加i，重新检查当前字幕
            else:
                # 第一个字幕就超过时长，强制分割
                segments.append((segment_start, subtitle.end_time))
                segment_start = subtitle.end_time
                i += 1
        else:
            # 不超过最大时长，继续添加
            i += 1

    # 添加最后一段（如果还有未处理的字幕）
    # 修复：当循环正常结束时，需要检查是否还有未添加的最后一段
    if len(segments) == 0 or (len(segments) > 0 and segments[-1][1] < subtitles[-1].end_time):
        # 如果没有任何段，或者最后一段的结束时间小于视频总时长，则添加最后一段
        if len(segments) > 0:
            # 从上一段结束的地方开始
            last_segment_end = segments[-1][1]
            if last_segment_end < subtitles[-1].end_time:
                segments.append((last_segment_end, subtitles[-1].end_time))
        else:
            # 如果没有任何段（所有字幕都在一段内），添加整个视频作为一段
            segments.append((segment_start, subtitles[-1].end_time))

    return segments


def format_time_for_ffmpeg(seconds: float) -> str:
    """
    将秒数转换为ffmpeg时间格式 (HH:MM:SS.mmm)

    Args:
        seconds: 秒数

    Returns:
        str: ffmpeg时间格式字符串
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60

    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"


def clear_output_directory(output_dir: str):
    """
    清空输出目录中的所有文件

    Args:
        output_dir: 输出目录路径
    """
    if os.path.exists(output_dir):
        try:
            # 获取目录中的所有文件
            files = glob.glob(os.path.join(output_dir, "*"))

            if files:
                print(f"正在清空输出目录: {output_dir}")
                for file_path in files:
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            print(f"  删除文件: {os.path.basename(file_path)}")
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            print(f"  删除目录: {os.path.basename(file_path)}")
                    except Exception as e:
                        print(f"  警告: 无法删除 {os.path.basename(file_path)}: {e}")
                print("目录清空完成")
            else:
                print(f"输出目录已为空: {output_dir}")
        except Exception as e:
            print(f"警告: 清空目录时出错: {e}")
    else:
        print(f"输出目录不存在，将自动创建: {output_dir}")


def split_video(input_video: str, output_dir: str, segments: List[Tuple[float, float]]) -> List[str]:
    """
    使用ffmpeg分割视频

    Args:
        input_video: 输入视频文件路径
        output_dir: 输出目录
        segments: 分割时间段列表

    Returns:
        List[str]: 生成的视频文件路径列表
    """
    if not os.path.exists(input_video):
        raise FileNotFoundError(f"输入视频文件不存在: {input_video}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    output_files = []

    for i, (start_time, end_time) in enumerate(segments, 1):
        output_file = os.path.join(output_dir, f"shorts_part{i}.mp4")

        # 构建ffmpeg命令
        start_str = format_time_for_ffmpeg(start_time)
        duration = end_time - start_time
        duration_str = format_time_for_ffmpeg(duration)

        # 使用更精确的分割方法，避免卡顿
        cmd = [
            'ffmpeg',
            '-ss', start_str,  # 先设置起始时间
            '-i', input_video,  # 再输入文件，提高精度
            '-t', duration_str,
            '-c:v', 'libx264',  # 重新编码视频，确保关键帧
            '-c:a', 'aac',      # 重新编码音频
            '-preset', 'fast',   # 快速编码
            '-crf', '23',        # 质量设置
            '-avoid_negative_ts', 'make_zero',
            '-y',  # 覆盖输出文件
            output_file
        ]

        print(f"正在生成第 {i} 段视频: {start_str} - {format_time_for_ffmpeg(end_time)} (时长: {duration_str})")
        print(f"命令: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✓ 成功生成: {output_file}")
            output_files.append(output_file)
        except subprocess.CalledProcessError as e:
            print(f"✗ 生成失败: {output_file}")
            print(f"错误信息: {e.stderr}")
            raise

    return output_files


def generate_subtitle_instructions(subtitles: List[SubtitleEntry], segments: List[Tuple[float, float]], output_dir: str):
    """
    根据分割时间点生成字幕指令文件

    Args:
        subtitles: 字幕列表
        segments: 分割时间段列表
        output_dir: 输出目录
    """
    instruction_file = os.path.join(output_dir, "长视频标题指令.txt")

    try:
        with open(instruction_file, 'w', encoding='utf-8') as f:
            # 写入详细的指令说明
            f.write("# 核心任务\n")
            f.write("请根据我按部分（part1, part2...）提供的字幕段内容，为每一个部分生成用于YouTube Shorts的标题、描述和标签。\n\n")

            f.write("# 关键要求\n")
            f.write("格式要求：\n")
            f.write("必须使用 Markdown 格式 进行输出，确保最终成品层次清晰、易于阅读。\n")
            f.write("每个部分之间必须使用 Markdown 水平分割线 --- 隔开。\n\n")
            f.write("内容要求：\n")
            f.write("所有内容（标题、描述、标签）都需要提供 中英双语 对照。\n\n")

            f.write("# 具体输出细则\n")
            f.write("请严格按照以下细则为 每一个部分 生成内容：\n\n")

            f.write("1. 标题 (Title):\n")
            f.write("风格： 必须有吸引力、能激发观众的点击欲望。\n")
            f.write("元素： 需包含一个与内容匹配的 Emoji (例如: ✨, 🔥, 😭)。\n")
            f.write("固定标签： 标题末尾必须统一添加 #shorts #drama #movie。\n")
            f.write("字数限制： 非常重要！ 包含 [partX]、中文/英文内容、Emoji 和末尾固定标签在内的总字符数不得超过80个字符。\n\n")

            f.write("2. 描述 (Description):\n")
            f.write("内容： 概括该字幕段的核心剧情、冲突或看点。\n")
            f.write("字数限制： 中英文描述的长度均控制在 200个字符 左右（是字符数，不是单词数）。\n\n")

            f.write("3. 标签 (Tags):\n")
            f.write("内容： 根据剧情提炼 3-5个 精准的关键词。\n")
            f.write("格式： 多个关键词之间用逗号 , 隔开。\n\n")

            f.write("# 输出格式范例（请严格遵守）\n\n")

            # 生成标题、描述、标签模板（只生成前2个part作为示例）
            for part_num in range(1, min(3, len(segments) + 1)):  # 只生成part1和part2
                f.write(f"## part{part_num}\n\n")
                f.write(f"**中文标题：** `[part{part_num}]吸睛的中文标题在此✨#shorts #drama #movie`\n")
                f.write(f"**英文标题：** `[part{part_num}]Catchy English Title Here✨#shorts #drama #movie`\n\n")
                f.write(f"**中文描述：** (这里是大约200字符的中文剧情简介，总结该片段的核心冲突和看点。)\n")
                f.write(f"**英文描述：** (Here is the ~200 character English plot summary, highlighting the core conflict and appeal of the segment.)\n\n")
                f.write(f"**中文标签：** 关键词一, 关键词二, 关键词三\n")
                f.write(f"**英文标签：** Keyword One, Keyword Two, Keyword Three\n\n")
                f.write("---\n\n")

            # 添加省略号表示剩余part
            if len(segments) > 2:
                f.write("## part3\n\n")
                f.write("**中文标题：** `[part3]这是第三个视频的标题🔥#shorts #drama #movie`\n")
                f.write("**英文标题：** `[part3]Title for the Third Video🔥#shorts #drama #movie`\n\n")
                f.write("**中文描述：** (这里是第三个视频的中文简介...)\n")
                f.write("**英文描述：** (Here is the description for the third video...)\n\n")
                f.write("**中文标签：** 关键词一, 关键词二, 关键词三, 关键词四\n")
                f.write("**英文标签：** Keyword One, Keyword Two, Keyword Three, Keyword Four\n\n")
                f.write("---\n\n")

            f.write("\n字幕段内容：\n\n")

            # 生成字幕内容
            for part_num, (start_time, end_time) in enumerate(segments, 1):
                f.write(f"part{part_num}:\n")

                # 找到这个时间段内的所有字幕
                part_subtitles = []
                for subtitle in subtitles:
                    # 字幕的结束时间在这个时间段内，或者字幕跨越了分割点
                    if (subtitle.start_time >= start_time and subtitle.end_time <= end_time) or \
                       (subtitle.start_time < end_time and subtitle.end_time > start_time):
                        part_subtitles.append(subtitle)

                # 按字幕序号排序
                part_subtitles.sort(key=lambda x: x.index)

                # 生成连续的字幕文本
                subtitle_texts = []
                for subtitle in part_subtitles:
                    subtitle_texts.append(f"[{subtitle.index}]{subtitle.text}")

                # 写入连续的字幕内容
                f.write("".join(subtitle_texts))
                f.write("\n\n")

                print(f"part{part_num}: 包含字幕 {part_subtitles[0].index}-{part_subtitles[-1].index} ({len(part_subtitles)}条)")

        print(f"✓ 字幕指令文件已生成: {instruction_file}")

    except Exception as e:
        print(f"✗ 生成字幕指令文件失败: {e}")
        raise


def main():
    """主函数"""
    # 文件路径配置
    srt_file = r"F:\github\aicut_auto\en.srt"
    input_video = r"F:\github\aicut_auto\原视频合并\shorts.mp4"
    output_dir = r"F:\github\aicut_auto\原视频合并\长视频"

    try:
        print("=== 视频分割程序开始 ===")

        # 0. 清空输出目录
        clear_output_directory(output_dir)

        # 1. 解析SRT文件
        print(f"正在解析SRT文件: {srt_file}")
        subtitles = parse_srt_file(srt_file)

        if not subtitles:
            print("错误: 没有找到有效的字幕")
            return

        print(f"视频总时长: {format_time_for_ffmpeg(subtitles[-1].end_time)}")

        # 2. 计算分割点
        print("正在计算分割点...")
        segments = calculate_split_points(subtitles, max_duration=60.0, min_remaining=30.0)

        print(f"计划分割为 {len(segments)} 段:")
        for i, (start, end) in enumerate(segments, 1):
            duration = end - start
            print(f"  第{i}段: {format_time_for_ffmpeg(start)} - {format_time_for_ffmpeg(end)} (时长: {format_time_for_ffmpeg(duration)})")

        # 3. 分割视频
        print("开始分割视频...")
        output_files = split_video(input_video, output_dir, segments)

        # 4. 生成字幕指令文件
        print("\n开始生成字幕指令文件...")
        generate_subtitle_instructions(subtitles, segments, output_dir)

        print("\n=== 分割完成 ===")
        print(f"共生成 {len(output_files)} 个视频文件:")
        for file in output_files:
            print(f"  {file}")
        print(f"字幕指令文件: {os.path.join(output_dir, '长视频标题指令.txt')}")

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())