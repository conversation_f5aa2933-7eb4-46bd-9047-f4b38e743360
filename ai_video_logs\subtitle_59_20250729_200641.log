2025-07-29 20:06:41,468 - INFO - ========== 字幕 #59 处理开始 ==========
2025-07-29 20:06:41,468 - INFO - 字幕内容: 他甚至嚣张地嘲讽男人，难道他想说自己就是那位神秘的An先生吗？
2025-07-29 20:06:41,468 - INFO - 字幕序号: [2368, 2370]
2025-07-29 20:06:41,468 - INFO - 音频文件详情:
2025-07-29 20:06:41,468 - INFO -   - 路径: output\59.wav
2025-07-29 20:06:41,468 - INFO -   - 时长: 3.87秒
2025-07-29 20:06:41,469 - INFO -   - 验证音频时长: 3.87秒
2025-07-29 20:06:41,469 - INFO - 字幕时间戳信息:
2025-07-29 20:06:41,478 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:41,478 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:41,478 - INFO -   - 根据生成的音频时长(3.87秒)已调整字幕时间戳
2025-07-29 20:06:41,478 - INFO - ========== 新模式：为字幕 #59 生成4套场景方案 ==========
2025-07-29 20:06:41,478 - INFO - 字幕序号列表: [2368, 2370]
2025-07-29 20:06:41,478 - INFO - 
--- 生成方案 #1：基于字幕序号 #2368 ---
2025-07-29 20:06:41,478 - INFO - 开始为单个字幕序号 #2368 匹配场景，目标时长: 3.87秒
2025-07-29 20:06:41,478 - INFO - 开始查找字幕序号 [2368] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:41,479 - INFO - 找到related_overlap场景: scene_id=2291, 字幕#2368
2025-07-29 20:06:41,480 - INFO - 字幕 #2368 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:41,480 - INFO - 字幕序号 #2368 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:41,480 - INFO - 选择第一个overlap场景作为起点: scene_id=2291
2025-07-29 20:06:41,480 - INFO - 添加起点场景: scene_id=2291, 时长=1.48秒, 累计时长=1.48秒
2025-07-29 20:06:41,480 - INFO - 起点场景时长不足，需要延伸填充 2.39秒
2025-07-29 20:06:41,480 - INFO - 起点场景在原始列表中的索引: 2290
2025-07-29 20:06:41,480 - INFO - 延伸添加场景: scene_id=2292 (完整时长 2.24秒)
2025-07-29 20:06:41,480 - INFO - 累计时长: 3.72秒
2025-07-29 20:06:41,480 - INFO - 延伸添加场景: scene_id=2293 (裁剪至 0.15秒)
2025-07-29 20:06:41,480 - INFO - 累计时长: 3.87秒
2025-07-29 20:06:41,480 - INFO - 字幕序号 #2368 场景匹配完成，共选择 3 个场景，总时长: 3.87秒
2025-07-29 20:06:41,480 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:41,480 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:41,480 - INFO - 
--- 生成方案 #2：基于字幕序号 #2370 ---
2025-07-29 20:06:41,480 - INFO - 开始为单个字幕序号 #2370 匹配场景，目标时长: 3.87秒
2025-07-29 20:06:41,480 - INFO - 开始查找字幕序号 [2370] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:41,481 - INFO - 找到related_overlap场景: scene_id=2292, 字幕#2370
2025-07-29 20:06:41,481 - INFO - 字幕 #2370 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:41,481 - INFO - 字幕序号 #2370 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:41,481 - ERROR - 字幕序号 #2370 没有找到任何可用的匹配场景
2025-07-29 20:06:41,481 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:41,481 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:41,481 - INFO - ========== 当前模式：为字幕 #59 生成 1 套场景方案 ==========
2025-07-29 20:06:41,481 - INFO - 开始查找字幕序号 [2368, 2370] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:41,482 - INFO - 找到related_overlap场景: scene_id=2291, 字幕#2368
2025-07-29 20:06:41,482 - INFO - 找到related_overlap场景: scene_id=2292, 字幕#2370
2025-07-29 20:06:41,483 - INFO - 字幕 #2368 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:41,483 - INFO - 字幕 #2370 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:41,483 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:41,483 - INFO - 开始生成方案 #1
2025-07-29 20:06:41,483 - INFO - 方案 #1: 为字幕#2368选择初始化overlap场景id=2291
2025-07-29 20:06:41,483 - INFO - 方案 #1: 为字幕#2370选择初始化overlap场景id=2292
2025-07-29 20:06:41,483 - INFO - 方案 #1: 初始选择后，当前总时长=3.72秒
2025-07-29 20:06:41,483 - INFO - 方案 #1: 额外between选择后，当前总时长=3.72秒
2025-07-29 20:06:41,483 - INFO - 方案 #1: 场景总时长(3.72秒)小于音频时长(3.87秒)，需要延伸填充
2025-07-29 20:06:41,483 - INFO - 方案 #1: 最后一个场景ID: 2292
2025-07-29 20:06:41,483 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2291
2025-07-29 20:06:41,483 - INFO - 方案 #1: 需要填充时长: 0.15秒
2025-07-29 20:06:41,483 - INFO - 方案 #1: 追加场景 scene_id=2293 (裁剪至 0.15秒)
2025-07-29 20:06:41,483 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:41,483 - INFO - 方案 #1 调整/填充后最终总时长: 3.87秒
2025-07-29 20:06:41,483 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:41,483 - INFO - ========== 当前模式：字幕 #59 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:41,483 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:41,483 - INFO - ========== 新模式：字幕 #59 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:41,483 - INFO - 
----- 处理字幕 #59 的方案 #1 -----
2025-07-29 20:06:41,483 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 20:06:41,483 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkg3chde3
2025-07-29 20:06:41,484 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2291.mp4 (确认存在: True)
2025-07-29 20:06:41,484 - INFO - 添加场景ID=2291，时长=1.48秒，累计时长=1.48秒
2025-07-29 20:06:41,484 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2292.mp4 (确认存在: True)
2025-07-29 20:06:41,484 - INFO - 添加场景ID=2292，时长=2.24秒，累计时长=3.72秒
2025-07-29 20:06:41,484 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2293.mp4 (确认存在: True)
2025-07-29 20:06:41,484 - INFO - 添加场景ID=2293，时长=1.40秒，累计时长=5.12秒
2025-07-29 20:06:41,484 - INFO - 准备合并 3 个场景文件，总时长约 5.12秒
2025-07-29 20:06:41,484 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2291.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2292.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2293.mp4'

2025-07-29 20:06:41,484 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkg3chde3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkg3chde3\temp_combined.mp4
2025-07-29 20:06:41,622 - INFO - 合并后的视频时长: 5.19秒，目标音频时长: 3.87秒
2025-07-29 20:06:41,623 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkg3chde3\temp_combined.mp4 -ss 0 -to 3.87 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 20:06:41,898 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:41,898 - INFO - 目标音频时长: 3.87秒
2025-07-29 20:06:41,898 - INFO - 实际视频时长: 3.90秒
2025-07-29 20:06:41,898 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-29 20:06:41,899 - INFO - ==========================================
2025-07-29 20:06:41,899 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:41,899 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 20:06:41,899 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkg3chde3
2025-07-29 20:06:41,942 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:41,942 - INFO -   - 音频时长: 3.87秒
2025-07-29 20:06:41,942 - INFO -   - 视频时长: 3.90秒
2025-07-29 20:06:41,942 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-29 20:06:41,942 - INFO - 
----- 处理字幕 #59 的方案 #2 -----
2025-07-29 20:06:41,942 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 20:06:41,943 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpf9miv2w9
2025-07-29 20:06:41,943 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2291.mp4 (确认存在: True)
2025-07-29 20:06:41,943 - INFO - 添加场景ID=2291，时长=1.48秒，累计时长=1.48秒
2025-07-29 20:06:41,943 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2292.mp4 (确认存在: True)
2025-07-29 20:06:41,943 - INFO - 添加场景ID=2292，时长=2.24秒，累计时长=3.72秒
2025-07-29 20:06:41,943 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2293.mp4 (确认存在: True)
2025-07-29 20:06:41,943 - INFO - 添加场景ID=2293，时长=1.40秒，累计时长=5.12秒
2025-07-29 20:06:41,944 - INFO - 准备合并 3 个场景文件，总时长约 5.12秒
2025-07-29 20:06:41,944 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2291.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2292.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2293.mp4'

2025-07-29 20:06:41,944 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpf9miv2w9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpf9miv2w9\temp_combined.mp4
2025-07-29 20:06:42,070 - INFO - 合并后的视频时长: 5.19秒，目标音频时长: 3.87秒
2025-07-29 20:06:42,070 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpf9miv2w9\temp_combined.mp4 -ss 0 -to 3.87 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 20:06:42,350 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:42,351 - INFO - 目标音频时长: 3.87秒
2025-07-29 20:06:42,351 - INFO - 实际视频时长: 3.90秒
2025-07-29 20:06:42,351 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-29 20:06:42,351 - INFO - ==========================================
2025-07-29 20:06:42,351 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:42,351 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 20:06:42,351 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpf9miv2w9
2025-07-29 20:06:42,395 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:42,395 - INFO -   - 音频时长: 3.87秒
2025-07-29 20:06:42,395 - INFO -   - 视频时长: 3.90秒
2025-07-29 20:06:42,395 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-29 20:06:42,395 - INFO - 
字幕 #59 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:42,395 - INFO - 生成的视频文件:
2025-07-29 20:06:42,395 - INFO -   1. F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 20:06:42,395 - INFO -   2. F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 20:06:42,395 - INFO - ========== 字幕 #59 处理结束 ==========

