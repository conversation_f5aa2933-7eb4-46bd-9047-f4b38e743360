2025-07-29 20:06:39,775 - INFO - ========== 字幕 #58 处理开始 ==========
2025-07-29 20:06:39,775 - INFO - 字幕内容: 新欢见状，立刻恶人先告状，污蔑男人抄袭威尔集团，坑害许氏，要将他扭送法办。
2025-07-29 20:06:39,775 - INFO - 字幕序号: [2318, 2325]
2025-07-29 20:06:39,775 - INFO - 音频文件详情:
2025-07-29 20:06:39,775 - INFO -   - 路径: output\58.wav
2025-07-29 20:06:39,775 - INFO -   - 时长: 6.04秒
2025-07-29 20:06:39,775 - INFO -   - 验证音频时长: 6.04秒
2025-07-29 20:06:39,775 - INFO - 字幕时间戳信息:
2025-07-29 20:06:39,775 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:39,775 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:39,775 - INFO -   - 根据生成的音频时长(6.04秒)已调整字幕时间戳
2025-07-29 20:06:39,776 - INFO - ========== 新模式：为字幕 #58 生成4套场景方案 ==========
2025-07-29 20:06:39,776 - INFO - 字幕序号列表: [2318, 2325]
2025-07-29 20:06:39,776 - INFO - 
--- 生成方案 #1：基于字幕序号 #2318 ---
2025-07-29 20:06:39,776 - INFO - 开始为单个字幕序号 #2318 匹配场景，目标时长: 6.04秒
2025-07-29 20:06:39,776 - INFO - 开始查找字幕序号 [2318] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:39,776 - INFO - 找到related_overlap场景: scene_id=2258, 字幕#2318
2025-07-29 20:06:39,777 - INFO - 字幕 #2318 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:39,777 - INFO - 字幕序号 #2318 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:39,777 - INFO - 选择第一个overlap场景作为起点: scene_id=2258
2025-07-29 20:06:39,777 - INFO - 添加起点场景: scene_id=2258, 时长=2.08秒, 累计时长=2.08秒
2025-07-29 20:06:39,777 - INFO - 起点场景时长不足，需要延伸填充 3.96秒
2025-07-29 20:06:39,777 - INFO - 起点场景在原始列表中的索引: 2257
2025-07-29 20:06:39,777 - INFO - 延伸添加场景: scene_id=2259 (完整时长 0.92秒)
2025-07-29 20:06:39,777 - INFO - 累计时长: 3.00秒
2025-07-29 20:06:39,777 - INFO - 延伸添加场景: scene_id=2260 (完整时长 1.64秒)
2025-07-29 20:06:39,777 - INFO - 累计时长: 4.64秒
2025-07-29 20:06:39,777 - INFO - 延伸添加场景: scene_id=2261 (裁剪至 1.40秒)
2025-07-29 20:06:39,777 - INFO - 累计时长: 6.04秒
2025-07-29 20:06:39,777 - INFO - 字幕序号 #2318 场景匹配完成，共选择 4 个场景，总时长: 6.04秒
2025-07-29 20:06:39,777 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:39,777 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:39,777 - INFO - 
--- 生成方案 #2：基于字幕序号 #2325 ---
2025-07-29 20:06:39,777 - INFO - 开始为单个字幕序号 #2325 匹配场景，目标时长: 6.04秒
2025-07-29 20:06:39,777 - INFO - 开始查找字幕序号 [2325] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:39,778 - INFO - 找到related_overlap场景: scene_id=2263, 字幕#2325
2025-07-29 20:06:39,778 - INFO - 找到related_overlap场景: scene_id=2264, 字幕#2325
2025-07-29 20:06:39,779 - INFO - 字幕 #2325 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:39,779 - INFO - 字幕序号 #2325 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:39,779 - INFO - 选择第一个overlap场景作为起点: scene_id=2263
2025-07-29 20:06:39,779 - INFO - 添加起点场景: scene_id=2263, 时长=1.08秒, 累计时长=1.08秒
2025-07-29 20:06:39,779 - INFO - 起点场景时长不足，需要延伸填充 4.96秒
2025-07-29 20:06:39,779 - INFO - 起点场景在原始列表中的索引: 2262
2025-07-29 20:06:39,779 - INFO - 延伸添加场景: scene_id=2264 (完整时长 1.36秒)
2025-07-29 20:06:39,779 - INFO - 累计时长: 2.44秒
2025-07-29 20:06:39,779 - INFO - 延伸添加场景: scene_id=2265 (完整时长 2.92秒)
2025-07-29 20:06:39,779 - INFO - 累计时长: 5.36秒
2025-07-29 20:06:39,779 - INFO - 延伸添加场景: scene_id=2266 (裁剪至 0.68秒)
2025-07-29 20:06:39,779 - INFO - 累计时长: 6.04秒
2025-07-29 20:06:39,779 - INFO - 字幕序号 #2325 场景匹配完成，共选择 4 个场景，总时长: 6.04秒
2025-07-29 20:06:39,779 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:39,779 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:39,779 - INFO - ========== 当前模式：为字幕 #58 生成 1 套场景方案 ==========
2025-07-29 20:06:39,779 - INFO - 开始查找字幕序号 [2318, 2325] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:39,780 - INFO - 找到related_overlap场景: scene_id=2258, 字幕#2318
2025-07-29 20:06:39,780 - INFO - 找到related_overlap场景: scene_id=2263, 字幕#2325
2025-07-29 20:06:39,780 - INFO - 找到related_overlap场景: scene_id=2264, 字幕#2325
2025-07-29 20:06:39,781 - INFO - 字幕 #2318 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:39,781 - INFO - 字幕 #2325 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:39,781 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:39,781 - INFO - 开始生成方案 #1
2025-07-29 20:06:39,781 - INFO - 方案 #1: 为字幕#2318选择初始化overlap场景id=2258
2025-07-29 20:06:39,781 - INFO - 方案 #1: 为字幕#2325选择初始化overlap场景id=2264
2025-07-29 20:06:39,781 - INFO - 方案 #1: 初始选择后，当前总时长=3.44秒
2025-07-29 20:06:39,781 - INFO - 方案 #1: 额外添加overlap场景id=2263, 当前总时长=4.52秒
2025-07-29 20:06:39,781 - INFO - 方案 #1: 额外between选择后，当前总时长=4.52秒
2025-07-29 20:06:39,781 - INFO - 方案 #1: 场景总时长(4.52秒)小于音频时长(6.04秒)，需要延伸填充
2025-07-29 20:06:39,781 - INFO - 方案 #1: 最后一个场景ID: 2263
2025-07-29 20:06:39,781 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2262
2025-07-29 20:06:39,781 - INFO - 方案 #1: 需要填充时长: 1.52秒
2025-07-29 20:06:39,781 - INFO - 方案 #1: 跳过已使用的场景: scene_id=2264
2025-07-29 20:06:39,781 - INFO - 方案 #1: 追加场景 scene_id=2265 (裁剪至 1.52秒)
2025-07-29 20:06:39,781 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:39,781 - INFO - 方案 #1 调整/填充后最终总时长: 6.04秒
2025-07-29 20:06:39,781 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:39,781 - INFO - ========== 当前模式：字幕 #58 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:39,781 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:39,781 - INFO - ========== 新模式：字幕 #58 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:39,781 - INFO - 
----- 处理字幕 #58 的方案 #1 -----
2025-07-29 20:06:39,781 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 20:06:39,782 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpesrge2hx
2025-07-29 20:06:39,782 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2258.mp4 (确认存在: True)
2025-07-29 20:06:39,782 - INFO - 添加场景ID=2258，时长=2.08秒，累计时长=2.08秒
2025-07-29 20:06:39,782 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2259.mp4 (确认存在: True)
2025-07-29 20:06:39,782 - INFO - 添加场景ID=2259，时长=0.92秒，累计时长=3.00秒
2025-07-29 20:06:39,782 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2260.mp4 (确认存在: True)
2025-07-29 20:06:39,782 - INFO - 添加场景ID=2260，时长=1.64秒，累计时长=4.64秒
2025-07-29 20:06:39,782 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2261.mp4 (确认存在: True)
2025-07-29 20:06:39,782 - INFO - 添加场景ID=2261，时长=1.56秒，累计时长=6.20秒
2025-07-29 20:06:39,783 - INFO - 准备合并 4 个场景文件，总时长约 6.20秒
2025-07-29 20:06:39,783 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2258.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2259.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2260.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2261.mp4'

2025-07-29 20:06:39,783 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpesrge2hx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpesrge2hx\temp_combined.mp4
2025-07-29 20:06:39,931 - INFO - 合并后的视频时长: 6.29秒，目标音频时长: 6.04秒
2025-07-29 20:06:39,931 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpesrge2hx\temp_combined.mp4 -ss 0 -to 6.041 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 20:06:40,294 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:40,294 - INFO - 目标音频时长: 6.04秒
2025-07-29 20:06:40,294 - INFO - 实际视频时长: 6.10秒
2025-07-29 20:06:40,294 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-29 20:06:40,294 - INFO - ==========================================
2025-07-29 20:06:40,294 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:40,294 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 20:06:40,295 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpesrge2hx
2025-07-29 20:06:40,340 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:40,341 - INFO -   - 音频时长: 6.04秒
2025-07-29 20:06:40,341 - INFO -   - 视频时长: 6.10秒
2025-07-29 20:06:40,341 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-29 20:06:40,341 - INFO - 
----- 处理字幕 #58 的方案 #2 -----
2025-07-29 20:06:40,341 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 20:06:40,341 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsxy77clo
2025-07-29 20:06:40,341 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2263.mp4 (确认存在: True)
2025-07-29 20:06:40,341 - INFO - 添加场景ID=2263，时长=1.08秒，累计时长=1.08秒
2025-07-29 20:06:40,342 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2264.mp4 (确认存在: True)
2025-07-29 20:06:40,342 - INFO - 添加场景ID=2264，时长=1.36秒，累计时长=2.44秒
2025-07-29 20:06:40,342 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2265.mp4 (确认存在: True)
2025-07-29 20:06:40,342 - INFO - 添加场景ID=2265，时长=2.92秒，累计时长=5.36秒
2025-07-29 20:06:40,342 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2266.mp4 (确认存在: True)
2025-07-29 20:06:40,342 - INFO - 添加场景ID=2266，时长=1.80秒，累计时长=7.16秒
2025-07-29 20:06:40,342 - INFO - 准备合并 4 个场景文件，总时长约 7.16秒
2025-07-29 20:06:40,342 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2263.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2264.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2265.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2266.mp4'

2025-07-29 20:06:40,342 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsxy77clo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsxy77clo\temp_combined.mp4
2025-07-29 20:06:40,517 - INFO - 合并后的视频时长: 7.25秒，目标音频时长: 6.04秒
2025-07-29 20:06:40,517 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsxy77clo\temp_combined.mp4 -ss 0 -to 6.041 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 20:06:40,870 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:40,870 - INFO - 目标音频时长: 6.04秒
2025-07-29 20:06:40,871 - INFO - 实际视频时长: 6.10秒
2025-07-29 20:06:40,871 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-29 20:06:40,871 - INFO - ==========================================
2025-07-29 20:06:40,871 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:40,871 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 20:06:40,871 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsxy77clo
2025-07-29 20:06:40,917 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:40,917 - INFO -   - 音频时长: 6.04秒
2025-07-29 20:06:40,917 - INFO -   - 视频时长: 6.10秒
2025-07-29 20:06:40,917 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-29 20:06:40,917 - INFO - 
----- 处理字幕 #58 的方案 #3 -----
2025-07-29 20:06:40,917 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 20:06:40,917 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9j9vdtgo
2025-07-29 20:06:40,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2258.mp4 (确认存在: True)
2025-07-29 20:06:40,917 - INFO - 添加场景ID=2258，时长=2.08秒，累计时长=2.08秒
2025-07-29 20:06:40,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2264.mp4 (确认存在: True)
2025-07-29 20:06:40,917 - INFO - 添加场景ID=2264，时长=1.36秒，累计时长=3.44秒
2025-07-29 20:06:40,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2263.mp4 (确认存在: True)
2025-07-29 20:06:40,917 - INFO - 添加场景ID=2263，时长=1.08秒，累计时长=4.52秒
2025-07-29 20:06:40,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2265.mp4 (确认存在: True)
2025-07-29 20:06:40,918 - INFO - 添加场景ID=2265，时长=2.92秒，累计时长=7.44秒
2025-07-29 20:06:40,918 - INFO - 准备合并 4 个场景文件，总时长约 7.44秒
2025-07-29 20:06:40,918 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2258.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2264.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2263.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2265.mp4'

2025-07-29 20:06:40,918 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9j9vdtgo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9j9vdtgo\temp_combined.mp4
2025-07-29 20:06:41,064 - INFO - 合并后的视频时长: 7.53秒，目标音频时长: 6.04秒
2025-07-29 20:06:41,064 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9j9vdtgo\temp_combined.mp4 -ss 0 -to 6.041 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 20:06:41,416 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:41,418 - INFO - 目标音频时长: 6.04秒
2025-07-29 20:06:41,418 - INFO - 实际视频时长: 6.10秒
2025-07-29 20:06:41,418 - INFO - 时长差异: 0.06秒 (1.03%)
2025-07-29 20:06:41,418 - INFO - ==========================================
2025-07-29 20:06:41,418 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:41,418 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 20:06:41,418 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9j9vdtgo
2025-07-29 20:06:41,467 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:41,467 - INFO -   - 音频时长: 6.04秒
2025-07-29 20:06:41,467 - INFO -   - 视频时长: 6.10秒
2025-07-29 20:06:41,467 - INFO -   - 时长差异: 0.06秒 (1.03%)
2025-07-29 20:06:41,467 - INFO - 
字幕 #58 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:41,467 - INFO - 生成的视频文件:
2025-07-29 20:06:41,467 - INFO -   1. F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 20:06:41,467 - INFO -   2. F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 20:06:41,467 - INFO -   3. F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 20:06:41,467 - INFO - ========== 字幕 #58 处理结束 ==========

