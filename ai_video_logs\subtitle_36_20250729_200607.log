2025-07-29 20:06:07,953 - INFO - ========== 字幕 #36 处理开始 ==========
2025-07-29 20:06:07,953 - INFO - 字幕内容: 她以为是男人给妹妹办了转院，立刻动用关系全城寻找，却始终一无所获。
2025-07-29 20:06:07,953 - INFO - 字幕序号: [605, 610]
2025-07-29 20:06:07,953 - INFO - 音频文件详情:
2025-07-29 20:06:07,953 - INFO -   - 路径: output\36.wav
2025-07-29 20:06:07,953 - INFO -   - 时长: 4.73秒
2025-07-29 20:06:07,954 - INFO -   - 验证音频时长: 4.73秒
2025-07-29 20:06:07,954 - INFO - 字幕时间戳信息:
2025-07-29 20:06:07,954 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:07,954 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:07,954 - INFO -   - 根据生成的音频时长(4.73秒)已调整字幕时间戳
2025-07-29 20:06:07,954 - INFO - ========== 新模式：为字幕 #36 生成4套场景方案 ==========
2025-07-29 20:06:07,954 - INFO - 字幕序号列表: [605, 610]
2025-07-29 20:06:07,954 - INFO - 
--- 生成方案 #1：基于字幕序号 #605 ---
2025-07-29 20:06:07,954 - INFO - 开始为单个字幕序号 #605 匹配场景，目标时长: 4.73秒
2025-07-29 20:06:07,954 - INFO - 开始查找字幕序号 [605] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:07,955 - INFO - 找到related_overlap场景: scene_id=656, 字幕#605
2025-07-29 20:06:07,956 - INFO - 找到related_between场景: scene_id=657, 字幕#605
2025-07-29 20:06:07,956 - INFO - 找到related_between场景: scene_id=658, 字幕#605
2025-07-29 20:06:07,956 - INFO - 字幕 #605 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:07,956 - INFO - 字幕序号 #605 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 20:06:07,956 - INFO - 选择第一个overlap场景作为起点: scene_id=656
2025-07-29 20:06:07,956 - INFO - 添加起点场景: scene_id=656, 时长=5.88秒, 累计时长=5.88秒
2025-07-29 20:06:07,956 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:07,956 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 20:06:07,956 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 20:06:07,956 - INFO - 
--- 生成方案 #2：基于字幕序号 #610 ---
2025-07-29 20:06:07,956 - INFO - 开始为单个字幕序号 #610 匹配场景，目标时长: 4.73秒
2025-07-29 20:06:07,956 - INFO - 开始查找字幕序号 [610] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:07,957 - INFO - 找到related_overlap场景: scene_id=659, 字幕#610
2025-07-29 20:06:07,957 - INFO - 找到related_between场景: scene_id=660, 字幕#610
2025-07-29 20:06:07,958 - INFO - 字幕 #610 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:07,958 - INFO - 字幕序号 #610 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:07,958 - INFO - 选择第一个overlap场景作为起点: scene_id=659
2025-07-29 20:06:07,958 - INFO - 添加起点场景: scene_id=659, 时长=6.84秒, 累计时长=6.84秒
2025-07-29 20:06:07,958 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 20:06:07,958 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 20:06:07,958 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:07,958 - INFO - ========== 当前模式：为字幕 #36 生成 1 套场景方案 ==========
2025-07-29 20:06:07,958 - INFO - 开始查找字幕序号 [605, 610] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:07,958 - INFO - 找到related_overlap场景: scene_id=656, 字幕#605
2025-07-29 20:06:07,958 - INFO - 找到related_overlap场景: scene_id=659, 字幕#610
2025-07-29 20:06:07,959 - INFO - 找到related_between场景: scene_id=657, 字幕#605
2025-07-29 20:06:07,959 - INFO - 找到related_between场景: scene_id=658, 字幕#605
2025-07-29 20:06:07,959 - INFO - 找到related_between场景: scene_id=660, 字幕#610
2025-07-29 20:06:07,959 - INFO - 字幕 #605 找到 1 个overlap场景, 2 个between场景
2025-07-29 20:06:07,959 - INFO - 字幕 #610 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:06:07,959 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 20:06:07,959 - INFO - 开始生成方案 #1
2025-07-29 20:06:07,959 - INFO - 方案 #1: 为字幕#605选择初始化overlap场景id=656
2025-07-29 20:06:07,959 - INFO - 方案 #1: 为字幕#610选择初始化overlap场景id=659
2025-07-29 20:06:07,959 - INFO - 方案 #1: 初始选择后，当前总时长=12.72秒
2025-07-29 20:06:07,959 - INFO - 方案 #1: 额外between选择后，当前总时长=12.72秒
2025-07-29 20:06:07,959 - INFO - 方案 #1: 场景总时长(12.72秒)大于音频时长(4.73秒)，需要裁剪
2025-07-29 20:06:07,959 - INFO - 调整前总时长: 12.72秒, 目标时长: 4.73秒
2025-07-29 20:06:07,959 - INFO - 需要裁剪 7.99秒
2025-07-29 20:06:07,959 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:07,959 - INFO - 裁剪场景ID=659：从6.84秒裁剪至2.05秒
2025-07-29 20:06:07,959 - INFO - 裁剪场景ID=656：从5.88秒裁剪至2.68秒
2025-07-29 20:06:07,959 - INFO - 调整后总时长: 4.73秒，与目标时长差异: 0.00秒
2025-07-29 20:06:07,959 - INFO - 方案 #1 调整/填充后最终总时长: 4.73秒
2025-07-29 20:06:07,959 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:07,959 - INFO - ========== 当前模式：字幕 #36 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:07,960 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:07,960 - INFO - ========== 新模式：字幕 #36 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:07,960 - INFO - 
----- 处理字幕 #36 的方案 #1 -----
2025-07-29 20:06:07,960 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 20:06:07,960 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppxxowrmi
2025-07-29 20:06:07,961 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\656.mp4 (确认存在: True)
2025-07-29 20:06:07,961 - INFO - 添加场景ID=656，时长=5.88秒，累计时长=5.88秒
2025-07-29 20:06:07,961 - INFO - 准备合并 1 个场景文件，总时长约 5.88秒
2025-07-29 20:06:07,961 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/656.mp4'

2025-07-29 20:06:07,961 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppxxowrmi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppxxowrmi\temp_combined.mp4
2025-07-29 20:06:08,074 - INFO - 合并后的视频时长: 5.90秒，目标音频时长: 4.73秒
2025-07-29 20:06:08,074 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppxxowrmi\temp_combined.mp4 -ss 0 -to 4.733 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 20:06:08,482 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:08,482 - INFO - 目标音频时长: 4.73秒
2025-07-29 20:06:08,482 - INFO - 实际视频时长: 4.78秒
2025-07-29 20:06:08,482 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-29 20:06:08,482 - INFO - ==========================================
2025-07-29 20:06:08,482 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:08,482 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 20:06:08,483 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppxxowrmi
2025-07-29 20:06:08,527 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:08,527 - INFO -   - 音频时长: 4.73秒
2025-07-29 20:06:08,527 - INFO -   - 视频时长: 4.78秒
2025-07-29 20:06:08,527 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-29 20:06:08,527 - INFO - 
----- 处理字幕 #36 的方案 #2 -----
2025-07-29 20:06:08,527 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 20:06:08,528 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwx5uo491
2025-07-29 20:06:08,528 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\659.mp4 (确认存在: True)
2025-07-29 20:06:08,528 - INFO - 添加场景ID=659，时长=6.84秒，累计时长=6.84秒
2025-07-29 20:06:08,528 - INFO - 准备合并 1 个场景文件，总时长约 6.84秒
2025-07-29 20:06:08,528 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/659.mp4'

2025-07-29 20:06:08,529 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwx5uo491\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwx5uo491\temp_combined.mp4
2025-07-29 20:06:08,651 - INFO - 合并后的视频时长: 6.86秒，目标音频时长: 4.73秒
2025-07-29 20:06:08,651 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwx5uo491\temp_combined.mp4 -ss 0 -to 4.733 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 20:06:09,039 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:09,039 - INFO - 目标音频时长: 4.73秒
2025-07-29 20:06:09,039 - INFO - 实际视频时长: 4.78秒
2025-07-29 20:06:09,039 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-29 20:06:09,039 - INFO - ==========================================
2025-07-29 20:06:09,039 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:09,039 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 20:06:09,040 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwx5uo491
2025-07-29 20:06:09,084 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:09,084 - INFO -   - 音频时长: 4.73秒
2025-07-29 20:06:09,084 - INFO -   - 视频时长: 4.78秒
2025-07-29 20:06:09,084 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-29 20:06:09,084 - INFO - 
----- 处理字幕 #36 的方案 #3 -----
2025-07-29 20:06:09,084 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 20:06:09,085 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxqbe1zyv
2025-07-29 20:06:09,085 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\656.mp4 (确认存在: True)
2025-07-29 20:06:09,086 - INFO - 添加场景ID=656，时长=5.88秒，累计时长=5.88秒
2025-07-29 20:06:09,086 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\659.mp4 (确认存在: True)
2025-07-29 20:06:09,086 - INFO - 添加场景ID=659，时长=6.84秒，累计时长=12.72秒
2025-07-29 20:06:09,086 - INFO - 场景总时长(12.72秒)已达到音频时长(4.73秒)的1.5倍，停止添加场景
2025-07-29 20:06:09,086 - INFO - 准备合并 2 个场景文件，总时长约 12.72秒
2025-07-29 20:06:09,086 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/656.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/659.mp4'

2025-07-29 20:06:09,086 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxqbe1zyv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxqbe1zyv\temp_combined.mp4
2025-07-29 20:06:09,208 - INFO - 合并后的视频时长: 12.77秒，目标音频时长: 4.73秒
2025-07-29 20:06:09,208 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxqbe1zyv\temp_combined.mp4 -ss 0 -to 4.733 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 20:06:09,523 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:09,523 - INFO - 目标音频时长: 4.73秒
2025-07-29 20:06:09,523 - INFO - 实际视频时长: 4.78秒
2025-07-29 20:06:09,523 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-29 20:06:09,523 - INFO - ==========================================
2025-07-29 20:06:09,523 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:09,523 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 20:06:09,524 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxqbe1zyv
2025-07-29 20:06:09,573 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:09,573 - INFO -   - 音频时长: 4.73秒
2025-07-29 20:06:09,573 - INFO -   - 视频时长: 4.78秒
2025-07-29 20:06:09,573 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-29 20:06:09,573 - INFO - 
字幕 #36 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:09,573 - INFO - 生成的视频文件:
2025-07-29 20:06:09,573 - INFO -   1. F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 20:06:09,573 - INFO -   2. F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 20:06:09,573 - INFO -   3. F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 20:06:09,573 - INFO - ========== 字幕 #36 处理结束 ==========

