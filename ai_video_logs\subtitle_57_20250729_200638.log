2025-07-29 20:06:38,562 - INFO - ========== 字幕 #57 处理开始 ==========
2025-07-29 20:06:38,562 - INFO - 字幕内容: 就在众人要将他们轰出去的危急时刻，男人出现了。
2025-07-29 20:06:38,562 - INFO - 字幕序号: [2305, 2309]
2025-07-29 20:06:38,562 - INFO - 音频文件详情:
2025-07-29 20:06:38,562 - INFO -   - 路径: output\57.wav
2025-07-29 20:06:38,562 - INFO -   - 时长: 1.97秒
2025-07-29 20:06:38,563 - INFO -   - 验证音频时长: 1.97秒
2025-07-29 20:06:38,563 - INFO - 字幕时间戳信息:
2025-07-29 20:06:38,563 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:38,563 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:38,563 - INFO -   - 根据生成的音频时长(1.97秒)已调整字幕时间戳
2025-07-29 20:06:38,563 - INFO - ========== 新模式：为字幕 #57 生成4套场景方案 ==========
2025-07-29 20:06:38,563 - INFO - 字幕序号列表: [2305, 2309]
2025-07-29 20:06:38,563 - INFO - 
--- 生成方案 #1：基于字幕序号 #2305 ---
2025-07-29 20:06:38,563 - INFO - 开始为单个字幕序号 #2305 匹配场景，目标时长: 1.97秒
2025-07-29 20:06:38,563 - INFO - 开始查找字幕序号 [2305] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:38,564 - INFO - 找到related_overlap场景: scene_id=2234, 字幕#2305
2025-07-29 20:06:38,565 - INFO - 字幕 #2305 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:38,565 - INFO - 字幕序号 #2305 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:38,565 - INFO - 选择第一个overlap场景作为起点: scene_id=2234
2025-07-29 20:06:38,565 - INFO - 添加起点场景: scene_id=2234, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 20:06:38,565 - INFO - 起点场景时长不足，需要延伸填充 0.09秒
2025-07-29 20:06:38,565 - INFO - 起点场景在原始列表中的索引: 2233
2025-07-29 20:06:38,565 - INFO - 延伸添加场景: scene_id=2235 (裁剪至 0.09秒)
2025-07-29 20:06:38,565 - INFO - 累计时长: 1.97秒
2025-07-29 20:06:38,565 - INFO - 字幕序号 #2305 场景匹配完成，共选择 2 个场景，总时长: 1.97秒
2025-07-29 20:06:38,565 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 20:06:38,565 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 20:06:38,565 - INFO - 
--- 生成方案 #2：基于字幕序号 #2309 ---
2025-07-29 20:06:38,565 - INFO - 开始为单个字幕序号 #2309 匹配场景，目标时长: 1.97秒
2025-07-29 20:06:38,565 - INFO - 开始查找字幕序号 [2309] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:38,565 - INFO - 找到related_overlap场景: scene_id=2237, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2238, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2239, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2240, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2241, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2242, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2243, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2244, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2245, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2246, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2247, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2248, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 找到related_between场景: scene_id=2249, 字幕#2309
2025-07-29 20:06:38,566 - INFO - 字幕 #2309 找到 1 个overlap场景, 12 个between场景
2025-07-29 20:06:38,566 - INFO - 字幕序号 #2309 找到 1 个可用overlap场景, 12 个可用between场景
2025-07-29 20:06:38,566 - INFO - 选择第一个overlap场景作为起点: scene_id=2237
2025-07-29 20:06:38,567 - INFO - 添加起点场景: scene_id=2237, 时长=1.28秒, 累计时长=1.28秒
2025-07-29 20:06:38,567 - INFO - 起点场景时长不足，需要延伸填充 0.70秒
2025-07-29 20:06:38,567 - INFO - 起点场景在原始列表中的索引: 2236
2025-07-29 20:06:38,567 - INFO - 延伸添加场景: scene_id=2238 (裁剪至 0.70秒)
2025-07-29 20:06:38,567 - INFO - 累计时长: 1.97秒
2025-07-29 20:06:38,567 - INFO - 字幕序号 #2309 场景匹配完成，共选择 2 个场景，总时长: 1.97秒
2025-07-29 20:06:38,567 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 20:06:38,567 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:38,567 - INFO - ========== 当前模式：为字幕 #57 生成 1 套场景方案 ==========
2025-07-29 20:06:38,567 - INFO - 开始查找字幕序号 [2305, 2309] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:38,567 - INFO - 找到related_overlap场景: scene_id=2234, 字幕#2305
2025-07-29 20:06:38,567 - INFO - 找到related_overlap场景: scene_id=2237, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2238, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2239, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2240, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2241, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2242, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2243, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2244, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2245, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2246, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2247, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2248, 字幕#2309
2025-07-29 20:06:38,568 - INFO - 找到related_between场景: scene_id=2249, 字幕#2309
2025-07-29 20:06:38,569 - INFO - 字幕 #2305 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:38,569 - INFO - 字幕 #2309 找到 1 个overlap场景, 12 个between场景
2025-07-29 20:06:38,569 - INFO - 共收集 2 个未使用的overlap场景和 12 个未使用的between场景
2025-07-29 20:06:38,569 - INFO - 开始生成方案 #1
2025-07-29 20:06:38,569 - INFO - 方案 #1: 为字幕#2305选择初始化overlap场景id=2234
2025-07-29 20:06:38,569 - INFO - 方案 #1: 为字幕#2309选择初始化overlap场景id=2237
2025-07-29 20:06:38,569 - INFO - 方案 #1: 初始选择后，当前总时长=3.16秒
2025-07-29 20:06:38,569 - INFO - 方案 #1: 额外between选择后，当前总时长=3.16秒
2025-07-29 20:06:38,569 - INFO - 方案 #1: 场景总时长(3.16秒)大于音频时长(1.97秒)，需要裁剪
2025-07-29 20:06:38,569 - INFO - 调整前总时长: 3.16秒, 目标时长: 1.97秒
2025-07-29 20:06:38,569 - INFO - 需要裁剪 1.18秒
2025-07-29 20:06:38,569 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 20:06:38,569 - INFO - 裁剪场景ID=2234：从1.88秒裁剪至1.00秒
2025-07-29 20:06:38,569 - INFO - 裁剪场景ID=2237：从1.28秒裁剪至1.00秒
2025-07-29 20:06:38,569 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.03秒
2025-07-29 20:06:38,569 - INFO - 调整后总时长: 2.00秒，与目标时长差异: 0.03秒
2025-07-29 20:06:38,569 - INFO - 方案 #1 调整/填充后最终总时长: 2.00秒
2025-07-29 20:06:38,569 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:38,569 - INFO - ========== 当前模式：字幕 #57 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:38,569 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:38,569 - INFO - ========== 新模式：字幕 #57 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:38,569 - INFO - 
----- 处理字幕 #57 的方案 #1 -----
2025-07-29 20:06:38,569 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 20:06:38,569 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcmkhno1f
2025-07-29 20:06:38,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2234.mp4 (确认存在: True)
2025-07-29 20:06:38,570 - INFO - 添加场景ID=2234，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:06:38,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2235.mp4 (确认存在: True)
2025-07-29 20:06:38,570 - INFO - 添加场景ID=2235，时长=1.72秒，累计时长=3.60秒
2025-07-29 20:06:38,570 - INFO - 场景总时长(3.60秒)已达到音频时长(1.97秒)的1.5倍，停止添加场景
2025-07-29 20:06:38,570 - INFO - 准备合并 2 个场景文件，总时长约 3.60秒
2025-07-29 20:06:38,570 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2234.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2235.mp4'

2025-07-29 20:06:38,570 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcmkhno1f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcmkhno1f\temp_combined.mp4
2025-07-29 20:06:38,703 - INFO - 合并后的视频时长: 3.65秒，目标音频时长: 1.97秒
2025-07-29 20:06:38,704 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcmkhno1f\temp_combined.mp4 -ss 0 -to 1.974 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 20:06:38,931 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:38,931 - INFO - 目标音频时长: 1.97秒
2025-07-29 20:06:38,931 - INFO - 实际视频时长: 2.02秒
2025-07-29 20:06:38,932 - INFO - 时长差异: 0.05秒 (2.48%)
2025-07-29 20:06:38,932 - INFO - ==========================================
2025-07-29 20:06:38,932 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:38,932 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 20:06:38,932 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcmkhno1f
2025-07-29 20:06:38,977 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:38,977 - INFO -   - 音频时长: 1.97秒
2025-07-29 20:06:38,977 - INFO -   - 视频时长: 2.02秒
2025-07-29 20:06:38,977 - INFO -   - 时长差异: 0.05秒 (2.48%)
2025-07-29 20:06:38,977 - INFO - 
----- 处理字幕 #57 的方案 #2 -----
2025-07-29 20:06:38,977 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 20:06:38,978 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3plcumkf
2025-07-29 20:06:38,979 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2237.mp4 (确认存在: True)
2025-07-29 20:06:38,979 - INFO - 添加场景ID=2237，时长=1.28秒，累计时长=1.28秒
2025-07-29 20:06:38,979 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2238.mp4 (确认存在: True)
2025-07-29 20:06:38,979 - INFO - 添加场景ID=2238，时长=2.36秒，累计时长=3.64秒
2025-07-29 20:06:38,979 - INFO - 场景总时长(3.64秒)已达到音频时长(1.97秒)的1.5倍，停止添加场景
2025-07-29 20:06:38,979 - INFO - 准备合并 2 个场景文件，总时长约 3.64秒
2025-07-29 20:06:38,979 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2237.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2238.mp4'

2025-07-29 20:06:38,979 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3plcumkf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3plcumkf\temp_combined.mp4
2025-07-29 20:06:39,117 - INFO - 合并后的视频时长: 3.69秒，目标音频时长: 1.97秒
2025-07-29 20:06:39,117 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3plcumkf\temp_combined.mp4 -ss 0 -to 1.974 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 20:06:39,334 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:39,334 - INFO - 目标音频时长: 1.97秒
2025-07-29 20:06:39,334 - INFO - 实际视频时长: 2.02秒
2025-07-29 20:06:39,335 - INFO - 时长差异: 0.05秒 (2.48%)
2025-07-29 20:06:39,335 - INFO - ==========================================
2025-07-29 20:06:39,335 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:39,335 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 20:06:39,335 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3plcumkf
2025-07-29 20:06:39,382 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:39,382 - INFO -   - 音频时长: 1.97秒
2025-07-29 20:06:39,382 - INFO -   - 视频时长: 2.02秒
2025-07-29 20:06:39,382 - INFO -   - 时长差异: 0.05秒 (2.48%)
2025-07-29 20:06:39,382 - INFO - 
----- 处理字幕 #57 的方案 #3 -----
2025-07-29 20:06:39,382 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 20:06:39,382 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptfe69g6i
2025-07-29 20:06:39,383 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2234.mp4 (确认存在: True)
2025-07-29 20:06:39,383 - INFO - 添加场景ID=2234，时长=1.88秒，累计时长=1.88秒
2025-07-29 20:06:39,383 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2237.mp4 (确认存在: True)
2025-07-29 20:06:39,383 - INFO - 添加场景ID=2237，时长=1.28秒，累计时长=3.16秒
2025-07-29 20:06:39,383 - INFO - 场景总时长(3.16秒)已达到音频时长(1.97秒)的1.5倍，停止添加场景
2025-07-29 20:06:39,383 - INFO - 准备合并 2 个场景文件，总时长约 3.16秒
2025-07-29 20:06:39,383 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2234.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2237.mp4'

2025-07-29 20:06:39,383 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptfe69g6i\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptfe69g6i\temp_combined.mp4
2025-07-29 20:06:39,497 - INFO - 合并后的视频时长: 3.21秒，目标音频时长: 1.97秒
2025-07-29 20:06:39,497 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptfe69g6i\temp_combined.mp4 -ss 0 -to 1.974 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 20:06:39,726 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:39,727 - INFO - 目标音频时长: 1.97秒
2025-07-29 20:06:39,727 - INFO - 实际视频时长: 2.02秒
2025-07-29 20:06:39,727 - INFO - 时长差异: 0.05秒 (2.48%)
2025-07-29 20:06:39,727 - INFO - ==========================================
2025-07-29 20:06:39,727 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:39,727 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 20:06:39,727 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptfe69g6i
2025-07-29 20:06:39,774 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:39,774 - INFO -   - 音频时长: 1.97秒
2025-07-29 20:06:39,774 - INFO -   - 视频时长: 2.02秒
2025-07-29 20:06:39,774 - INFO -   - 时长差异: 0.05秒 (2.48%)
2025-07-29 20:06:39,774 - INFO - 
字幕 #57 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:39,774 - INFO - 生成的视频文件:
2025-07-29 20:06:39,774 - INFO -   1. F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 20:06:39,774 - INFO -   2. F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 20:06:39,774 - INFO -   3. F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 20:06:39,774 - INFO - ========== 字幕 #57 处理结束 ==========

