2025-07-29 20:05:26,312 - INFO - ========== 字幕 #8 处理开始 ==========
2025-07-29 20:05:26,312 - INFO - 字幕内容: 男人心急如焚，他不是来吃醋的，而是来救命的！妹妹心脏病发，急需手术费！
2025-07-29 20:05:26,312 - INFO - 字幕序号: [159, 161]
2025-07-29 20:05:26,312 - INFO - 音频文件详情:
2025-07-29 20:05:26,312 - INFO -   - 路径: output\8.wav
2025-07-29 20:05:26,312 - INFO -   - 时长: 5.78秒
2025-07-29 20:05:26,312 - INFO -   - 验证音频时长: 5.78秒
2025-07-29 20:05:26,313 - INFO - 字幕时间戳信息:
2025-07-29 20:05:26,313 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:26,313 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:26,313 - INFO -   - 根据生成的音频时长(5.78秒)已调整字幕时间戳
2025-07-29 20:05:26,313 - INFO - ========== 新模式：为字幕 #8 生成4套场景方案 ==========
2025-07-29 20:05:26,313 - INFO - 字幕序号列表: [159, 161]
2025-07-29 20:05:26,313 - INFO - 
--- 生成方案 #1：基于字幕序号 #159 ---
2025-07-29 20:05:26,313 - INFO - 开始为单个字幕序号 #159 匹配场景，目标时长: 5.78秒
2025-07-29 20:05:26,313 - INFO - 开始查找字幕序号 [159] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:26,313 - INFO - 找到related_overlap场景: scene_id=181, 字幕#159
2025-07-29 20:05:26,313 - INFO - 找到related_overlap场景: scene_id=182, 字幕#159
2025-07-29 20:05:26,314 - INFO - 字幕 #159 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:26,314 - INFO - 字幕序号 #159 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:26,314 - ERROR - 字幕序号 #159 没有找到任何可用的匹配场景
2025-07-29 20:05:26,314 - WARNING - 方案 #1 生成失败，未找到合适的场景
2025-07-29 20:05:26,314 - INFO - 
--- 生成方案 #2：基于字幕序号 #161 ---
2025-07-29 20:05:26,314 - INFO - 开始为单个字幕序号 #161 匹配场景，目标时长: 5.78秒
2025-07-29 20:05:26,314 - INFO - 开始查找字幕序号 [161] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:26,314 - INFO - 找到related_overlap场景: scene_id=183, 字幕#161
2025-07-29 20:05:26,315 - INFO - 字幕 #161 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:26,316 - INFO - 字幕序号 #161 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:26,316 - INFO - 选择第一个overlap场景作为起点: scene_id=183
2025-07-29 20:05:26,316 - INFO - 添加起点场景: scene_id=183, 时长=3.44秒, 累计时长=3.44秒
2025-07-29 20:05:26,316 - INFO - 起点场景时长不足，需要延伸填充 2.34秒
2025-07-29 20:05:26,316 - INFO - 起点场景在原始列表中的索引: 182
2025-07-29 20:05:26,316 - INFO - 延伸添加场景: scene_id=184 (完整时长 1.76秒)
2025-07-29 20:05:26,316 - INFO - 累计时长: 5.20秒
2025-07-29 20:05:26,316 - INFO - 延伸添加场景: scene_id=185 (裁剪至 0.58秒)
2025-07-29 20:05:26,316 - INFO - 累计时长: 5.78秒
2025-07-29 20:05:26,316 - INFO - 字幕序号 #161 场景匹配完成，共选择 3 个场景，总时长: 5.78秒
2025-07-29 20:05:26,316 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:26,316 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:05:26,316 - INFO - ========== 当前模式：为字幕 #8 生成 1 套场景方案 ==========
2025-07-29 20:05:26,316 - INFO - 开始查找字幕序号 [159, 161] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:26,316 - INFO - 找到related_overlap场景: scene_id=181, 字幕#159
2025-07-29 20:05:26,316 - INFO - 找到related_overlap场景: scene_id=182, 字幕#159
2025-07-29 20:05:26,316 - INFO - 找到related_overlap场景: scene_id=183, 字幕#161
2025-07-29 20:05:26,317 - INFO - 字幕 #159 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:05:26,317 - INFO - 字幕 #161 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:26,317 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:05:26,317 - INFO - 开始生成方案 #1
2025-07-29 20:05:26,317 - INFO - 方案 #1: 为字幕#159选择初始化overlap场景id=182
2025-07-29 20:05:26,317 - INFO - 方案 #1: 为字幕#161选择初始化overlap场景id=183
2025-07-29 20:05:26,317 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-07-29 20:05:26,317 - INFO - 方案 #1: 额外添加overlap场景id=181, 当前总时长=5.40秒
2025-07-29 20:05:26,317 - INFO - 方案 #1: 额外between选择后，当前总时长=5.40秒
2025-07-29 20:05:26,317 - INFO - 方案 #1: 场景总时长(5.40秒)小于音频时长(5.78秒)，需要延伸填充
2025-07-29 20:05:26,317 - INFO - 方案 #1: 最后一个场景ID: 181
2025-07-29 20:05:26,317 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 180
2025-07-29 20:05:26,317 - INFO - 方案 #1: 需要填充时长: 0.38秒
2025-07-29 20:05:26,317 - INFO - 方案 #1: 跳过已使用的场景: scene_id=182
2025-07-29 20:05:26,317 - INFO - 方案 #1: 跳过已使用的场景: scene_id=183
2025-07-29 20:05:26,317 - INFO - 方案 #1: 追加场景 scene_id=184 (裁剪至 0.38秒)
2025-07-29 20:05:26,317 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:26,317 - INFO - 方案 #1 调整/填充后最终总时长: 5.78秒
2025-07-29 20:05:26,317 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:26,317 - INFO - ========== 当前模式：字幕 #8 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:26,317 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:05:26,317 - INFO - ========== 新模式：字幕 #8 共生成 2 套有效场景方案 ==========
2025-07-29 20:05:26,317 - INFO - 
----- 处理字幕 #8 的方案 #1 -----
2025-07-29 20:05:26,317 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 20:05:26,327 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpitirhjjl
2025-07-29 20:05:26,327 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\183.mp4 (确认存在: True)
2025-07-29 20:05:26,328 - INFO - 添加场景ID=183，时长=3.44秒，累计时长=3.44秒
2025-07-29 20:05:26,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\184.mp4 (确认存在: True)
2025-07-29 20:05:26,328 - INFO - 添加场景ID=184，时长=1.76秒，累计时长=5.20秒
2025-07-29 20:05:26,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\185.mp4 (确认存在: True)
2025-07-29 20:05:26,328 - INFO - 添加场景ID=185，时长=1.48秒，累计时长=6.68秒
2025-07-29 20:05:26,328 - INFO - 准备合并 3 个场景文件，总时长约 6.68秒
2025-07-29 20:05:26,328 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/183.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/184.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/185.mp4'

2025-07-29 20:05:26,328 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpitirhjjl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpitirhjjl\temp_combined.mp4
2025-07-29 20:05:26,458 - INFO - 合并后的视频时长: 6.75秒，目标音频时长: 5.78秒
2025-07-29 20:05:26,458 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpitirhjjl\temp_combined.mp4 -ss 0 -to 5.781 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 20:05:26,804 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:26,804 - INFO - 目标音频时长: 5.78秒
2025-07-29 20:05:26,804 - INFO - 实际视频时长: 5.82秒
2025-07-29 20:05:26,804 - INFO - 时长差异: 0.04秒 (0.73%)
2025-07-29 20:05:26,804 - INFO - ==========================================
2025-07-29 20:05:26,804 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:26,804 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 20:05:26,805 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpitirhjjl
2025-07-29 20:05:26,850 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:26,850 - INFO -   - 音频时长: 5.78秒
2025-07-29 20:05:26,850 - INFO -   - 视频时长: 5.82秒
2025-07-29 20:05:26,850 - INFO -   - 时长差异: 0.04秒 (0.73%)
2025-07-29 20:05:26,850 - INFO - 
----- 处理字幕 #8 的方案 #2 -----
2025-07-29 20:05:26,850 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 20:05:26,851 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu02klr5c
2025-07-29 20:05:26,851 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\182.mp4 (确认存在: True)
2025-07-29 20:05:26,851 - INFO - 添加场景ID=182，时长=1.28秒，累计时长=1.28秒
2025-07-29 20:05:26,851 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\183.mp4 (确认存在: True)
2025-07-29 20:05:26,851 - INFO - 添加场景ID=183，时长=3.44秒，累计时长=4.72秒
2025-07-29 20:05:26,851 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\181.mp4 (确认存在: True)
2025-07-29 20:05:26,851 - INFO - 添加场景ID=181，时长=0.68秒，累计时长=5.40秒
2025-07-29 20:05:26,852 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\184.mp4 (确认存在: True)
2025-07-29 20:05:26,852 - INFO - 添加场景ID=184，时长=1.76秒，累计时长=7.16秒
2025-07-29 20:05:26,852 - INFO - 准备合并 4 个场景文件，总时长约 7.16秒
2025-07-29 20:05:26,852 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/182.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/183.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/181.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/184.mp4'

2025-07-29 20:05:26,852 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu02klr5c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu02klr5c\temp_combined.mp4
2025-07-29 20:05:26,990 - INFO - 合并后的视频时长: 7.25秒，目标音频时长: 5.78秒
2025-07-29 20:05:26,990 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu02klr5c\temp_combined.mp4 -ss 0 -to 5.781 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 20:05:27,322 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:27,322 - INFO - 目标音频时长: 5.78秒
2025-07-29 20:05:27,322 - INFO - 实际视频时长: 5.82秒
2025-07-29 20:05:27,322 - INFO - 时长差异: 0.04秒 (0.73%)
2025-07-29 20:05:27,322 - INFO - ==========================================
2025-07-29 20:05:27,322 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:27,322 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 20:05:27,323 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu02klr5c
2025-07-29 20:05:27,367 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:27,367 - INFO -   - 音频时长: 5.78秒
2025-07-29 20:05:27,367 - INFO -   - 视频时长: 5.82秒
2025-07-29 20:05:27,367 - INFO -   - 时长差异: 0.04秒 (0.73%)
2025-07-29 20:05:27,367 - INFO - 
字幕 #8 处理完成，成功生成 2/2 套方案
2025-07-29 20:05:27,367 - INFO - 生成的视频文件:
2025-07-29 20:05:27,367 - INFO -   1. F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 20:05:27,367 - INFO -   2. F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 20:05:27,367 - INFO - ========== 字幕 #8 处理结束 ==========

