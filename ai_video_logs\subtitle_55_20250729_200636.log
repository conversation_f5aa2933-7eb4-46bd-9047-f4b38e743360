2025-07-29 20:06:36,121 - INFO - ========== 字幕 #55 处理开始 ==========
2025-07-29 20:06:36,121 - INFO - 字幕内容: 然而，林总话锋一转，指出这份计划书与威尔集团未公开的项目核心内容高度雷同，直指女人偷奸耍滑，抄袭商业机密。
2025-07-29 20:06:36,122 - INFO - 字幕序号: [2234, 2237]
2025-07-29 20:06:36,122 - INFO - 音频文件详情:
2025-07-29 20:06:36,122 - INFO -   - 路径: output\55.wav
2025-07-29 20:06:36,122 - INFO -   - 时长: 8.23秒
2025-07-29 20:06:36,122 - INFO -   - 验证音频时长: 8.23秒
2025-07-29 20:06:36,132 - INFO - 字幕时间戳信息:
2025-07-29 20:06:36,132 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:36,132 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:36,132 - INFO -   - 根据生成的音频时长(8.23秒)已调整字幕时间戳
2025-07-29 20:06:36,132 - INFO - ========== 新模式：为字幕 #55 生成4套场景方案 ==========
2025-07-29 20:06:36,132 - INFO - 字幕序号列表: [2234, 2237]
2025-07-29 20:06:36,132 - INFO - 
--- 生成方案 #1：基于字幕序号 #2234 ---
2025-07-29 20:06:36,132 - INFO - 开始为单个字幕序号 #2234 匹配场景，目标时长: 8.23秒
2025-07-29 20:06:36,132 - INFO - 开始查找字幕序号 [2234] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:36,132 - INFO - 找到related_overlap场景: scene_id=2185, 字幕#2234
2025-07-29 20:06:36,132 - INFO - 找到related_overlap场景: scene_id=2186, 字幕#2234
2025-07-29 20:06:36,133 - INFO - 找到related_between场景: scene_id=2187, 字幕#2234
2025-07-29 20:06:36,133 - INFO - 字幕 #2234 找到 2 个overlap场景, 1 个between场景
2025-07-29 20:06:36,133 - INFO - 字幕序号 #2234 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 20:06:36,133 - INFO - 选择第一个overlap场景作为起点: scene_id=2185
2025-07-29 20:06:36,133 - INFO - 添加起点场景: scene_id=2185, 时长=1.60秒, 累计时长=1.60秒
2025-07-29 20:06:36,133 - INFO - 起点场景时长不足，需要延伸填充 6.63秒
2025-07-29 20:06:36,133 - INFO - 起点场景在原始列表中的索引: 2184
2025-07-29 20:06:36,133 - INFO - 延伸添加场景: scene_id=2186 (完整时长 1.20秒)
2025-07-29 20:06:36,133 - INFO - 累计时长: 2.80秒
2025-07-29 20:06:36,133 - INFO - 延伸添加场景: scene_id=2187 (完整时长 1.20秒)
2025-07-29 20:06:36,133 - INFO - 累计时长: 4.00秒
2025-07-29 20:06:36,133 - INFO - 延伸添加场景: scene_id=2188 (完整时长 1.28秒)
2025-07-29 20:06:36,133 - INFO - 累计时长: 5.28秒
2025-07-29 20:06:36,133 - INFO - 延伸添加场景: scene_id=2189 (完整时长 2.16秒)
2025-07-29 20:06:36,133 - INFO - 累计时长: 7.43秒
2025-07-29 20:06:36,133 - INFO - 延伸添加场景: scene_id=2190 (裁剪至 0.80秒)
2025-07-29 20:06:36,133 - INFO - 累计时长: 8.23秒
2025-07-29 20:06:36,133 - INFO - 字幕序号 #2234 场景匹配完成，共选择 6 个场景，总时长: 8.23秒
2025-07-29 20:06:36,134 - INFO - 方案 #1 生成成功，包含 6 个场景
2025-07-29 20:06:36,134 - INFO - 新模式：第1套方案的 6 个场景已加入全局已使用集合
2025-07-29 20:06:36,134 - INFO - 
--- 生成方案 #2：基于字幕序号 #2237 ---
2025-07-29 20:06:36,134 - INFO - 开始为单个字幕序号 #2237 匹配场景，目标时长: 8.23秒
2025-07-29 20:06:36,134 - INFO - 开始查找字幕序号 [2237] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:36,134 - INFO - 找到related_overlap场景: scene_id=2189, 字幕#2237
2025-07-29 20:06:36,135 - INFO - 字幕 #2237 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:36,135 - INFO - 字幕序号 #2237 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:36,135 - ERROR - 字幕序号 #2237 没有找到任何可用的匹配场景
2025-07-29 20:06:36,135 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 20:06:36,135 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 20:06:36,135 - INFO - ========== 当前模式：为字幕 #55 生成 1 套场景方案 ==========
2025-07-29 20:06:36,135 - INFO - 开始查找字幕序号 [2234, 2237] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:36,135 - INFO - 找到related_overlap场景: scene_id=2185, 字幕#2234
2025-07-29 20:06:36,135 - INFO - 找到related_overlap场景: scene_id=2186, 字幕#2234
2025-07-29 20:06:36,135 - INFO - 找到related_overlap场景: scene_id=2189, 字幕#2237
2025-07-29 20:06:36,136 - INFO - 找到related_between场景: scene_id=2187, 字幕#2234
2025-07-29 20:06:36,136 - INFO - 字幕 #2234 找到 2 个overlap场景, 1 个between场景
2025-07-29 20:06:36,136 - INFO - 字幕 #2237 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:36,136 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:06:36,136 - INFO - 开始生成方案 #1
2025-07-29 20:06:36,136 - INFO - 方案 #1: 为字幕#2234选择初始化overlap场景id=2185
2025-07-29 20:06:36,136 - INFO - 方案 #1: 为字幕#2237选择初始化overlap场景id=2189
2025-07-29 20:06:36,136 - INFO - 方案 #1: 初始选择后，当前总时长=3.76秒
2025-07-29 20:06:36,136 - INFO - 方案 #1: 额外添加overlap场景id=2186, 当前总时长=4.96秒
2025-07-29 20:06:36,136 - INFO - 方案 #1: 额外between选择后，当前总时长=4.96秒
2025-07-29 20:06:36,136 - INFO - 方案 #1: 额外添加between场景id=2187, 当前总时长=6.16秒
2025-07-29 20:06:36,136 - INFO - 方案 #1: 场景总时长(6.16秒)小于音频时长(8.23秒)，需要延伸填充
2025-07-29 20:06:36,136 - INFO - 方案 #1: 最后一个场景ID: 2187
2025-07-29 20:06:36,136 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2186
2025-07-29 20:06:36,136 - INFO - 方案 #1: 需要填充时长: 2.08秒
2025-07-29 20:06:36,136 - INFO - 方案 #1: 追加场景 scene_id=2188 (完整时长 1.28秒)
2025-07-29 20:06:36,136 - INFO - 方案 #1: 跳过已使用的场景: scene_id=2189
2025-07-29 20:06:36,136 - INFO - 方案 #1: 追加场景 scene_id=2190 (裁剪至 0.80秒)
2025-07-29 20:06:36,136 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:36,136 - INFO - 方案 #1 调整/填充后最终总时长: 8.23秒
2025-07-29 20:06:36,136 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:36,136 - INFO - ========== 当前模式：字幕 #55 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:36,136 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 20:06:36,136 - INFO - ========== 新模式：字幕 #55 共生成 2 套有效场景方案 ==========
2025-07-29 20:06:36,136 - INFO - 
----- 处理字幕 #55 的方案 #1 -----
2025-07-29 20:06:36,137 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 20:06:36,137 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8bjk4oqk
2025-07-29 20:06:36,137 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2185.mp4 (确认存在: True)
2025-07-29 20:06:36,137 - INFO - 添加场景ID=2185，时长=1.60秒，累计时长=1.60秒
2025-07-29 20:06:36,137 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2186.mp4 (确认存在: True)
2025-07-29 20:06:36,138 - INFO - 添加场景ID=2186，时长=1.20秒，累计时长=2.80秒
2025-07-29 20:06:36,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2187.mp4 (确认存在: True)
2025-07-29 20:06:36,138 - INFO - 添加场景ID=2187，时长=1.20秒，累计时长=4.00秒
2025-07-29 20:06:36,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2188.mp4 (确认存在: True)
2025-07-29 20:06:36,138 - INFO - 添加场景ID=2188，时长=1.28秒，累计时长=5.28秒
2025-07-29 20:06:36,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2189.mp4 (确认存在: True)
2025-07-29 20:06:36,138 - INFO - 添加场景ID=2189，时长=2.16秒，累计时长=7.43秒
2025-07-29 20:06:36,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2190.mp4 (确认存在: True)
2025-07-29 20:06:36,138 - INFO - 添加场景ID=2190，时长=1.88秒，累计时长=9.31秒
2025-07-29 20:06:36,138 - INFO - 准备合并 6 个场景文件，总时长约 9.31秒
2025-07-29 20:06:36,138 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2185.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2186.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2187.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2188.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2189.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2190.mp4'

2025-07-29 20:06:36,138 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8bjk4oqk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8bjk4oqk\temp_combined.mp4
2025-07-29 20:06:36,319 - INFO - 合并后的视频时长: 9.44秒，目标音频时长: 8.23秒
2025-07-29 20:06:36,319 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8bjk4oqk\temp_combined.mp4 -ss 0 -to 8.231 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 20:06:36,743 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:36,743 - INFO - 目标音频时长: 8.23秒
2025-07-29 20:06:36,743 - INFO - 实际视频时长: 8.26秒
2025-07-29 20:06:36,743 - INFO - 时长差异: 0.03秒 (0.39%)
2025-07-29 20:06:36,743 - INFO - ==========================================
2025-07-29 20:06:36,743 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:36,743 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 20:06:36,744 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8bjk4oqk
2025-07-29 20:06:36,790 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:36,790 - INFO -   - 音频时长: 8.23秒
2025-07-29 20:06:36,790 - INFO -   - 视频时长: 8.26秒
2025-07-29 20:06:36,790 - INFO -   - 时长差异: 0.03秒 (0.39%)
2025-07-29 20:06:36,790 - INFO - 
----- 处理字幕 #55 的方案 #2 -----
2025-07-29 20:06:36,790 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 20:06:36,791 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpie4gzfwk
2025-07-29 20:06:36,791 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2185.mp4 (确认存在: True)
2025-07-29 20:06:36,791 - INFO - 添加场景ID=2185，时长=1.60秒，累计时长=1.60秒
2025-07-29 20:06:36,791 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2189.mp4 (确认存在: True)
2025-07-29 20:06:36,791 - INFO - 添加场景ID=2189，时长=2.16秒，累计时长=3.76秒
2025-07-29 20:06:36,791 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2186.mp4 (确认存在: True)
2025-07-29 20:06:36,791 - INFO - 添加场景ID=2186，时长=1.20秒，累计时长=4.96秒
2025-07-29 20:06:36,792 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2187.mp4 (确认存在: True)
2025-07-29 20:06:36,792 - INFO - 添加场景ID=2187，时长=1.20秒，累计时长=6.16秒
2025-07-29 20:06:36,792 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2188.mp4 (确认存在: True)
2025-07-29 20:06:36,792 - INFO - 添加场景ID=2188，时长=1.28秒，累计时长=7.43秒
2025-07-29 20:06:36,792 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2190.mp4 (确认存在: True)
2025-07-29 20:06:36,792 - INFO - 添加场景ID=2190，时长=1.88秒，累计时长=9.31秒
2025-07-29 20:06:36,792 - INFO - 准备合并 6 个场景文件，总时长约 9.31秒
2025-07-29 20:06:36,792 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2185.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2189.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2186.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2187.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2188.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2190.mp4'

2025-07-29 20:06:36,792 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpie4gzfwk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpie4gzfwk\temp_combined.mp4
2025-07-29 20:06:36,969 - INFO - 合并后的视频时长: 9.44秒，目标音频时长: 8.23秒
2025-07-29 20:06:36,969 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpie4gzfwk\temp_combined.mp4 -ss 0 -to 8.231 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 20:06:37,376 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:37,376 - INFO - 目标音频时长: 8.23秒
2025-07-29 20:06:37,376 - INFO - 实际视频时长: 8.26秒
2025-07-29 20:06:37,376 - INFO - 时长差异: 0.03秒 (0.39%)
2025-07-29 20:06:37,376 - INFO - ==========================================
2025-07-29 20:06:37,376 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:37,376 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 20:06:37,377 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpie4gzfwk
2025-07-29 20:06:37,422 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:37,422 - INFO -   - 音频时长: 8.23秒
2025-07-29 20:06:37,422 - INFO -   - 视频时长: 8.26秒
2025-07-29 20:06:37,422 - INFO -   - 时长差异: 0.03秒 (0.39%)
2025-07-29 20:06:37,422 - INFO - 
字幕 #55 处理完成，成功生成 2/2 套方案
2025-07-29 20:06:37,422 - INFO - 生成的视频文件:
2025-07-29 20:06:37,422 - INFO -   1. F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 20:06:37,422 - INFO -   2. F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 20:06:37,422 - INFO - ========== 字幕 #55 处理结束 ==========

