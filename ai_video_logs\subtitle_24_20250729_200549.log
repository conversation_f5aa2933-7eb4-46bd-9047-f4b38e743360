2025-07-29 20:05:49,313 - INFO - ========== 字幕 #24 处理开始 ==========
2025-07-29 20:05:49,313 - INFO - 字幕内容: 男人一言不发，只是拿起两人唯一的合照，当着她的面，将她的那一半狠狠撕下。
2025-07-29 20:05:49,313 - INFO - 字幕序号: [434, 439]
2025-07-29 20:05:49,313 - INFO - 音频文件详情:
2025-07-29 20:05:49,313 - INFO -   - 路径: output\24.wav
2025-07-29 20:05:49,313 - INFO -   - 时长: 4.24秒
2025-07-29 20:05:49,314 - INFO -   - 验证音频时长: 4.24秒
2025-07-29 20:05:49,314 - INFO - 字幕时间戳信息:
2025-07-29 20:05:49,323 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:05:49,323 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:05:49,323 - INFO -   - 根据生成的音频时长(4.24秒)已调整字幕时间戳
2025-07-29 20:05:49,323 - INFO - ========== 新模式：为字幕 #24 生成4套场景方案 ==========
2025-07-29 20:05:49,323 - INFO - 字幕序号列表: [434, 439]
2025-07-29 20:05:49,323 - INFO - 
--- 生成方案 #1：基于字幕序号 #434 ---
2025-07-29 20:05:49,323 - INFO - 开始为单个字幕序号 #434 匹配场景，目标时长: 4.24秒
2025-07-29 20:05:49,323 - INFO - 开始查找字幕序号 [434] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:49,324 - INFO - 找到related_overlap场景: scene_id=478, 字幕#434
2025-07-29 20:05:49,324 - INFO - 找到related_between场景: scene_id=479, 字幕#434
2025-07-29 20:05:49,325 - INFO - 字幕 #434 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:49,325 - INFO - 字幕序号 #434 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 20:05:49,325 - INFO - 选择第一个overlap场景作为起点: scene_id=478
2025-07-29 20:05:49,325 - INFO - 添加起点场景: scene_id=478, 时长=1.60秒, 累计时长=1.60秒
2025-07-29 20:05:49,325 - INFO - 起点场景时长不足，需要延伸填充 2.64秒
2025-07-29 20:05:49,325 - INFO - 起点场景在原始列表中的索引: 477
2025-07-29 20:05:49,325 - INFO - 延伸添加场景: scene_id=479 (完整时长 0.60秒)
2025-07-29 20:05:49,325 - INFO - 累计时长: 2.20秒
2025-07-29 20:05:49,325 - INFO - 延伸添加场景: scene_id=480 (完整时长 0.60秒)
2025-07-29 20:05:49,325 - INFO - 累计时长: 2.80秒
2025-07-29 20:05:49,325 - INFO - 延伸添加场景: scene_id=481 (完整时长 1.04秒)
2025-07-29 20:05:49,325 - INFO - 累计时长: 3.84秒
2025-07-29 20:05:49,325 - INFO - 延伸添加场景: scene_id=482 (裁剪至 0.40秒)
2025-07-29 20:05:49,325 - INFO - 累计时长: 4.24秒
2025-07-29 20:05:49,325 - INFO - 字幕序号 #434 场景匹配完成，共选择 5 个场景，总时长: 4.24秒
2025-07-29 20:05:49,325 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-07-29 20:05:49,325 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-07-29 20:05:49,325 - INFO - 
--- 生成方案 #2：基于字幕序号 #439 ---
2025-07-29 20:05:49,325 - INFO - 开始为单个字幕序号 #439 匹配场景，目标时长: 4.24秒
2025-07-29 20:05:49,325 - INFO - 开始查找字幕序号 [439] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:49,326 - INFO - 找到related_overlap场景: scene_id=484, 字幕#439
2025-07-29 20:05:49,326 - INFO - 字幕 #439 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:49,326 - INFO - 字幕序号 #439 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:05:49,326 - INFO - 选择第一个overlap场景作为起点: scene_id=484
2025-07-29 20:05:49,326 - INFO - 添加起点场景: scene_id=484, 时长=1.92秒, 累计时长=1.92秒
2025-07-29 20:05:49,326 - INFO - 起点场景时长不足，需要延伸填充 2.32秒
2025-07-29 20:05:49,326 - INFO - 起点场景在原始列表中的索引: 483
2025-07-29 20:05:49,326 - INFO - 延伸添加场景: scene_id=485 (完整时长 1.24秒)
2025-07-29 20:05:49,326 - INFO - 累计时长: 3.16秒
2025-07-29 20:05:49,326 - INFO - 延伸添加场景: scene_id=486 (裁剪至 1.08秒)
2025-07-29 20:05:49,326 - INFO - 累计时长: 4.24秒
2025-07-29 20:05:49,326 - INFO - 字幕序号 #439 场景匹配完成，共选择 3 个场景，总时长: 4.24秒
2025-07-29 20:05:49,326 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 20:05:49,326 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:05:49,326 - INFO - ========== 当前模式：为字幕 #24 生成 1 套场景方案 ==========
2025-07-29 20:05:49,326 - INFO - 开始查找字幕序号 [434, 439] 对应的场景，共有 3816 个场景可选
2025-07-29 20:05:49,326 - INFO - 找到related_overlap场景: scene_id=478, 字幕#434
2025-07-29 20:05:49,326 - INFO - 找到related_overlap场景: scene_id=484, 字幕#439
2025-07-29 20:05:49,327 - INFO - 找到related_between场景: scene_id=479, 字幕#434
2025-07-29 20:05:49,327 - INFO - 字幕 #434 找到 1 个overlap场景, 1 个between场景
2025-07-29 20:05:49,327 - INFO - 字幕 #439 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:05:49,328 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 20:05:49,328 - INFO - 开始生成方案 #1
2025-07-29 20:05:49,328 - INFO - 方案 #1: 为字幕#434选择初始化overlap场景id=478
2025-07-29 20:05:49,328 - INFO - 方案 #1: 为字幕#439选择初始化overlap场景id=484
2025-07-29 20:05:49,328 - INFO - 方案 #1: 初始选择后，当前总时长=3.52秒
2025-07-29 20:05:49,328 - INFO - 方案 #1: 额外between选择后，当前总时长=3.52秒
2025-07-29 20:05:49,328 - INFO - 方案 #1: 额外添加between场景id=479, 当前总时长=4.12秒
2025-07-29 20:05:49,328 - INFO - 方案 #1: 场景总时长(4.12秒)小于音频时长(4.24秒)，需要延伸填充
2025-07-29 20:05:49,328 - INFO - 方案 #1: 最后一个场景ID: 479
2025-07-29 20:05:49,328 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 478
2025-07-29 20:05:49,328 - INFO - 方案 #1: 需要填充时长: 0.12秒
2025-07-29 20:05:49,328 - INFO - 方案 #1: 追加场景 scene_id=480 (裁剪至 0.12秒)
2025-07-29 20:05:49,328 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:05:49,328 - INFO - 方案 #1 调整/填充后最终总时长: 4.24秒
2025-07-29 20:05:49,328 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:05:49,328 - INFO - ========== 当前模式：字幕 #24 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:05:49,328 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:05:49,328 - INFO - ========== 新模式：字幕 #24 共生成 3 套有效场景方案 ==========
2025-07-29 20:05:49,328 - INFO - 
----- 处理字幕 #24 的方案 #1 -----
2025-07-29 20:05:49,328 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 20:05:49,328 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp77a3fmtp
2025-07-29 20:05:49,329 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\478.mp4 (确认存在: True)
2025-07-29 20:05:49,329 - INFO - 添加场景ID=478，时长=1.60秒，累计时长=1.60秒
2025-07-29 20:05:49,330 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\479.mp4 (确认存在: True)
2025-07-29 20:05:49,330 - INFO - 添加场景ID=479，时长=0.60秒，累计时长=2.20秒
2025-07-29 20:05:49,330 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\480.mp4 (确认存在: True)
2025-07-29 20:05:49,330 - INFO - 添加场景ID=480，时长=0.60秒，累计时长=2.80秒
2025-07-29 20:05:49,330 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\481.mp4 (确认存在: True)
2025-07-29 20:05:49,330 - INFO - 添加场景ID=481，时长=1.04秒，累计时长=3.84秒
2025-07-29 20:05:49,330 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\482.mp4 (确认存在: True)
2025-07-29 20:05:49,330 - INFO - 添加场景ID=482，时长=1.40秒，累计时长=5.24秒
2025-07-29 20:05:49,330 - INFO - 准备合并 5 个场景文件，总时长约 5.24秒
2025-07-29 20:05:49,330 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/478.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/479.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/480.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/481.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/482.mp4'

2025-07-29 20:05:49,330 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp77a3fmtp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp77a3fmtp\temp_combined.mp4
2025-07-29 20:05:49,478 - INFO - 合并后的视频时长: 5.36秒，目标音频时长: 4.24秒
2025-07-29 20:05:49,478 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp77a3fmtp\temp_combined.mp4 -ss 0 -to 4.24 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 20:05:49,778 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:49,778 - INFO - 目标音频时长: 4.24秒
2025-07-29 20:05:49,778 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:49,778 - INFO - 时长差异: 0.02秒 (0.55%)
2025-07-29 20:05:49,778 - INFO - ==========================================
2025-07-29 20:05:49,778 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:49,779 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 20:05:49,789 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp77a3fmtp
2025-07-29 20:05:49,834 - INFO - 方案 #1 处理完成:
2025-07-29 20:05:49,834 - INFO -   - 音频时长: 4.24秒
2025-07-29 20:05:49,834 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:49,834 - INFO -   - 时长差异: 0.02秒 (0.55%)
2025-07-29 20:05:49,834 - INFO - 
----- 处理字幕 #24 的方案 #2 -----
2025-07-29 20:05:49,834 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 20:05:49,834 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv8jx910c
2025-07-29 20:05:49,835 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\484.mp4 (确认存在: True)
2025-07-29 20:05:49,835 - INFO - 添加场景ID=484，时长=1.92秒，累计时长=1.92秒
2025-07-29 20:05:49,835 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\485.mp4 (确认存在: True)
2025-07-29 20:05:49,835 - INFO - 添加场景ID=485，时长=1.24秒，累计时长=3.16秒
2025-07-29 20:05:49,835 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\486.mp4 (确认存在: True)
2025-07-29 20:05:49,835 - INFO - 添加场景ID=486，时长=1.84秒，累计时长=5.00秒
2025-07-29 20:05:49,835 - INFO - 准备合并 3 个场景文件，总时长约 5.00秒
2025-07-29 20:05:49,835 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/484.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/485.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/486.mp4'

2025-07-29 20:05:49,835 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv8jx910c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv8jx910c\temp_combined.mp4
2025-07-29 20:05:49,970 - INFO - 合并后的视频时长: 5.07秒，目标音频时长: 4.24秒
2025-07-29 20:05:49,970 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv8jx910c\temp_combined.mp4 -ss 0 -to 4.24 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 20:05:50,268 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:50,268 - INFO - 目标音频时长: 4.24秒
2025-07-29 20:05:50,268 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:50,268 - INFO - 时长差异: 0.02秒 (0.55%)
2025-07-29 20:05:50,268 - INFO - ==========================================
2025-07-29 20:05:50,268 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:50,268 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 20:05:50,269 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv8jx910c
2025-07-29 20:05:50,316 - INFO - 方案 #2 处理完成:
2025-07-29 20:05:50,316 - INFO -   - 音频时长: 4.24秒
2025-07-29 20:05:50,316 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:50,316 - INFO -   - 时长差异: 0.02秒 (0.55%)
2025-07-29 20:05:50,316 - INFO - 
----- 处理字幕 #24 的方案 #3 -----
2025-07-29 20:05:50,316 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 20:05:50,316 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjtzf9dn2
2025-07-29 20:05:50,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\478.mp4 (确认存在: True)
2025-07-29 20:05:50,317 - INFO - 添加场景ID=478，时长=1.60秒，累计时长=1.60秒
2025-07-29 20:05:50,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\484.mp4 (确认存在: True)
2025-07-29 20:05:50,317 - INFO - 添加场景ID=484，时长=1.92秒，累计时长=3.52秒
2025-07-29 20:05:50,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\479.mp4 (确认存在: True)
2025-07-29 20:05:50,317 - INFO - 添加场景ID=479，时长=0.60秒，累计时长=4.12秒
2025-07-29 20:05:50,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\480.mp4 (确认存在: True)
2025-07-29 20:05:50,317 - INFO - 添加场景ID=480，时长=0.60秒，累计时长=4.72秒
2025-07-29 20:05:50,317 - INFO - 准备合并 4 个场景文件，总时长约 4.72秒
2025-07-29 20:05:50,317 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/478.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/484.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/479.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/480.mp4'

2025-07-29 20:05:50,317 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjtzf9dn2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjtzf9dn2\temp_combined.mp4
2025-07-29 20:05:50,456 - INFO - 合并后的视频时长: 4.79秒，目标音频时长: 4.24秒
2025-07-29 20:05:50,456 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjtzf9dn2\temp_combined.mp4 -ss 0 -to 4.24 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 20:05:50,742 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:05:50,742 - INFO - 目标音频时长: 4.24秒
2025-07-29 20:05:50,742 - INFO - 实际视频时长: 4.26秒
2025-07-29 20:05:50,742 - INFO - 时长差异: 0.02秒 (0.55%)
2025-07-29 20:05:50,742 - INFO - ==========================================
2025-07-29 20:05:50,742 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:05:50,742 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 20:05:50,743 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjtzf9dn2
2025-07-29 20:05:50,786 - INFO - 方案 #3 处理完成:
2025-07-29 20:05:50,786 - INFO -   - 音频时长: 4.24秒
2025-07-29 20:05:50,786 - INFO -   - 视频时长: 4.26秒
2025-07-29 20:05:50,786 - INFO -   - 时长差异: 0.02秒 (0.55%)
2025-07-29 20:05:50,786 - INFO - 
字幕 #24 处理完成，成功生成 3/3 套方案
2025-07-29 20:05:50,786 - INFO - 生成的视频文件:
2025-07-29 20:05:50,786 - INFO -   1. F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 20:05:50,786 - INFO -   2. F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 20:05:50,786 - INFO -   3. F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 20:05:50,786 - INFO - ========== 字幕 #24 处理结束 ==========

