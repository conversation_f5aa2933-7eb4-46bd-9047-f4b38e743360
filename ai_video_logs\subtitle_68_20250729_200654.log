2025-07-29 20:06:54,053 - INFO - ========== 字幕 #68 处理开始 ==========
2025-07-29 20:06:54,053 - INFO - 字幕内容: 女人的母亲更是上演了一出川剧变脸，前一秒还骂着废物，后一秒就亲热地喊起了“我的好女婿”。
2025-07-29 20:06:54,053 - INFO - 字幕序号: [2662, 2673]
2025-07-29 20:06:54,053 - INFO - 音频文件详情:
2025-07-29 20:06:54,053 - INFO -   - 路径: output\68.wav
2025-07-29 20:06:54,053 - INFO -   - 时长: 5.57秒
2025-07-29 20:06:54,053 - INFO -   - 验证音频时长: 5.57秒
2025-07-29 20:06:54,053 - INFO - 字幕时间戳信息:
2025-07-29 20:06:54,063 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:54,063 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:54,063 - INFO -   - 根据生成的音频时长(5.57秒)已调整字幕时间戳
2025-07-29 20:06:54,063 - INFO - ========== 新模式：为字幕 #68 生成4套场景方案 ==========
2025-07-29 20:06:54,063 - INFO - 字幕序号列表: [2662, 2673]
2025-07-29 20:06:54,063 - INFO - 
--- 生成方案 #1：基于字幕序号 #2662 ---
2025-07-29 20:06:54,063 - INFO - 开始为单个字幕序号 #2662 匹配场景，目标时长: 5.57秒
2025-07-29 20:06:54,063 - INFO - 开始查找字幕序号 [2662] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:54,064 - INFO - 找到related_overlap场景: scene_id=2493, 字幕#2662
2025-07-29 20:06:54,064 - INFO - 字幕 #2662 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:54,064 - INFO - 字幕序号 #2662 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:54,065 - INFO - 选择第一个overlap场景作为起点: scene_id=2493
2025-07-29 20:06:54,065 - INFO - 添加起点场景: scene_id=2493, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 20:06:54,065 - INFO - 起点场景时长不足，需要延伸填充 3.85秒
2025-07-29 20:06:54,065 - INFO - 起点场景在原始列表中的索引: 2492
2025-07-29 20:06:54,065 - INFO - 延伸添加场景: scene_id=2494 (完整时长 2.12秒)
2025-07-29 20:06:54,065 - INFO - 累计时长: 3.84秒
2025-07-29 20:06:54,065 - INFO - 延伸添加场景: scene_id=2495 (裁剪至 1.73秒)
2025-07-29 20:06:54,065 - INFO - 累计时长: 5.57秒
2025-07-29 20:06:54,065 - INFO - 字幕序号 #2662 场景匹配完成，共选择 3 个场景，总时长: 5.57秒
2025-07-29 20:06:54,065 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 20:06:54,065 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 20:06:54,065 - INFO - 
--- 生成方案 #2：基于字幕序号 #2673 ---
2025-07-29 20:06:54,065 - INFO - 开始为单个字幕序号 #2673 匹配场景，目标时长: 5.57秒
2025-07-29 20:06:54,065 - INFO - 开始查找字幕序号 [2673] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:54,065 - INFO - 找到related_overlap场景: scene_id=2501, 字幕#2673
2025-07-29 20:06:54,066 - INFO - 字幕 #2673 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:54,066 - INFO - 字幕序号 #2673 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:54,066 - INFO - 选择第一个overlap场景作为起点: scene_id=2501
2025-07-29 20:06:54,066 - INFO - 添加起点场景: scene_id=2501, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 20:06:54,066 - INFO - 起点场景时长不足，需要延伸填充 3.85秒
2025-07-29 20:06:54,066 - INFO - 起点场景在原始列表中的索引: 2500
2025-07-29 20:06:54,066 - INFO - 延伸添加场景: scene_id=2502 (完整时长 0.80秒)
2025-07-29 20:06:54,066 - INFO - 累计时长: 2.52秒
2025-07-29 20:06:54,066 - INFO - 延伸添加场景: scene_id=2503 (完整时长 1.16秒)
2025-07-29 20:06:54,066 - INFO - 累计时长: 3.68秒
2025-07-29 20:06:54,066 - INFO - 延伸添加场景: scene_id=2504 (完整时长 1.08秒)
2025-07-29 20:06:54,066 - INFO - 累计时长: 4.76秒
2025-07-29 20:06:54,066 - INFO - 延伸添加场景: scene_id=2505 (裁剪至 0.82秒)
2025-07-29 20:06:54,066 - INFO - 累计时长: 5.57秒
2025-07-29 20:06:54,066 - INFO - 字幕序号 #2673 场景匹配完成，共选择 5 个场景，总时长: 5.57秒
2025-07-29 20:06:54,066 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 20:06:54,066 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:54,066 - INFO - ========== 当前模式：为字幕 #68 生成 1 套场景方案 ==========
2025-07-29 20:06:54,066 - INFO - 开始查找字幕序号 [2662, 2673] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:54,067 - INFO - 找到related_overlap场景: scene_id=2493, 字幕#2662
2025-07-29 20:06:54,067 - INFO - 找到related_overlap场景: scene_id=2501, 字幕#2673
2025-07-29 20:06:54,067 - INFO - 字幕 #2662 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:54,067 - INFO - 字幕 #2673 找到 1 个overlap场景, 0 个between场景
2025-07-29 20:06:54,067 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 20:06:54,068 - INFO - 开始生成方案 #1
2025-07-29 20:06:54,068 - INFO - 方案 #1: 为字幕#2662选择初始化overlap场景id=2493
2025-07-29 20:06:54,068 - INFO - 方案 #1: 为字幕#2673选择初始化overlap场景id=2501
2025-07-29 20:06:54,068 - INFO - 方案 #1: 初始选择后，当前总时长=3.44秒
2025-07-29 20:06:54,068 - INFO - 方案 #1: 额外between选择后，当前总时长=3.44秒
2025-07-29 20:06:54,068 - INFO - 方案 #1: 场景总时长(3.44秒)小于音频时长(5.57秒)，需要延伸填充
2025-07-29 20:06:54,068 - INFO - 方案 #1: 最后一个场景ID: 2501
2025-07-29 20:06:54,068 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2500
2025-07-29 20:06:54,068 - INFO - 方案 #1: 需要填充时长: 2.13秒
2025-07-29 20:06:54,068 - INFO - 方案 #1: 追加场景 scene_id=2502 (完整时长 0.80秒)
2025-07-29 20:06:54,068 - INFO - 方案 #1: 追加场景 scene_id=2503 (完整时长 1.16秒)
2025-07-29 20:06:54,068 - INFO - 方案 #1: 追加场景 scene_id=2504 (裁剪至 0.17秒)
2025-07-29 20:06:54,068 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 20:06:54,068 - INFO - 方案 #1 调整/填充后最终总时长: 5.57秒
2025-07-29 20:06:54,068 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:54,068 - INFO - ========== 当前模式：字幕 #68 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:54,068 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:54,068 - INFO - ========== 新模式：字幕 #68 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:54,068 - INFO - 
----- 处理字幕 #68 的方案 #1 -----
2025-07-29 20:06:54,068 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 20:06:54,068 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp38txjdm7
2025-07-29 20:06:54,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2493.mp4 (确认存在: True)
2025-07-29 20:06:54,068 - INFO - 添加场景ID=2493，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:06:54,069 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2494.mp4 (确认存在: True)
2025-07-29 20:06:54,069 - INFO - 添加场景ID=2494，时长=2.12秒，累计时长=3.84秒
2025-07-29 20:06:54,069 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2495.mp4 (确认存在: True)
2025-07-29 20:06:54,069 - INFO - 添加场景ID=2495，时长=2.04秒，累计时长=5.88秒
2025-07-29 20:06:54,069 - INFO - 准备合并 3 个场景文件，总时长约 5.88秒
2025-07-29 20:06:54,069 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2493.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2494.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2495.mp4'

2025-07-29 20:06:54,069 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp38txjdm7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp38txjdm7\temp_combined.mp4
2025-07-29 20:06:54,209 - INFO - 合并后的视频时长: 5.95秒，目标音频时长: 5.57秒
2025-07-29 20:06:54,209 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp38txjdm7\temp_combined.mp4 -ss 0 -to 5.573 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 20:06:54,541 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:54,542 - INFO - 目标音频时长: 5.57秒
2025-07-29 20:06:54,542 - INFO - 实际视频时长: 5.62秒
2025-07-29 20:06:54,542 - INFO - 时长差异: 0.05秒 (0.90%)
2025-07-29 20:06:54,542 - INFO - ==========================================
2025-07-29 20:06:54,542 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:54,542 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 20:06:54,542 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp38txjdm7
2025-07-29 20:06:54,587 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:54,587 - INFO -   - 音频时长: 5.57秒
2025-07-29 20:06:54,587 - INFO -   - 视频时长: 5.62秒
2025-07-29 20:06:54,587 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-07-29 20:06:54,587 - INFO - 
----- 处理字幕 #68 的方案 #2 -----
2025-07-29 20:06:54,587 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 20:06:54,588 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpb73vo_e8
2025-07-29 20:06:54,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2501.mp4 (确认存在: True)
2025-07-29 20:06:54,589 - INFO - 添加场景ID=2501，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:06:54,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2502.mp4 (确认存在: True)
2025-07-29 20:06:54,589 - INFO - 添加场景ID=2502，时长=0.80秒，累计时长=2.52秒
2025-07-29 20:06:54,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2503.mp4 (确认存在: True)
2025-07-29 20:06:54,589 - INFO - 添加场景ID=2503，时长=1.16秒，累计时长=3.68秒
2025-07-29 20:06:54,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2504.mp4 (确认存在: True)
2025-07-29 20:06:54,589 - INFO - 添加场景ID=2504，时长=1.08秒，累计时长=4.76秒
2025-07-29 20:06:54,589 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2505.mp4 (确认存在: True)
2025-07-29 20:06:54,589 - INFO - 添加场景ID=2505，时长=1.64秒，累计时长=6.40秒
2025-07-29 20:06:54,589 - INFO - 准备合并 5 个场景文件，总时长约 6.40秒
2025-07-29 20:06:54,589 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2501.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2502.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2503.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2504.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2505.mp4'

2025-07-29 20:06:54,589 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpb73vo_e8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpb73vo_e8\temp_combined.mp4
2025-07-29 20:06:54,773 - INFO - 合并后的视频时长: 6.52秒，目标音频时长: 5.57秒
2025-07-29 20:06:54,773 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpb73vo_e8\temp_combined.mp4 -ss 0 -to 5.573 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 20:06:55,163 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:55,163 - INFO - 目标音频时长: 5.57秒
2025-07-29 20:06:55,163 - INFO - 实际视频时长: 5.62秒
2025-07-29 20:06:55,163 - INFO - 时长差异: 0.05秒 (0.90%)
2025-07-29 20:06:55,163 - INFO - ==========================================
2025-07-29 20:06:55,163 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:55,163 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 20:06:55,164 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpb73vo_e8
2025-07-29 20:06:55,210 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:55,210 - INFO -   - 音频时长: 5.57秒
2025-07-29 20:06:55,210 - INFO -   - 视频时长: 5.62秒
2025-07-29 20:06:55,210 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-07-29 20:06:55,210 - INFO - 
----- 处理字幕 #68 的方案 #3 -----
2025-07-29 20:06:55,210 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 20:06:55,210 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpidgjb2ey
2025-07-29 20:06:55,210 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2493.mp4 (确认存在: True)
2025-07-29 20:06:55,210 - INFO - 添加场景ID=2493，时长=1.72秒，累计时长=1.72秒
2025-07-29 20:06:55,211 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2501.mp4 (确认存在: True)
2025-07-29 20:06:55,211 - INFO - 添加场景ID=2501，时长=1.72秒，累计时长=3.44秒
2025-07-29 20:06:55,211 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2502.mp4 (确认存在: True)
2025-07-29 20:06:55,211 - INFO - 添加场景ID=2502，时长=0.80秒，累计时长=4.24秒
2025-07-29 20:06:55,211 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2503.mp4 (确认存在: True)
2025-07-29 20:06:55,211 - INFO - 添加场景ID=2503，时长=1.16秒，累计时长=5.40秒
2025-07-29 20:06:55,211 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2504.mp4 (确认存在: True)
2025-07-29 20:06:55,211 - INFO - 添加场景ID=2504，时长=1.08秒，累计时长=6.48秒
2025-07-29 20:06:55,211 - INFO - 准备合并 5 个场景文件，总时长约 6.48秒
2025-07-29 20:06:55,211 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2493.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2501.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2502.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2503.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2504.mp4'

2025-07-29 20:06:55,211 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpidgjb2ey\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpidgjb2ey\temp_combined.mp4
2025-07-29 20:06:55,371 - INFO - 合并后的视频时长: 6.60秒，目标音频时长: 5.57秒
2025-07-29 20:06:55,371 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpidgjb2ey\temp_combined.mp4 -ss 0 -to 5.573 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 20:06:55,739 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:55,739 - INFO - 目标音频时长: 5.57秒
2025-07-29 20:06:55,739 - INFO - 实际视频时长: 5.62秒
2025-07-29 20:06:55,739 - INFO - 时长差异: 0.05秒 (0.90%)
2025-07-29 20:06:55,739 - INFO - ==========================================
2025-07-29 20:06:55,739 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:55,739 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 20:06:55,740 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpidgjb2ey
2025-07-29 20:06:55,785 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:55,785 - INFO -   - 音频时长: 5.57秒
2025-07-29 20:06:55,785 - INFO -   - 视频时长: 5.62秒
2025-07-29 20:06:55,785 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-07-29 20:06:55,785 - INFO - 
字幕 #68 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:55,786 - INFO - 生成的视频文件:
2025-07-29 20:06:55,786 - INFO -   1. F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 20:06:55,786 - INFO -   2. F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 20:06:55,786 - INFO -   3. F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 20:06:55,786 - INFO - ========== 字幕 #68 处理结束 ==========

