2025-07-29 20:06:06,262 - INFO - ========== 字幕 #35 处理开始 ==========
2025-07-29 20:06:06,263 - INFO - 字幕内容: 另一边，女人终于意识到不对劲，跑到医院寻找妹妹，护士却告诉她，这个床位一直是空的。
2025-07-29 20:06:06,263 - INFO - 字幕序号: [594, 602]
2025-07-29 20:06:06,263 - INFO - 音频文件详情:
2025-07-29 20:06:06,263 - INFO -   - 路径: output\35.wav
2025-07-29 20:06:06,263 - INFO -   - 时长: 6.12秒
2025-07-29 20:06:06,263 - INFO -   - 验证音频时长: 6.12秒
2025-07-29 20:06:06,263 - INFO - 字幕时间戳信息:
2025-07-29 20:06:06,263 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 20:06:06,263 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 20:06:06,263 - INFO -   - 根据生成的音频时长(6.12秒)已调整字幕时间戳
2025-07-29 20:06:06,263 - INFO - ========== 新模式：为字幕 #35 生成4套场景方案 ==========
2025-07-29 20:06:06,263 - INFO - 字幕序号列表: [594, 602]
2025-07-29 20:06:06,263 - INFO - 
--- 生成方案 #1：基于字幕序号 #594 ---
2025-07-29 20:06:06,263 - INFO - 开始为单个字幕序号 #594 匹配场景，目标时长: 6.12秒
2025-07-29 20:06:06,263 - INFO - 开始查找字幕序号 [594] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:06,263 - INFO - 找到related_overlap场景: scene_id=636, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=637, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=638, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=639, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=640, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=641, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=642, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=643, 字幕#594
2025-07-29 20:06:06,264 - INFO - 找到related_between场景: scene_id=644, 字幕#594
2025-07-29 20:06:06,265 - INFO - 字幕 #594 找到 1 个overlap场景, 8 个between场景
2025-07-29 20:06:06,265 - INFO - 字幕序号 #594 找到 1 个可用overlap场景, 8 个可用between场景
2025-07-29 20:06:06,265 - INFO - 选择第一个overlap场景作为起点: scene_id=636
2025-07-29 20:06:06,265 - INFO - 添加起点场景: scene_id=636, 时长=1.68秒, 累计时长=1.68秒
2025-07-29 20:06:06,265 - INFO - 起点场景时长不足，需要延伸填充 4.44秒
2025-07-29 20:06:06,265 - INFO - 起点场景在原始列表中的索引: 635
2025-07-29 20:06:06,265 - INFO - 延伸添加场景: scene_id=637 (完整时长 2.76秒)
2025-07-29 20:06:06,265 - INFO - 累计时长: 4.44秒
2025-07-29 20:06:06,265 - INFO - 延伸添加场景: scene_id=638 (完整时长 1.08秒)
2025-07-29 20:06:06,265 - INFO - 累计时长: 5.52秒
2025-07-29 20:06:06,265 - INFO - 延伸添加场景: scene_id=639 (裁剪至 0.60秒)
2025-07-29 20:06:06,265 - INFO - 累计时长: 6.12秒
2025-07-29 20:06:06,265 - INFO - 字幕序号 #594 场景匹配完成，共选择 4 个场景，总时长: 6.12秒
2025-07-29 20:06:06,265 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 20:06:06,265 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 20:06:06,265 - INFO - 
--- 生成方案 #2：基于字幕序号 #602 ---
2025-07-29 20:06:06,265 - INFO - 开始为单个字幕序号 #602 匹配场景，目标时长: 6.12秒
2025-07-29 20:06:06,265 - INFO - 开始查找字幕序号 [602] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:06,265 - INFO - 找到related_overlap场景: scene_id=653, 字幕#602
2025-07-29 20:06:06,265 - INFO - 找到related_overlap场景: scene_id=654, 字幕#602
2025-07-29 20:06:06,266 - INFO - 字幕 #602 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:06,266 - INFO - 字幕序号 #602 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 20:06:06,266 - INFO - 选择第一个overlap场景作为起点: scene_id=653
2025-07-29 20:06:06,266 - INFO - 添加起点场景: scene_id=653, 时长=0.92秒, 累计时长=0.92秒
2025-07-29 20:06:06,266 - INFO - 起点场景时长不足，需要延伸填充 5.20秒
2025-07-29 20:06:06,266 - INFO - 起点场景在原始列表中的索引: 652
2025-07-29 20:06:06,266 - INFO - 延伸添加场景: scene_id=654 (完整时长 2.04秒)
2025-07-29 20:06:06,266 - INFO - 累计时长: 2.96秒
2025-07-29 20:06:06,267 - INFO - 延伸添加场景: scene_id=655 (完整时长 1.44秒)
2025-07-29 20:06:06,267 - INFO - 累计时长: 4.40秒
2025-07-29 20:06:06,267 - INFO - 延伸添加场景: scene_id=656 (裁剪至 1.72秒)
2025-07-29 20:06:06,267 - INFO - 累计时长: 6.12秒
2025-07-29 20:06:06,267 - INFO - 字幕序号 #602 场景匹配完成，共选择 4 个场景，总时长: 6.12秒
2025-07-29 20:06:06,267 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 20:06:06,267 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 20:06:06,267 - INFO - ========== 当前模式：为字幕 #35 生成 1 套场景方案 ==========
2025-07-29 20:06:06,267 - INFO - 开始查找字幕序号 [594, 602] 对应的场景，共有 3816 个场景可选
2025-07-29 20:06:06,267 - INFO - 找到related_overlap场景: scene_id=636, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_overlap场景: scene_id=653, 字幕#602
2025-07-29 20:06:06,267 - INFO - 找到related_overlap场景: scene_id=654, 字幕#602
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=637, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=638, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=639, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=640, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=641, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=642, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=643, 字幕#594
2025-07-29 20:06:06,267 - INFO - 找到related_between场景: scene_id=644, 字幕#594
2025-07-29 20:06:06,268 - INFO - 字幕 #594 找到 1 个overlap场景, 8 个between场景
2025-07-29 20:06:06,268 - INFO - 字幕 #602 找到 2 个overlap场景, 0 个between场景
2025-07-29 20:06:06,268 - INFO - 共收集 3 个未使用的overlap场景和 8 个未使用的between场景
2025-07-29 20:06:06,268 - INFO - 开始生成方案 #1
2025-07-29 20:06:06,268 - INFO - 方案 #1: 为字幕#594选择初始化overlap场景id=636
2025-07-29 20:06:06,268 - INFO - 方案 #1: 为字幕#602选择初始化overlap场景id=653
2025-07-29 20:06:06,268 - INFO - 方案 #1: 初始选择后，当前总时长=2.60秒
2025-07-29 20:06:06,268 - INFO - 方案 #1: 额外添加overlap场景id=654, 当前总时长=4.64秒
2025-07-29 20:06:06,268 - INFO - 方案 #1: 额外between选择后，当前总时长=4.64秒
2025-07-29 20:06:06,268 - INFO - 方案 #1: 额外添加between场景id=637, 当前总时长=7.40秒
2025-07-29 20:06:06,268 - INFO - 方案 #1: 场景总时长(7.40秒)大于音频时长(6.12秒)，需要裁剪
2025-07-29 20:06:06,268 - INFO - 调整前总时长: 7.40秒, 目标时长: 6.12秒
2025-07-29 20:06:06,268 - INFO - 需要裁剪 1.28秒
2025-07-29 20:06:06,268 - INFO - 裁剪最长场景ID=637：从2.76秒裁剪至1.48秒
2025-07-29 20:06:06,268 - INFO - 调整后总时长: 6.12秒，与目标时长差异: 0.00秒
2025-07-29 20:06:06,268 - INFO - 方案 #1 调整/填充后最终总时长: 6.12秒
2025-07-29 20:06:06,268 - INFO - 方案 #1 添加到方案列表
2025-07-29 20:06:06,268 - INFO - ========== 当前模式：字幕 #35 的 1 套有效场景方案生成完成 ==========
2025-07-29 20:06:06,268 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 20:06:06,268 - INFO - ========== 新模式：字幕 #35 共生成 3 套有效场景方案 ==========
2025-07-29 20:06:06,268 - INFO - 
----- 处理字幕 #35 的方案 #1 -----
2025-07-29 20:06:06,268 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 20:06:06,269 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp4aft3k8
2025-07-29 20:06:06,270 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\636.mp4 (确认存在: True)
2025-07-29 20:06:06,270 - INFO - 添加场景ID=636，时长=1.68秒，累计时长=1.68秒
2025-07-29 20:06:06,270 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\637.mp4 (确认存在: True)
2025-07-29 20:06:06,270 - INFO - 添加场景ID=637，时长=2.76秒，累计时长=4.44秒
2025-07-29 20:06:06,270 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\638.mp4 (确认存在: True)
2025-07-29 20:06:06,270 - INFO - 添加场景ID=638，时长=1.08秒，累计时长=5.52秒
2025-07-29 20:06:06,270 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\639.mp4 (确认存在: True)
2025-07-29 20:06:06,270 - INFO - 添加场景ID=639，时长=1.64秒，累计时长=7.16秒
2025-07-29 20:06:06,270 - INFO - 准备合并 4 个场景文件，总时长约 7.16秒
2025-07-29 20:06:06,270 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/636.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/637.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/638.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/639.mp4'

2025-07-29 20:06:06,270 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp4aft3k8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp4aft3k8\temp_combined.mp4
2025-07-29 20:06:06,411 - INFO - 合并后的视频时长: 7.25秒，目标音频时长: 6.12秒
2025-07-29 20:06:06,411 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp4aft3k8\temp_combined.mp4 -ss 0 -to 6.116 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 20:06:06,764 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:06,764 - INFO - 目标音频时长: 6.12秒
2025-07-29 20:06:06,764 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:06:06,764 - INFO - 时长差异: 0.03秒 (0.44%)
2025-07-29 20:06:06,764 - INFO - ==========================================
2025-07-29 20:06:06,764 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:06,764 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 20:06:06,765 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp4aft3k8
2025-07-29 20:06:06,810 - INFO - 方案 #1 处理完成:
2025-07-29 20:06:06,810 - INFO -   - 音频时长: 6.12秒
2025-07-29 20:06:06,810 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:06:06,810 - INFO -   - 时长差异: 0.03秒 (0.44%)
2025-07-29 20:06:06,810 - INFO - 
----- 处理字幕 #35 的方案 #2 -----
2025-07-29 20:06:06,810 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 20:06:06,811 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8e1trbye
2025-07-29 20:06:06,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\653.mp4 (确认存在: True)
2025-07-29 20:06:06,811 - INFO - 添加场景ID=653，时长=0.92秒，累计时长=0.92秒
2025-07-29 20:06:06,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\654.mp4 (确认存在: True)
2025-07-29 20:06:06,811 - INFO - 添加场景ID=654，时长=2.04秒，累计时长=2.96秒
2025-07-29 20:06:06,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\655.mp4 (确认存在: True)
2025-07-29 20:06:06,811 - INFO - 添加场景ID=655，时长=1.44秒，累计时长=4.40秒
2025-07-29 20:06:06,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\656.mp4 (确认存在: True)
2025-07-29 20:06:06,811 - INFO - 添加场景ID=656，时长=5.88秒，累计时长=10.28秒
2025-07-29 20:06:06,811 - INFO - 场景总时长(10.28秒)已达到音频时长(6.12秒)的1.5倍，停止添加场景
2025-07-29 20:06:06,811 - INFO - 准备合并 4 个场景文件，总时长约 10.28秒
2025-07-29 20:06:06,812 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/653.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/654.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/655.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/656.mp4'

2025-07-29 20:06:06,812 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8e1trbye\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8e1trbye\temp_combined.mp4
2025-07-29 20:06:06,976 - INFO - 合并后的视频时长: 10.37秒，目标音频时长: 6.12秒
2025-07-29 20:06:06,976 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8e1trbye\temp_combined.mp4 -ss 0 -to 6.116 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 20:06:07,331 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:07,331 - INFO - 目标音频时长: 6.12秒
2025-07-29 20:06:07,331 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:06:07,332 - INFO - 时长差异: 0.03秒 (0.44%)
2025-07-29 20:06:07,332 - INFO - ==========================================
2025-07-29 20:06:07,332 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:07,332 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 20:06:07,332 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8e1trbye
2025-07-29 20:06:07,378 - INFO - 方案 #2 处理完成:
2025-07-29 20:06:07,378 - INFO -   - 音频时长: 6.12秒
2025-07-29 20:06:07,378 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:06:07,378 - INFO -   - 时长差异: 0.03秒 (0.44%)
2025-07-29 20:06:07,378 - INFO - 
----- 处理字幕 #35 的方案 #3 -----
2025-07-29 20:06:07,378 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 20:06:07,378 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps0s2paa_
2025-07-29 20:06:07,379 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\636.mp4 (确认存在: True)
2025-07-29 20:06:07,379 - INFO - 添加场景ID=636，时长=1.68秒，累计时长=1.68秒
2025-07-29 20:06:07,379 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\653.mp4 (确认存在: True)
2025-07-29 20:06:07,379 - INFO - 添加场景ID=653，时长=0.92秒，累计时长=2.60秒
2025-07-29 20:06:07,379 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\654.mp4 (确认存在: True)
2025-07-29 20:06:07,379 - INFO - 添加场景ID=654，时长=2.04秒，累计时长=4.64秒
2025-07-29 20:06:07,379 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\637.mp4 (确认存在: True)
2025-07-29 20:06:07,379 - INFO - 添加场景ID=637，时长=2.76秒，累计时长=7.40秒
2025-07-29 20:06:07,379 - INFO - 准备合并 4 个场景文件，总时长约 7.40秒
2025-07-29 20:06:07,379 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/636.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/653.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/654.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/637.mp4'

2025-07-29 20:06:07,379 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps0s2paa_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps0s2paa_\temp_combined.mp4
2025-07-29 20:06:07,538 - INFO - 合并后的视频时长: 7.49秒，目标音频时长: 6.12秒
2025-07-29 20:06:07,539 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps0s2paa_\temp_combined.mp4 -ss 0 -to 6.116 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 20:06:07,905 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 20:06:07,905 - INFO - 目标音频时长: 6.12秒
2025-07-29 20:06:07,905 - INFO - 实际视频时长: 6.14秒
2025-07-29 20:06:07,905 - INFO - 时长差异: 0.03秒 (0.44%)
2025-07-29 20:06:07,905 - INFO - ==========================================
2025-07-29 20:06:07,905 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 20:06:07,905 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 20:06:07,906 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps0s2paa_
2025-07-29 20:06:07,952 - INFO - 方案 #3 处理完成:
2025-07-29 20:06:07,952 - INFO -   - 音频时长: 6.12秒
2025-07-29 20:06:07,952 - INFO -   - 视频时长: 6.14秒
2025-07-29 20:06:07,952 - INFO -   - 时长差异: 0.03秒 (0.44%)
2025-07-29 20:06:07,953 - INFO - 
字幕 #35 处理完成，成功生成 3/3 套方案
2025-07-29 20:06:07,953 - INFO - 生成的视频文件:
2025-07-29 20:06:07,953 - INFO -   1. F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 20:06:07,953 - INFO -   2. F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 20:06:07,953 - INFO -   3. F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 20:06:07,953 - INFO - ========== 字幕 #35 处理结束 ==========

